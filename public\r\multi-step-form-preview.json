{"name": "multi-step-form-preview", "type": "registry:block", "dependencies": [], "registryDependencies": ["https://blocks.mvp-subha.me/r/multi-step-form.json", "https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/input.json", "https://blocks.mvp-subha.me/r/label.json", "https://blocks.mvp-subha.me/r/progress.json"], "files": [{"type": "registry:block", "content": "import MultiStepForm from '@/components/ui/multi-step-form';\n\nexport default function MultiStepFormPreview() {\n  return (\n    <div className=\"mx-auto w-full max-w-lg\">\n      <MultiStepForm\n        onSubmit={(data) => {\n          console.log('Form submitted:', data);\n        }}\n      />\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/forms/multi-step-form-preview.tsx", "target": "components/mvpblocks/multi-step-form-preview.tsx"}]}