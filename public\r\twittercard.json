{"name": "twittercard", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { VerifiedIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport Link from 'next/link';\n\ninterface ReplyProps {\n  authorName: string;\n  authorHandle: string;\n  authorImage: string;\n  content: string;\n  isVerified?: boolean;\n  timestamp: string;\n}\n\ninterface TweetCardProps {\n  authorName: string;\n  authorHandle: string;\n  authorImage: string;\n  content: string[];\n  isVerified?: boolean;\n  timestamp: string;\n  reply?: ReplyProps;\n}\n\nexport default function TweetCard({\n  authorName = 'Subhadeep',\n  authorHandle = 'mvp_Subha',\n  authorImage = 'https://pbs.twimg.com/profile_images/1763223695898681344/2mvSadJl_400x400.jpg',\n  content = [\n    'Mvpblocks is the best ever UI component collection library 🎉',\n    '1. Can be opened in v0',\n    '2. Can be installed with CLI',\n    '3. Deploy to your app',\n  ],\n  isVerified = true,\n  timestamp = 'Mar 3, 2025',\n  reply = {\n    authorName: 'shadcn',\n    authorHandle: 'shadcn',\n    authorImage:\n      'https://pbs.twimg.com/profile_images/1593304942210478080/TUYae5z7_400x400.jpg',\n    content: 'Awesome.',\n    isVerified: true,\n    timestamp: 'March 3',\n  },\n}: TweetCardProps) {\n  return (\n    <Link href=\"https://x.com/mvp_Subha\" target=\"_blank\">\n      <div\n        className={cn(\n          'relative isolate w-full max-w-xl min-w-[400px] overflow-hidden rounded-2xl p-1.5 md:min-w-[500px]',\n          'bg-white/5 dark:bg-black/90',\n          'bg-linear-to-br from-black/5 to-black/[0.02] dark:from-white/5 dark:to-white/[0.02]',\n          'backdrop-blur-xl backdrop-saturate-[180%]',\n          'border border-black/10 dark:border-white/10',\n          'shadow-[0_8px_16px_rgb(0_0_0_/_0.15)] dark:shadow-[0_8px_16px_rgb(0_0_0_/_0.25)]',\n          'translate-z-0 will-change-transform',\n        )}\n      >\n        <div\n          className={cn(\n            'relative w-full rounded-xl p-5',\n            'bg-linear-to-br from-black/[0.05] to-transparent dark:from-white/[0.08] dark:to-transparent',\n            'backdrop-blur-md backdrop-saturate-150',\n            'border border-black/[0.05] dark:border-white/[0.08]',\n            'text-black/90 dark:text-white',\n            'shadow-xs',\n            'translate-z-0 will-change-transform',\n            'before:pointer-events-none before:absolute before:inset-0 before:bg-linear-to-br before:from-black/[0.02] before:to-black/[0.01] before:opacity-0 before:transition-opacity dark:before:from-white/[0.03] dark:before:to-white/[0.01]',\n            'hover:before:opacity-100',\n          )}\n        >\n          <div className=\"flex gap-3\">\n            <div className=\"shrink-0\">\n              <div className=\"h-10 w-10 overflow-hidden rounded-full\">\n                <img\n                  src={authorImage}\n                  alt={authorName}\n                  className=\"h-full w-full object-cover\"\n                />\n              </div>\n            </div>\n\n            <div className=\"flex-1\">\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex flex-col\">\n                  <div className=\"flex items-center gap-1\">\n                    <span className=\"cursor-pointer font-semibold text-black hover:underline dark:text-white/90\">\n                      {authorName}\n                    </span>\n                    {isVerified && (\n                      <VerifiedIcon className=\"h-4 w-4 text-blue-400\" />\n                    )}\n                  </div>\n                  <span className=\"text-sm text-black dark:text-white/60\">\n                    @{authorHandle}\n                  </span>\n                </div>\n                <button\n                  type=\"button\"\n                  className=\"flex h-8 w-8 items-center justify-center rounded-lg p-1 text-black hover:bg-black/5 hover:text-black dark:text-white/80 dark:hover:bg-white/5 dark:hover:text-white\"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    width=\"1200\"\n                    height=\"1227\"\n                    fill=\"none\"\n                    viewBox=\"0 0 1200 1227\"\n                    className=\"h-4 w-4\"\n                  >\n                    <title>X</title>\n                    <path\n                      fill=\"currentColor\"\n                      d=\"M714.163 519.284 1160.89 0h-105.86L667.137 450.887 357.328 0H0l468.492 681.821L0 1226.37h105.866l409.625-476.152 327.181 476.152H1200L714.137 519.284h.026ZM569.165 687.828l-47.468-67.894-377.686-540.24h162.604l304.797 435.991 47.468 67.894 396.2 566.721H892.476L569.165 687.854v-.026Z\"\n                    />\n                  </svg>\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"mt-2\">\n            {content.map((item, index) => (\n              <p\n                key={index}\n                className=\"text-base text-black dark:text-white/90\"\n              >\n                {item}\n              </p>\n            ))}\n            <span className=\"mt-2 block text-sm text-black dark:text-white/50\">\n              {timestamp}\n            </span>\n          </div>\n\n          {reply && (\n            <div className=\"mt-4 border-t border-black/[0.08] pt-4 dark:border-white/[0.08]\">\n              <div className=\"flex gap-3\">\n                <div className=\"shrink-0\">\n                  <div className=\"h-10 w-10 overflow-hidden rounded-full\">\n                    <img\n                      src={reply.authorImage}\n                      alt={reply.authorName}\n                      className=\"h-full w-full object-cover\"\n                    />\n                  </div>\n                </div>\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center gap-1\">\n                    <span className=\"cursor-pointer font-semibold text-black hover:underline dark:text-white/90\">\n                      {reply.authorName}\n                    </span>\n                    {reply.isVerified && (\n                      <VerifiedIcon className=\"h-4 w-4 text-blue-400\" />\n                    )}\n                    <span className=\"text-sm text-black dark:text-white/60\">\n                      @{reply.authorHandle}\n                    </span>\n                    <span className=\"text-sm text-black dark:text-white/60\">\n                      ·\n                    </span>\n                    <span className=\"text-sm text-black dark:text-white/60\">\n                      {reply.timestamp}\n                    </span>\n                  </div>\n                  <p className=\"mt-1 text-sm text-black dark:text-white/80\">\n                    {reply.content}\n                  </p>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </Link>\n  );\n}\n", "path": "/components/mvpblocks/cards/twitter/twittercard.tsx", "target": "components/mvpblocks/twittercard.tsx"}]}