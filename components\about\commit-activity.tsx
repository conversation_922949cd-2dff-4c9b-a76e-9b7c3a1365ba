'use client';

import { useMemo, useState } from 'react';
import {
  Line<PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import { format, isAfter, parseISO, subDays, subMonths } from 'date-fns';
import { githubData } from '@/constants/github-data';

import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from '@/components/ui/card';
import { ChartContainer } from '@/components/ui/chart';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

type UserStats = Record<string, number>;

function prepareChartData(stats: UserStats, filter: 'all' | 'month' | 'week') {
  const now = new Date();
  const entries = Object.entries(stats || {})
    .map(([date, count]) => ({ date: parseISO(date), count }))
    .filter(({ date }) => {
      if (filter === 'month') return isAfter(date, subMonths(now, 1));
      if (filter === 'week') return isAfter(date, subDays(now, 7));
      return true;
    })
    .sort((a, b) => a.date.getTime() - b.date.getTime());

  return entries.map(({ date, count }) => ({
    date: format(date, 'MMM-dd'),
    commits: count,
  }));
}

const chartConfig = {
  commits: {
    label: 'Commits',
    color: 'hsl(var(--chart-1))',
  },
};

export default function CommitActivity() {
  const [filter, setFilter] = useState<'all' | 'month' | 'week'>('all');

  const chartData = useMemo(
    () => prepareChartData(githubData.userStats, filter),
    [filter],
  );

  return (
    <Card className="from-secondary/40 to-secondary/0 bg-gradient-to-b shadow-[0px_-2px_50px_0px_#e91e631c_inset]">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>GitHub Commits</CardTitle>
            <CardDescription>Commits over time</CardDescription>
          </div>
          <Select value={filter} onValueChange={(val) => setFilter(val as any)}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All time</SelectItem>
              <SelectItem value="month">Last month</SelectItem>
              <SelectItem value="week">Last week</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent className="px-0">
        <ChartContainer config={chartConfig}>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart
              data={chartData}
              margin={{ top: 16, right: 16, bottom: 24, left: 16 }}
            >
              <CartesianGrid
                strokeDasharray="4"
                horizontal={true}
                vertical={false}
                strokeOpacity={0.9}
                stroke="#ffffff10"
              />
              <XAxis
                dataKey="date"
                tickLine={false}
                axisLine={false}
                tickMargin={12}
                interval="preserveStartEnd"
              />
              <YAxis
                tickLine={false}
                axisLine={false}
                allowDecimals={false}
                tickMargin={8}
              />
              <Tooltip
                cursor={false}
                content={({ payload }) => {
                  if (payload && payload.length) {
                    const { date, commits } = payload[0].payload;
                    return (
                      <div className="bg-background rounded-md border px-3 py-2 text-sm shadow-md">
                        <div className="font-medium">{date}</div>
                        <div className="text-muted-foreground">
                          {commits} commits
                        </div>
                      </div>
                    );
                  }
                  return null;
                }}
              />
              <Line
                dataKey="commits"
                type="monotone"
                stroke="hsl(var(--chart-1))"
                strokeWidth={2}
                dot={{ fill: 'hsl(var(--chart-2))' }}
                activeDot={{ r: 6 }}
                isAnimationActive={true}
              />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}