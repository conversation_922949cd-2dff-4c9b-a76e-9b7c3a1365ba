{"name": "revenue-chart", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { memo } from 'react';\nimport { motion } from 'framer-motion';\nimport { Button } from '@/components/ui/button';\nimport { BarChart3, Calendar } from 'lucide-react';\n\nconst chartData = [\n  { month: 'Jan', value: 4000, growth: 12, color: 'bg-blue-500' },\n  { month: 'Feb', value: 3000, growth: -8, color: 'bg-red-500' },\n  { month: 'Mar', value: 5000, growth: 25, color: 'bg-green-500' },\n  { month: 'Apr', value: 4500, growth: 15, color: 'bg-yellow-500' },\n  { month: 'May', value: 6000, growth: 33, color: 'bg-purple-500' },\n  { month: 'Jun', value: 5500, growth: 22, color: 'bg-cyan-500' },\n];\n\nexport const RevenueChart = memo(() => {\n  return (\n    <div className=\"border-border bg-card/40 rounded-xl border p-6\">\n      <div className=\"mb-6 flex items-center justify-between\">\n        <div>\n          <h3 className=\"flex items-center gap-2 text-lg font-semibold\">\n            <BarChart3 className=\"h-5 w-5 text-green-500\" />\n            Revenue Analytics\n          </h3>\n          <p className=\"text-muted-foreground text-sm\">\n            Monthly revenue performance\n          </p>\n        </div>\n        <Button variant=\"outline\" size=\"sm\">\n          <Calendar className=\"mr-2 h-4 w-4\" />\n          Last 6 months\n        </Button>\n      </div>\n\n      {/* Fixed Chart Area */}\n      <div className=\"relative mb-4 h-64 rounded-lg p-4\">\n        <div className=\"flex h-full items-end justify-between gap-3\">\n          {chartData.map((item, index) => (\n            <div\n              key={item.month}\n              className=\"group flex flex-1 flex-col items-center\"\n            >\n              <motion.div\n                initial={{ height: 0 }}\n                animate={{ height: `${(item.value / 6000) * 180}px` }}\n                transition={{ duration: 1, delay: index * 0.1 }}\n                className={`w-full ${item.color} relative min-h-[20px] cursor-pointer rounded-t-lg transition-opacity hover:opacity-80`}\n              >\n                {/* Tooltip */}\n                <div className=\"border-border bg-popover absolute -top-16 left-1/2 z-10 -translate-x-1/2 transform rounded-lg border px-3 py-2 text-sm whitespace-nowrap opacity-0 shadow-lg transition-opacity group-hover:opacity-100\">\n                  <div className=\"font-medium\">\n                    ${item.value.toLocaleString()}\n                  </div>\n                  <div\n                    className={`text-xs ${item.growth > 0 ? 'text-green-500' : 'text-red-500'}`}\n                  >\n                    {item.growth > 0 ? '+' : ''}\n                    {item.growth}%\n                  </div>\n                </div>\n              </motion.div>\n              <div className=\"text-muted-foreground mt-2 text-center text-xs font-medium\">\n                {item.month}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Summary Stats */}\n      <div className=\"border-border/50 grid grid-cols-3 gap-4 border-t pt-4\">\n        <div className=\"text-center\">\n          <div className=\"text-2xl font-bold text-green-500\">$27K</div>\n          <div className=\"text-muted-foreground text-xs\">Total Revenue</div>\n        </div>\n        <div className=\"text-center\">\n          <div className=\"text-2xl font-bold text-blue-500\">+18%</div>\n          <div className=\"text-muted-foreground text-xs\">Growth Rate</div>\n        </div>\n        <div className=\"text-center\">\n          <div className=\"text-2xl font-bold text-purple-500\">$4.5K</div>\n          <div className=\"text-muted-foreground text-xs\">Average</div>\n        </div>\n      </div>\n    </div>\n  );\n});\n\nRevenueChart.displayName = 'RevenueChart';\n", "path": "/components/mvpblocks/dashboards/admin-dashboard-1/ui/revenue-chart.tsx", "target": "components/mvpblocks/ui/revenue-chart.tsx"}]}