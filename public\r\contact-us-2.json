{"name": "contact-us-2", "type": "registry:block", "author": "ParnaRoyChowdhury777", "dependencies": ["lucide-react", "react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\nimport React from 'react';\nimport { Mail } from 'lucide-react';\nimport { Phone } from 'lucide-react';\nimport { MapPin } from 'lucide-react';\nimport { Github } from 'lucide-react';\nimport { Twitter } from 'lucide-react';\nimport { Facebook } from 'lucide-react';\nimport { Instagram } from 'lucide-react';\nimport { Send } from 'lucide-react';\nimport Link from 'next/link';\n\nexport default function ContactUs2() {\n  const [state, setState] = React.useState({\n    name: '',\n    email: '',\n    message: '',\n    errors: {} as Record<string, string>,\n    submitting: false,\n    submitted: false,\n  });\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setState({ ...state, submitting: true });\n\n    // Console log only\n    console.log('Form submitted:', {\n      name: state.name,\n      email: state.email,\n      message: state.message,\n    });\n\n    setState({\n      ...state,\n      submitting: false,\n      submitted: true,\n    });\n  };\n\n  return (\n    <section className=\"w-full max-w-screen-md px-2\">\n      <h2 className=\"mt-4 mb-5 bg-gradient-to-br from-gray-300 via-blue-300 to-gray-700 bg-clip-text text-center text-4xl font-bold text-transparent md:text-6xl\">\n        Let&apos;s Get in Touch\n      </h2>\n      <p className=\"text-muted-foreground mb-6 text-center\">\n        Fill out the form below and we&apos;ll get back to you as soon as\n        possible.\n      </p>\n      <div\n        className=\"bg-opacity-10 mx-auto mb-6 grid w-full items-start gap-12 rounded-lg border bg-white px-4 pt-10 pb-6 shadow shadow-slate-800 md:grid-cols-2 lg:px-12\"\n        style={{\n          backgroundImage:\n            'radial-gradient(164.75% 100% at 50% 0,#272f3c 0,#0b1224 48.73%)',\n        }}\n      >\n        <form className=\"space-y-8 text-slate-300\" onSubmit={handleSubmit}>\n          <div className=\"space-y-4 text-lg\">\n            <label htmlFor=\"name\" />\n            Name\n            <input\n              id=\"name\"\n              type=\"text\"\n              required\n              className=\"bg-background flex h-10 w-full rounded-md border border-slate-700 bg-slate-950 px-3 py-2 text-sm shadow-inner shadow-slate-800 outline-none hover:border-slate-600 hover:transition-all hover:outline-none focus:border-slate-500 focus:outline-none\"\n              placeholder=\"Enter your name\"\n              name=\"name\"\n            />\n          </div>\n\n          <div className=\"space-y-4 text-lg\">\n            <label htmlFor=\"email\" /> Email\n            <input\n              id=\"email\"\n              placeholder=\"Enter your email\"\n              type=\"email\"\n              className=\"hover:transition-al bg-background placeholder:text-muted-foreground flex h-10 w-full rounded-md border border-slate-700 bg-slate-950 px-3 py-2 text-sm shadow-inner shadow-slate-800 outline-none file:text-sm file:font-medium hover:border-slate-400 hover:outline-none focus:border-slate-500 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50\"\n              name=\"email\"\n              required\n            />\n            {state.errors && state.errors.email && (\n              <p className=\"mt-1 text-sm text-red-500\">{state.errors.email}</p>\n            )}\n          </div>\n          <div className=\"space-y-4 text-lg\">\n            <label htmlFor=\"message\" className=\"text-lg\" />\n            Message\n            <textarea\n              className=\"bg-background ring-offset-background placeholder:text-muted-foreground mb-5 flex min-h-[100px] w-full rounded-md border border-slate-700 bg-slate-950 px-3 py-2 text-sm text-white shadow-inner shadow-slate-800 outline-none hover:border-slate-400 hover:transition-all hover:outline-none focus:border-slate-500 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50\"\n              id=\"message\"\n              placeholder=\"Enter your message\"\n              name=\"message\"\n            />\n            {state.errors && (state.errors as any).message && (\n              <p className=\"mt-1 text-sm text-red-500\">\n                {(state.errors as any).message}\n              </p>\n            )}\n          </div>\n          <button\n            className=\"group/btn relative block h-10 w-full rounded-md bg-gradient-to-br from-slate-800 to-slate-700 py-2 text-center font-medium text-white shadow-[0px_1px_0px_0px_var(--zinc-800)_inset,0px_-1px_0px_0px_var(--zinc-800)_inset] transition-all duration-300 ease-in-out hover:from-slate-700 hover:to-slate-800 hover:shadow-[0px_1px_0px_0px_var(--zinc-800)_inset,0px_-1px_0px_0px_var(--zinc-800)_inset]\"\n            type=\"submit\"\n            disabled={state.submitting}\n          >\n            {state.submitting ? 'Sending...' : 'Send'}\n            <Send className=\"mx-2 inline h-4\" />\n          </button>\n        </form>\n        <div>\n          <h3 className=\"mb-10 text-2xl font-semibold text-slate-300\">\n            Connect with Us\n          </h3>\n          <div className=\"mb-12 flex gap-8\">\n            <Link\n              className=\"flex h-10 w-10 items-center justify-center rounded-full border border-slate-600 shadow-inner shadow-gray-800 hover:shadow-md hover:shadow-slate-500 hover:transition hover:duration-300 hover:ease-in-out\"\n              href=\"#\"\n            >\n              <Mail className=\"h-5 w-5 text-white\" />\n            </Link>\n            <div className=\"text-md text-slate-300\">\n              <p>Email to us at </p>\n              <p><EMAIL></p>\n            </div>\n          </div>\n\n          <div className=\"mb-12 flex gap-8\">\n            <Link\n              className=\"flex h-10 w-10 items-center justify-center rounded-full border border-slate-600 shadow-inner shadow-gray-800 hover:shadow-md hover:shadow-slate-500 hover:transition hover:duration-300 hover:ease-in-out\"\n              href=\"#\"\n            >\n              <Phone className=\"h-5 w-5 text-white\" />\n            </Link>\n            <div className=\"text-md text-slate-300\">\n              <p>Call us at </p>\n              <p>XXXXX XXXXX</p>\n            </div>\n          </div>\n\n          <div className=\"mb-12 flex gap-8\">\n            <Link\n              className=\"flex h-10 w-10 items-center justify-center rounded-full border border-slate-600 px-2 shadow-inner shadow-gray-800 hover:shadow-md hover:shadow-slate-500 hover:transition hover:duration-300 hover:ease-in-out\"\n              href=\"#\"\n            >\n              <MapPin className=\"h-5 w-5 text-white\" />\n            </Link>\n            <div className=\"text-md text-slate-300\">\n              <p>Location at </p>\n              <p>Techno Main Salt Lake, Sector-V, Kolkata-700091</p>\n            </div>\n          </div>\n\n          <div className=\"flex space-x-12 py-7\">\n            <Link\n              className=\"flex h-10 w-10 items-center justify-center rounded-full border border-slate-600 bg-gray-800 hover:shadow-md hover:shadow-slate-500 hover:transition hover:duration-300 hover:ease-in-out\"\n              href=\"#\"\n            >\n              <Twitter className=\"h-5 w-5 text-white\" />\n            </Link>\n            <Link\n              className=\"flex h-10 w-10 items-center justify-center rounded-full border border-slate-600 bg-gray-800 hover:shadow-md hover:shadow-slate-500 hover:transition hover:duration-300 hover:ease-in-out\"\n              href=\"#\"\n            >\n              <Facebook className=\"h-5 w-5 text-white\" />\n            </Link>\n            <Link\n              className=\"flex h-10 w-10 items-center justify-center rounded-full border border-slate-700 bg-gray-800 hover:shadow-md hover:shadow-slate-500 hover:transition hover:duration-300 hover:ease-in-out\"\n              href=\"#\"\n            >\n              <Instagram className=\"h-5 w-5 text-white\" />\n            </Link>\n            <Link\n              className=\"flex h-10 w-10 items-center justify-center rounded-full border border-slate-700 bg-gray-800 hover:shadow-md hover:shadow-slate-500 hover:transition hover:duration-300 hover:ease-in-out\"\n              href=\"#\"\n            >\n              <Github className=\"h-5 w-5 text-white\" />\n            </Link>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/contact/contact-us-2.tsx", "target": "components/mvpblocks/contact-us-2.tsx"}]}