{"name": "masonry-grid-1", "type": "registry:block", "dependencies": ["react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\n\nconst images = [\n  'https://picsum.photos/800/600?random=1',\n  'https://picsum.photos/640/480?random=2',\n  'https://picsum.photos/1280/720?random=3',\n  'https://picsum.photos/960/540?random=4',\n  'https://picsum.photos/900/300?random=5',\n  'https://picsum.photos/1200/600?random=6',\n  'https://picsum.photos/400/600?random=7',\n  'https://picsum.photos/300/450?random=8',\n  'https://picsum.photos/600/800?random=9',\n  'https://picsum.photos/450/600?random=10',\n  'https://picsum.photos/600/600?random=11',\n  'https://picsum.photos/500/550?random=12',\n  'https://picsum.photos/700/850?random=20',\n  'https://picsum.photos/1280/960?random=14',\n  'https://picsum.photos/1440/810?random=15',\n  'https://picsum.photos/1024/768?random=16',\n  'https://picsum.photos/800/800?random=17',\n  'https://picsum.photos/1080/720?random=18',\n  'https://picsum.photos/1920/1080?random=19',\n  'https://picsum.photos/1280/800?random=20',\n  'https://picsum.photos/800/400?random=21',\n  'https://picsum.photos/1024/576?random=22',\n  'https://picsum.photos/640/360?random=23',\n];\n\nexport default function MasonryGallery() {\n  const [hovered, setHovered] = useState<number | null>(null);\n\n  return (\n    <div className=\"min-h-screen px-4 py-20 md:px-6\">\n      <div className=\"columns-1 gap-4 space-y-4 transition-all sm:columns-2 md:columns-3 lg:columns-4\">\n        {images.map((src, index) => (\n          <motion.div\n            key={index}\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.4, delay: index * 0.1 }}\n            viewport={{ once: true }}\n            onMouseEnter={() => setHovered(index)}\n            onMouseLeave={() => setHovered(null)}\n            className=\"group relative overflow-hidden rounded-2xl shadow-lg transition-all duration-300 ease-in-out\"\n          >\n            <motion.img\n              src={src}\n              alt={`Random ${index}`}\n              className={`w-full rounded-lg object-cover transition-all duration-300 ease-in-out ${\n                hovered === null\n                  ? 'blur-0 scale-100'\n                  : hovered === index\n                    ? 'blur-0 scale-105'\n                    : 'blur-xs'\n              }`}\n              whileHover={{ scale: 1.05 }}\n            />\n          </motion.div>\n        ))}\n      </div>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/grids/masonry-grid-1.tsx", "target": "components/mvpblocks/masonry-grid-1.tsx"}]}