{"name": "geometric-hero", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/button.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { easeInOut, motion } from 'framer-motion';\nimport { Button } from '@/components/ui/button';\nimport { ArrowRight } from 'lucide-react';\nimport { Pacifico } from 'next/font/google';\nimport { cn } from '@/lib/utils';\n\nconst pacifico = Pacifico({\n  subsets: ['latin'],\n  weight: ['400'],\n  variable: '--font-pacifico',\n});\n\nfunction ElegantShape({\n  className,\n  delay = 0,\n  width = 400,\n  height = 100,\n  rotate = 0,\n  gradient = 'from-white/[0.08]',\n}: {\n  className?: string;\n  delay?: number;\n  width?: number;\n  height?: number;\n  rotate?: number;\n  gradient?: string;\n}) {\n  return (\n    <motion.div\n      initial={{\n        opacity: 0,\n        y: -150,\n        rotate: rotate - 15,\n      }}\n      animate={{\n        opacity: 1,\n        y: 0,\n        rotate: rotate,\n      }}\n      transition={{\n        duration: 2.4,\n        delay,\n        ease: [0.23, 0.86, 0.39, 0.96],\n        opacity: { duration: 1.2 },\n      }}\n      className={cn('absolute', className)}\n    >\n      <motion.div\n        animate={{\n          y: [0, 15, 0],\n        }}\n        transition={{\n          duration: 12,\n          repeat: Number.POSITIVE_INFINITY,\n          ease: 'easeInOut',\n        }}\n        style={{\n          width,\n          height,\n        }}\n        className=\"relative\"\n      >\n        <div\n          className={cn(\n            'absolute inset-0 rounded-full',\n            'bg-gradient-to-r to-transparent',\n            gradient,\n            'border-2 border-white/80 backdrop-blur-[2px] dark:border-white/80',\n            'shadow-[0_8px_32px_0_rgba(255,255,255,0.4)] dark:shadow-[0_8px_32px_0_rgba(255,255,255,0.5)]',\n            'after:absolute after:inset-0 after:rounded-full',\n            'after:bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.6),transparent_70%)]',\n            'dark:after:bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.7),transparent_70%)]',\n          )}\n        />\n      </motion.div>\n    </motion.div>\n  );\n}\n\nexport default function HeroGeometric({\n  badge = 'MVPBlocks',\n  title1 = 'Build Faster',\n  title2 = 'Ship Sooner',\n}: {\n  badge?: string;\n  title1?: string;\n  title2?: string;\n}) {\n  const fadeUpVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: (i: number) => ({\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 1,\n        delay: 0.5 + i * 0.2,\n        ease: easeInOut,\n      },\n    }),\n  };\n\n  return (\n    <div className=\"bg-background relative flex min-h-screen w-full items-center justify-center overflow-hidden dark:bg-black\">\n      <div className=\"from-primary/20 dark:from-primary/30 absolute inset-0 bg-gradient-to-br via-transparent to-rose-500/20 blur-3xl dark:to-rose-500/30\" />\n\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <ElegantShape\n          delay={0.3}\n          width={600}\n          height={140}\n          rotate={12}\n          gradient=\"from-indigo-500/70\"\n          className=\"top-[15%] left-[-10%] md:top-[20%] md:left-[-5%]\"\n        />\n\n        <ElegantShape\n          delay={0.5}\n          width={500}\n          height={120}\n          rotate={-15}\n          gradient=\"from-rose-400\"\n          className=\"top-[70%] right-[-5%] md:top-[75%] md:right-[0%]\"\n        />\n\n        <ElegantShape\n          delay={0.4}\n          width={300}\n          height={80}\n          rotate={-8}\n          gradient=\"from-violet-400\"\n          className=\"bottom-[5%] left-[5%] md:bottom-[10%] md:left-[10%]\"\n        />\n\n        <ElegantShape\n          delay={0.6}\n          width={200}\n          height={60}\n          rotate={20}\n          gradient=\"from-amber-500/70 dark:from-amber-400/90\"\n          className=\"top-[10%] right-[15%] md:top-[15%] md:right-[20%]\"\n        />\n\n        <ElegantShape\n          delay={0.7}\n          width={150}\n          height={40}\n          rotate={-25}\n          gradient=\"from-cyan-500/70 dark:from-cyan-400/90\"\n          className=\"top-[5%] left-[20%] md:top-[10%] md:left-[25%]\"\n        />\n      </div>\n\n      <div className=\"relative z-10 container mx-auto max-w-6xl px-4 md:px-6\">\n        <div className=\"mx-auto max-w-3xl text-center\">\n          <motion.div\n            custom={0}\n            variants={fadeUpVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n            className=\"border-primary/30 bg-card/50 mb-8 inline-flex items-center gap-2 rounded-full border px-4 py-1.5 shadow-sm backdrop-blur-sm md:mb-12\"\n          >\n            <img src=\"/logo.webp\" alt=\"logo\" className=\"h-6 w-6\" />\n            <span className=\"text-foreground text-sm font-medium tracking-wide\">\n              {badge}\n            </span>\n          </motion.div>\n\n          <motion.div\n            custom={1}\n            variants={fadeUpVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n          >\n            <h1 className=\"mx-4 mb-6 text-4xl font-bold tracking-tight sm:text-6xl md:mb-8 md:text-8xl\">\n              <span className=\"from-foreground to-foreground/80 bg-gradient-to-b bg-clip-text text-transparent\">\n                {title1}\n              </span>\n              <br />\n              <span\n                className={cn(\n                  'from-primary via-primary/90 bg-gradient-to-r to-rose-500 bg-clip-text p-4 text-transparent',\n                  pacifico.className,\n                  'font-bold',\n                )}\n              >\n                {title2}\n              </span>\n            </h1>\n          </motion.div>\n\n          <motion.div\n            custom={2}\n            variants={fadeUpVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n          >\n            <p className=\"text-muted-foreground mx-auto mb-10 max-w-xl px-4 text-base leading-relaxed sm:text-lg md:text-xl\">\n              Accelerate your development with our modern, accessible, and\n              customizable UI components.\n            </p>\n          </motion.div>\n\n          <motion.div\n            custom={3}\n            variants={fadeUpVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n            className=\"flex flex-col justify-center gap-4 sm:flex-row\"\n          >\n            <Button\n              size=\"lg\"\n              className=\"from-primary shadow-primary/10 hover:from-primary/90 rounded-full border-none bg-gradient-to-r to-rose-500 shadow-md hover:to-rose-500/90\"\n            >\n              Get Started\n              <ArrowRight className=\"ml-2 h-4 w-4\" />\n            </Button>\n            <Button\n              size=\"lg\"\n              variant=\"outline\"\n              className=\"border-primary/30 hover:bg-primary/5 rounded-full shadow-sm\"\n            >\n              View Components\n            </Button>\n          </motion.div>\n        </div>\n      </div>\n\n      <div className=\"from-background to-background/80 pointer-events-none absolute inset-0 bg-gradient-to-t via-transparent dark:from-black dark:to-black/80\" />\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/hero/geometric-hero.tsx", "target": "components/mvpblocks/geometric-hero.tsx"}]}