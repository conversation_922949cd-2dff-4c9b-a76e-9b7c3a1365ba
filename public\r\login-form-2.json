{"name": "login-form-2", "author": "Xeven777", "type": "registry:block", "dependencies": ["framer-motion"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/input.json", "https://blocks.mvp-subha.me/r/label.json", "https://blocks.mvp-subha.me/r/card.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Card, CardContent } from '@/components/ui/card';\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\n\nexport default function LoginForm2() {\n  return (\n    <div className=\"rose-gradient bg-background relative min-h-screen overflow-hidden\">\n      <div className=\"from-background absolute -top-10 left-0 h-1/2 w-full rounded-b-full bg-gradient-to-b to-transparent blur\"></div>\n      <div className=\"from-primary/80 absolute -top-64 left-0 h-1/2 w-full rounded-full bg-gradient-to-b to-transparent blur-3xl\"></div>\n      <div className=\"relative z-10 grid min-h-screen grid-cols-1 md:grid-cols-2\">\n        <motion.div\n          className=\"hidden flex-1 items-center justify-center space-y-8 p-8 text-center md:flex\"\n          initial={{ opacity: 0, x: -50 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.8, ease: 'easeOut' }}\n        >\n          <div className=\"space-y-6\">\n            <motion.div\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.8, delay: 0.2, ease: 'easeOut' }}\n            >\n              <img\n                src=\"/assets/roblox-logo.webp\"\n                alt=\"Illustration\"\n                className=\"mx-auto h-auto w-full md:w-90\"\n              />\n            </motion.div>\n            {/* <motion.h1\n              className=\"text-2xl md:text-4xl font-bold leading-tight tracking-tight\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.4, ease: \"easeOut\" }}\n            >\n              The make money app\n            </motion.h1> */}\n          </div>\n        </motion.div>\n\n        {/* Right Side - Login Form */}\n        <motion.div\n          className=\"flex flex-1 items-center justify-center p-8\"\n          initial={{ opacity: 0, x: 50 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.8, ease: 'easeOut' }}\n        >\n          <motion.div\n            initial={{ opacity: 0, y: 30, scale: 0.95 }}\n            animate={{ opacity: 1, y: 0, scale: 1 }}\n            transition={{ duration: 0.6, delay: 0.2, ease: 'easeOut' }}\n          >\n            <Card className=\"border-border/70 bg-card/20 w-full max-w-md shadow-[0_10px_26px_#e0e0e0a1] backdrop-blur-lg dark:shadow-none\">\n              <CardContent className=\"space-y-6 p-8\">\n                {/* Logo and Header */}\n                <motion.div\n                  className=\"space-y-4 text-center\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: 0.4, ease: 'easeOut' }}\n                >\n                  <div className=\"flex items-center justify-center space-x-2\">\n                    <span className=\"text-2xl font-bold tracking-tight md:text-4xl\">\n                      Login\n                    </span>\n                  </div>\n                  <p className=\"text-muted-foreground text-sm\">\n                    Create an account or log in to discover Purgions and find\n                    ways to make money.\n                  </p>\n                </motion.div>\n\n                {/* Email Input */}\n                <motion.div\n                  className=\"space-y-2\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: 0.5, ease: 'easeOut' }}\n                >\n                  <Label htmlFor=\"email\">Email</Label>\n                  <Input id=\"email\" type=\"email\" />\n                </motion.div>\n\n                <motion.div\n                  className=\"space-y-2\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: 0.6, ease: 'easeOut' }}\n                >\n                  <Label htmlFor=\"password\">Password</Label>\n                  <Input\n                    id=\"password\"\n                    type=\"password\"\n                    className=\"border-border border\"\n                  />\n                </motion.div>\n\n                {/* Continue Button */}\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: 0.7, ease: 'easeOut' }}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <Button className=\"w-full\">Continue</Button>\n                </motion.div>\n\n                {/* Divider */}\n                <motion.div\n                  className=\"relative\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  transition={{ duration: 0.5, delay: 0.8, ease: 'easeOut' }}\n                >\n                  <div className=\"absolute inset-0 flex items-center\">\n                    <div className=\"border-border w-full border-t\"></div>\n                  </div>\n                  <div className=\"relative flex justify-center text-sm\">\n                    <span className=\"bg-card text-muted-foreground px-2\">\n                      OR\n                    </span>\n                  </div>\n                </motion.div>\n\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: 0.9, ease: 'easeOut' }}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <Button\n                    variant=\"secondary\"\n                    className=\"bg-primary-foreground text-primary hover:bg-primary-foreground/95 w-full shadow-[0_4px_16px_var(--border)] duration-300 dark:shadow-[0_4px_14px_var(--muted-foreground)]\"\n                  >\n                    <svg\n                      className=\"h-5 w-5\"\n                      viewBox=\"0 0 24 24\"\n                      fill=\"currentColor\"\n                    >\n                      <path d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\" />\n                      <path d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\" />\n                      <path d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\" />\n                      <path d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\" />\n                    </svg>\n\n                    <span className=\"ml-2\">Sign in with Google</span>\n                  </Button>\n                </motion.div>\n\n                {/* Terms */}\n                <motion.p\n                  className=\"text-muted-foreground mt-2 text-center text-xs\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  transition={{ duration: 0.5, delay: 1.0, ease: 'easeOut' }}\n                >\n                  By signing in you agree to our{' '}\n                  <Link\n                    href=\"#\"\n                    className=\"text-muted-foreground hover:text-primary underline\"\n                  >\n                    terms of service\n                  </Link>{' '}\n                  and{' '}\n                  <Link\n                    href=\"#\"\n                    className=\"text-muted-foreground hover:text-primary underline\"\n                  >\n                    privacy policy\n                  </Link>\n                  .\n                </motion.p>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </motion.div>\n      </div>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/forms/login-form-2.tsx", "target": "components/mvpblocks/login-form-2.tsx"}]}