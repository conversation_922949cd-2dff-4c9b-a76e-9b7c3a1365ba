{"name": "notebook", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/button.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { buttonVariants } from '@/components/ui/button';\nimport { cn } from '@/lib/utils';\nimport { ArrowRight } from 'lucide-react';\n\nexport default function NotebookHero() {\n  return (\n    <div className=\"min-h-screen py-6 sm:py-14\">\n      <div className=\"pointer-events-none absolute inset-0 top-0 z-0 overflow-hidden\">\n        <div className=\"absolute -top-20 -left-20 h-[600px] w-[600px] rounded-full bg-gradient-to-br from-rose-500/30 via-rose-500/20 to-transparent opacity-50 blur-[100px]\" />\n        <div className=\"absolute -top-40 -right-20 h-[500px] w-[500px] rounded-full bg-gradient-to-bl from-red-500/30 via-red-500/20 to-transparent opacity-50 blur-[100px]\" />\n        <div className=\"absolute bottom-0 left-0 h-[400px] w-[400px] rounded-full bg-gradient-to-tr from-amber-500/20 via-amber-500/10 to-transparent opacity-30 blur-[80px]\" />\n      </div>\n\n      <main className=\"relative container mt-4 max-w-[1100px] px-2 py-4 lg:py-8\">\n        <div className=\"relative sm:overflow-hidden\">\n          <div className=\"border-primary/20 bg-background/70 shadow-primary/10 relative flex flex-col items-start justify-start rounded-xl border px-4 pt-12 shadow-xl backdrop-blur-md max-md:text-center md:px-12 md:pt-16\">\n            <div\n              className=\"animate-gradient-x absolute inset-0 top-32 z-0 hidden blur-2xl dark:block\"\n              style={{\n                maskImage:\n                  'linear-gradient(to bottom, transparent, white, transparent)',\n                background:\n                  'repeating-linear-gradient(65deg, hsl(var(--primary)), hsl(var(--primary)/0.8) 12px, color-mix(in oklab, hsl(var(--primary)) 30%, transparent) 20px, transparent 200px)',\n                backgroundSize: '200% 100%',\n              }}\n            />\n            <div\n              className=\"animate-gradient-x absolute inset-0 top-32 z-0 text-left blur-2xl dark:hidden\"\n              style={{\n                maskImage:\n                  'linear-gradient(to bottom, transparent, white, transparent)',\n                background:\n                  'repeating-linear-gradient(65deg, hsl(var(--primary)/0.9), hsl(var(--primary)/0.7) 12px, color-mix(in oklab, hsl(var(--primary)) 30%, transparent) 20px, transparent 200px)',\n                backgroundSize: '200% 100%',\n              }}\n            />\n            <h1 className=\"mb-4 flex flex-wrap gap-2 text-3xl leading-tight font-medium md:text-5xl\">\n              Build <span className=\"text-primary\">Beautiful UI</span> with\n              MVPBlocks\n            </h1>\n            <p className=\"text-muted-foreground mb-8 text-left md:max-w-[80%] md:text-xl\">\n              Your comprehensive library of ready-to-use UI components built\n              with Next.js and Tailwind CSS. From simple buttons to complex\n              layouts, MVPBlocks helps you create stunning interfaces with\n              minimal effort.\n            </p>\n            <div className=\"mb-6 flex flex-wrap gap-4 md:flex-row\">\n              <div className=\"flex items-center gap-2\">\n                <svg\n                  className=\"text-primary h-5 w-5\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth=\"2\"\n                    d=\"M5 13l4 4L19 7\"\n                  ></path>\n                </svg>\n                <span>100+ Components</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <svg\n                  className=\"text-primary h-5 w-5\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth=\"2\"\n                    d=\"M5 13l4 4L19 7\"\n                  ></path>\n                </svg>\n                <span>Dark & Light Mode</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <svg\n                  className=\"text-primary h-5 w-5\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth=\"2\"\n                    d=\"M5 13l4 4L19 7\"\n                  ></path>\n                </svg>\n                <span>Responsive Design</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <svg\n                  className=\"text-primary h-5 w-5\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth=\"2\"\n                    d=\"M5 13l4 4L19 7\"\n                  ></path>\n                </svg>\n                <span>Accessible Components</span>\n              </div>\n            </div>\n\n            <div className=\"z-10 mt-2 inline-flex items-center justify-start gap-3\">\n              <a\n                href=\"#\"\n                className={cn(\n                  buttonVariants({\n                    size: 'lg',\n                    className:\n                      'from-primary to-primary/80 text-primary-foreground rounded-full bg-gradient-to-b',\n                  }),\n                )}\n              >\n                Getting Started <ArrowRight className=\"size-4\" />\n              </a>\n              <a\n                href=\"https://github.com/subhadeeproy3902/mvpblocks\"\n                target=\"_blank\"\n                rel=\"noreferrer noopener\"\n                className={cn(\n                  buttonVariants({\n                    size: 'lg',\n                    variant: 'outline',\n                    className: 'bg-background rounded-full',\n                  }),\n                )}\n              >\n                GitHub{' '}\n                <svg\n                  className=\"ml-1 inline size-4\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  strokeWidth=\"2\"\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                >\n                  <path d=\"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4\" />\n                  <path d=\"M9 18c-4.51 2-5-2-7-2\" />\n                </svg>\n              </a>\n            </div>\n\n            <div className=\"relative z-10 mt-16 w-full\">\n              <img\n                src=\"https://blocks.mvp-subha.me/assets/bg.webp\"\n                alt=\"MVPBlocks component library preview\"\n                width={1000}\n                height={600}\n                className=\"animate-in fade-in slide-in-from-bottom-12 z-10 mx-auto -mb-60 w-full rounded-lg border-6 border-neutral-100 object-cover shadow-2xl duration-1000 select-none lg:-mb-40 dark:border-neutral-600\"\n              />\n\n              <div className=\"animate-in fade-in slide-in-from-left-4 absolute -top-6 -right-6 rotate-6 transform rounded-lg bg-white p-3 shadow-lg dark:bg-neutral-900\">\n                <div className=\"flex items-center gap-2\">\n                  <svg\n                    className=\"h-5 w-5 text-green-500\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                  >\n                    <path\n                      fillRule=\"evenodd\"\n                      d=\"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\"\n                      clipRule=\"evenodd\"\n                    ></path>\n                  </svg>\n                  <span className=\"font-medium\">Ready-to-Use Components</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/hero/notebook.tsx", "target": "components/mvpblocks/notebook.tsx"}]}