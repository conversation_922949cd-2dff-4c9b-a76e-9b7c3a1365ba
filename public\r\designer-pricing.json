{"name": "designer-pricing", "type": "registry:block", "dependencies": [], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { Instrument_Serif } from 'next/font/google';\nimport { cn } from '@/lib/utils';\n\nconst serif = Instrument_Serif({\n  subsets: ['latin'],\n  weight: '400',\n});\n\nexport default function DesignerPricing() {\n  return (\n    <div className=\"relative min-h-full w-full bg-white font-sans text-black antialiased\">\n      <section className=\"relative mr-auto ml-auto max-w-7xl pt-16 pr-4 pb-16 pl-4 sm:px-6 sm:py-24 lg:px-8\">\n        <div className=\"mb-16 text-center sm:mb-20\">\n          <h1 className=\"mb-6 text-4xl leading-tight font-bold tracking-tight sm:text-5xl lg:text-6xl\">\n            <span\n              className={cn(\n                'text-7xl font-normal tracking-tight text-red-500',\n                serif.className,\n              )}\n            >\n              Choose Your\n            </span>\n            <br />\n            <span\n              className={cn(\n                'text-8xl font-normal tracking-tight text-black',\n                serif.className,\n              )}\n            >\n              Creative Journey\n            </span>\n          </h1>\n          <p className=\"mr-auto ml-auto max-w-3xl text-base text-neutral-600 sm:text-lg\">\n            From indie creators to enterprise teams, we&apos;ve crafted the\n            perfect plan for every stage of your design evolution\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-2 lg:gap-8 xl:grid-cols-3\">\n          <article className=\"relative flex flex-col rounded-3xl border-2 border-neutral-200 bg-white pt-8 pr-8 pb-8 pl-8 transition-all duration-300 hover:border-red-500 lg:p-10\">\n            <div className=\"mb-8 flex items-start justify-between\">\n              <div className=\"flex items-center gap-2\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  stroke-width=\"2\"\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  data-lucide=\"palette\"\n                  className=\"lucide lucide-palette h-5 w-5 text-red-500\"\n                >\n                  <path d=\"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z\"></path>\n                  <circle\n                    cx=\"13.5\"\n                    cy=\"6.5\"\n                    r=\".5\"\n                    fill=\"currentColor\"\n                  ></circle>\n                  <circle\n                    cx=\"17.5\"\n                    cy=\"10.5\"\n                    r=\".5\"\n                    fill=\"currentColor\"\n                  ></circle>\n                  <circle\n                    cx=\"6.5\"\n                    cy=\"12.5\"\n                    r=\".5\"\n                    fill=\"currentColor\"\n                  ></circle>\n                  <circle cx=\"8.5\" cy=\"7.5\" r=\".5\" fill=\"currentColor\"></circle>\n                </svg>\n                <span className=\"text-sm font-semibold tracking-wide text-neutral-500 uppercase\">\n                  Creative\n                </span>\n              </div>\n              <span className=\"rounded-full border border-red-200 bg-red-50 px-3 py-1 text-xs font-medium text-red-600 uppercase\">\n                Perfect Start\n              </span>\n            </div>\n\n            <div className=\"mb-8\">\n              <h2 className=\"mb-3 text-2xl leading-tight font-medium lg:text-2xl\">\n                Individual Creators\n              </h2>\n              <p className=\"text-sm text-neutral-600\">\n                Everything you need to launch your creative projects\n              </p>\n            </div>\n\n            <div className=\"mb-8\">\n              <div className=\"mb-2 flex items-end gap-2\">\n                <span className=\"text-4xl font-bold tracking-tight lg:text-5xl\">\n                  $79\n                </span>\n                <span className=\"mb-1 text-neutral-500\">/month</span>\n              </div>\n              <p className=\"text-xs text-neutral-500\">\n                Billed monthly • Cancel anytime\n              </p>\n            </div>\n\n            <div className=\"mb-8 flex flex-col gap-3\">\n              <button className=\"w-full rounded-full bg-red-500 px-6 py-3 text-sm font-semibold text-white transition-all duration-200 hover:bg-red-600\">\n                Start Creating\n              </button>\n              <button className=\"w-full rounded-full border-2 border-black px-6 py-3 text-sm font-medium text-black transition-all duration-200 hover:bg-black hover:text-white\">\n                Learn More\n              </button>\n            </div>\n\n            <hr className=\"mb-8 border-neutral-200\" />\n\n            <ul className=\"space-y-4 text-sm\">\n              <li className=\"flex items-start gap-3\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  stroke-width=\"2\"\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  data-lucide=\"check-circle\"\n                  className=\"lucide lucide-check-circle mt-0.5 h-4 w-4 flex-shrink-0 text-red-500\"\n                >\n                  <path d=\"M21.801 10A10 10 0 1 1 17 3.335\"></path>\n                  <path d=\"m9 11 3 3L22 4\"></path>\n                </svg>\n                <span className=\"\">\n                  <strong>Brand Identity</strong> - Logo, colors, typography\n                </span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  stroke-width=\"2\"\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  data-lucide=\"check-circle\"\n                  className=\"lucide lucide-check-circle mt-0.5 h-4 w-4 flex-shrink-0 text-red-500\"\n                >\n                  <path d=\"M21.801 10A10 10 0 1 1 17 3.335\"></path>\n                  <path d=\"m9 11 3 3L22 4\"></path>\n                </svg>\n                <span className=\"\">\n                  <strong>Website</strong> - 5 pages, mobile-optimized\n                </span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  stroke-width=\"2\"\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  data-lucide=\"check-circle\"\n                  className=\"lucide lucide-check-circle mt-0.5 h-4 w-4 flex-shrink-0 text-red-500\"\n                >\n                  <path d=\"M21.801 10A10 10 0 1 1 17 3.335\"></path>\n                  <path d=\"m9 11 3 3L22 4\"></path>\n                </svg>\n                <span className=\"\">\n                  <strong>Social Media Kit</strong> - 10 custom templates\n                </span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  stroke-width=\"2\"\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  data-lucide=\"check-circle\"\n                  className=\"lucide lucide-check-circle mt-0.5 h-4 w-4 flex-shrink-0 text-red-500\"\n                >\n                  <path d=\"M21.801 10A10 10 0 1 1 17 3.335\"></path>\n                  <path d=\"m9 11 3 3L22 4\"></path>\n                </svg>\n                <span>\n                  <strong>Email Support</strong> - Response within 24 hours\n                </span>\n              </li>\n            </ul>\n          </article>\n\n          <article className=\"relative z-10 flex scale-105 flex-col rounded-3xl border-2 border-black bg-black p-8 text-white transition-all duration-300 lg:scale-110 lg:p-10\">\n            <div className=\"absolute -top-4 left-1/2 -translate-x-1/2 transform\">\n              <div className=\"rounded-full bg-red-500 px-4 py-2 text-xs font-bold text-white uppercase\">\n                Most Popular\n              </div>\n            </div>\n\n            <div className=\"mt-4 mb-8 flex items-start justify-between\">\n              <div className=\"flex items-center gap-2\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  stroke-width=\"2\"\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  data-lucide=\"rocket\"\n                  className=\"lucide lucide-rocket h-5 w-5 text-red-500\"\n                >\n                  <path d=\"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z\"></path>\n                  <path d=\"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z\"></path>\n                  <path d=\"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0\"></path>\n                  <path d=\"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5\"></path>\n                </svg>\n                <span className=\"text-sm font-semibold tracking-wide text-neutral-400 uppercase\">\n                  Professional\n                </span>\n              </div>\n            </div>\n\n            <div className=\"mb-8\">\n              <h2 className=\"mb-3 text-2xl leading-tight font-medium lg:text-2xl\">\n                Growing Businesses\n              </h2>\n              <p className=\"text-sm text-neutral-300\">\n                Scale your brand with comprehensive design systems\n              </p>\n            </div>\n\n            <div className=\"mb-8\">\n              <div className=\"mb-2 flex items-end gap-2\">\n                <span className=\"text-4xl font-bold tracking-tight lg:text-5xl\">\n                  $199\n                </span>\n                <span className=\"mb-1 text-neutral-400\">/month</span>\n              </div>\n              <p className=\"text-xs text-neutral-400\">\n                Billed monthly • 2 months free yearly\n              </p>\n            </div>\n\n            <div className=\"mb-8 flex flex-col gap-3\">\n              <button className=\"w-full rounded-full bg-white px-6 py-3 text-sm font-semibold text-black transition-all duration-200 hover:bg-neutral-100\">\n                Scale Your Brand\n              </button>\n              <button className=\"w-full rounded-full border-2 border-white px-6 py-3 text-sm font-medium text-white transition-all duration-200 hover:bg-white hover:text-black\">\n                Book Demo\n              </button>\n            </div>\n\n            <hr className=\"mb-8 border-neutral-700\" />\n\n            <ul className=\"space-y-4 text-sm\">\n              <li className=\"flex items-start gap-3\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  stroke-width=\"2\"\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  data-lucide=\"check-circle\"\n                  className=\"lucide lucide-check-circle mt-0.5 h-4 w-4 flex-shrink-0 text-red-500\"\n                >\n                  <path d=\"M21.801 10A10 10 0 1 1 17 3.335\"></path>\n                  <path d=\"m9 11 3 3L22 4\"></path>\n                </svg>\n                <span className=\"\">\n                  <strong>Brand Strategy</strong> - Market research &amp;\n                  positioning\n                </span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  stroke-width=\"2\"\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  data-lucide=\"check-circle\"\n                  className=\"lucide lucide-check-circle mt-0.5 h-4 w-4 flex-shrink-0 text-red-500\"\n                >\n                  <path d=\"M21.801 10A10 10 0 1 1 17 3.335\"></path>\n                  <path d=\"m9 11 3 3L22 4\"></path>\n                </svg>\n                <span>\n                  <strong>Web Experience</strong> - 15 pages + e-commerce\n                </span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  stroke-width=\"2\"\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  data-lucide=\"check-circle\"\n                  className=\"lucide lucide-check-circle mt-0.5 h-4 w-4 flex-shrink-0 text-red-500\"\n                >\n                  <path d=\"M21.801 10A10 10 0 1 1 17 3.335\"></path>\n                  <path d=\"m9 11 3 3L22 4\"></path>\n                </svg>\n                <span>\n                  <strong>UI/UX Design</strong> - App or web application\n                </span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  stroke-width=\"2\"\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  data-lucide=\"check-circle\"\n                  className=\"lucide lucide-check-circle mt-0.5 h-4 w-4 flex-shrink-0 text-red-500\"\n                >\n                  <path d=\"M21.801 10A10 10 0 1 1 17 3.335\"></path>\n                  <path d=\"m9 11 3 3L22 4\"></path>\n                </svg>\n                <span>\n                  <strong>Marketing Materials</strong> - Brochures,\n                  presentations\n                </span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  stroke-width=\"2\"\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  data-lucide=\"check-circle\"\n                  className=\"lucide lucide-check-circle mt-0.5 h-4 w-4 flex-shrink-0 text-red-500\"\n                >\n                  <path d=\"M21.801 10A10 10 0 1 1 17 3.335\"></path>\n                  <path d=\"m9 11 3 3L22 4\"></path>\n                </svg>\n                <span>\n                  <strong>Priority Support</strong> - Slack + video calls\n                </span>\n              </li>\n            </ul>\n          </article>\n\n          <article className=\"relative flex flex-col rounded-3xl border-2 border-neutral-200 bg-white pt-8 pr-8 pb-8 pl-8 transition-all duration-300 hover:border-red-500 lg:col-span-2 lg:p-10 xl:col-span-1\">\n            <div className=\"mb-8 flex items-start justify-between\">\n              <div className=\"flex items-center gap-2\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  stroke-width=\"2\"\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  data-lucide=\"building-2\"\n                  className=\"lucide lucide-building-2 h-5 w-5 text-red-500\"\n                >\n                  <path d=\"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z\"></path>\n                  <path d=\"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2\"></path>\n                  <path d=\"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2\"></path>\n                  <path d=\"M10 6h4\"></path>\n                  <path d=\"M10 10h4\"></path>\n                  <path d=\"M10 14h4\"></path>\n                  <path d=\"M10 18h4\"></path>\n                </svg>\n                <span className=\"text-sm font-semibold tracking-wide text-neutral-500 uppercase\">\n                  Enterprise\n                </span>\n              </div>\n              <span className=\"rounded-full border border-neutral-300 bg-neutral-100 px-3 py-1 text-xs font-medium text-neutral-700 uppercase\">\n                Custom\n              </span>\n            </div>\n\n            <div className=\"mb-8\">\n              <h2 className=\"mb-3 text-2xl leading-tight font-medium lg:text-2xl\">\n                Large Teams\n              </h2>\n              <p className=\"text-sm text-neutral-600\">\n                Dedicated design team with unlimited creative capacity\n              </p>\n            </div>\n\n            <div className=\"mb-8\">\n              <div className=\"mb-2 flex items-end gap-2\">\n                <span className=\"text-4xl font-bold tracking-tight text-black lg:text-5xl\">\n                  Custom\n                </span>\n              </div>\n              <p className=\"text-xs text-neutral-500\">\n                Tailored pricing based on your needs\n              </p>\n            </div>\n\n            <div className=\"mb-8 flex flex-col gap-3\">\n              <button className=\"w-full rounded-full bg-black px-6 py-3 text-sm font-semibold text-white transition-all duration-200 hover:bg-neutral-800\">\n                Get Custom Quote\n              </button>\n              <button className=\"w-full rounded-full border-2 border-black px-6 py-3 text-sm font-medium text-black transition-all duration-200 hover:bg-black hover:text-white\">\n                Enterprise Demo\n              </button>\n            </div>\n\n            <hr className=\"mb-8 border-neutral-200\" />\n\n            <ul className=\"space-y-4 text-sm\">\n              <li className=\"flex items-start gap-3\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  stroke-width=\"2\"\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  data-lucide=\"check-circle\"\n                  className=\"lucide lucide-check-circle mt-0.5 h-4 w-4 flex-shrink-0 text-red-500\"\n                >\n                  <path d=\"M21.801 10A10 10 0 1 1 17 3.335\"></path>\n                  <path d=\"m9 11 3 3L22 4\"></path>\n                </svg>\n                <span>\n                  <strong>Dedicated Team</strong> - 3-5 senior designers\n                </span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  stroke-width=\"2\"\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  data-lucide=\"check-circle\"\n                  className=\"lucide lucide-check-circle mt-0.5 h-4 w-4 flex-shrink-0 text-red-500\"\n                >\n                  <path d=\"M21.801 10A10 10 0 1 1 17 3.335\"></path>\n                  <path d=\"m9 11 3 3L22 4\"></path>\n                </svg>\n                <span>\n                  <strong>Enterprise System</strong> - Multi-brand management\n                </span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  stroke-width=\"2\"\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  data-lucide=\"check-circle\"\n                  className=\"lucide lucide-check-circle mt-0.5 h-4 w-4 flex-shrink-0 text-red-500\"\n                >\n                  <path d=\"M21.801 10A10 10 0 1 1 17 3.335\"></path>\n                  <path d=\"m9 11 3 3L22 4\"></path>\n                </svg>\n                <span>\n                  <strong>White-label Solutions</strong> - Custom portals\n                </span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  stroke-width=\"2\"\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  data-lucide=\"check-circle\"\n                  className=\"lucide lucide-check-circle mt-0.5 h-4 w-4 flex-shrink-0 text-red-500\"\n                >\n                  <path d=\"M21.801 10A10 10 0 1 1 17 3.335\"></path>\n                  <path d=\"m9 11 3 3L22 4\"></path>\n                </svg>\n                <span>\n                  <strong>24/7 Support</strong> - Dedicated account manager\n                </span>\n              </li>\n            </ul>\n          </article>\n        </div>\n\n        <div className=\"mt-16 text-center sm:mt-20\">\n          <p className=\"mb-8 text-sm text-neutral-500\">\n            Trusted by 2,500+ creative professionals worldwide\n          </p>\n          <div className=\"flex items-center justify-center gap-8 opacity-60\">\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"24\"\n              height=\"24\"\n              viewBox=\"0 0 24 24\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              stroke-width=\"2\"\n              stroke-linecap=\"round\"\n              stroke-linejoin=\"round\"\n              data-lucide=\"shield-check\"\n              className=\"lucide lucide-shield-check h-6 w-6\"\n            >\n              <path d=\"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z\"></path>\n              <path d=\"m9 12 2 2 4-4\"></path>\n            </svg>\n            <span className=\"text-sm\">SSL Secured</span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"24\"\n              height=\"24\"\n              viewBox=\"0 0 24 24\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              stroke-width=\"2\"\n              stroke-linecap=\"round\"\n              stroke-linejoin=\"round\"\n              data-lucide=\"clock\"\n              className=\"lucide lucide-clock h-6 w-6\"\n            >\n              <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n              <polyline points=\"12 6 12 12 16 14\"></polyline>\n            </svg>\n            <span className=\"text-sm\">24/7 Support</span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"24\"\n              height=\"24\"\n              viewBox=\"0 0 24 24\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              stroke-width=\"2\"\n              stroke-linecap=\"round\"\n              stroke-linejoin=\"round\"\n              data-lucide=\"refresh-cw\"\n              className=\"lucide lucide-refresh-cw h-6 w-6\"\n            >\n              <path d=\"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8\"></path>\n              <path d=\"M21 3v5h-5\"></path>\n              <path d=\"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16\"></path>\n              <path d=\"M8 16H3v5\"></path>\n            </svg>\n            <span className=\"text-sm\">Cancel Anytime</span>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/pricing/designer-pricing.tsx", "target": "components/mvpblocks/designer-pricing.tsx"}]}