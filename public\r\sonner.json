{"name": "sonner", "type": "registry:ui", "dependencies": ["sonner", "next-themes"], "files": [{"type": "registry:ui", "content": "'use client';\n\nimport { useTheme } from 'next-themes';\nimport { Toaster as Sonner } from 'sonner';\n\ntype ToasterProps = React.ComponentProps<typeof Sonner>;\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = 'system' } = useTheme();\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps['theme']}\n      className=\"toaster group\"\n      toastOptions={{\n        classNames: {\n          toast:\n            'group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg',\n          description: 'group-[.toast]:text-muted-foreground',\n          actionButton:\n            'group-[.toast]:bg-primary group-[.toast]:text-primary-foreground',\n          cancelButton:\n            'group-[.toast]:bg-muted group-[.toast]:text-muted-foreground',\n        },\n      }}\n      {...props}\n    />\n  );\n};\n\nexport { Toaster };\n", "path": "/components/ui/sonner.tsx", "target": "components/ui/sonner.tsx"}]}