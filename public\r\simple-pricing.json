{"name": "simple-pricing", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react", "@number-flow/react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/badge.json", "https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/card.json", "https://blocks.mvp-subha.me/r/tabs.json", "https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';\nimport NumberF<PERSON> from '@number-flow/react';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardFooter,\n  CardHeader,\n  CardTitle,\n} from '@/components/ui/card';\nimport { cn } from '@/lib/utils';\nimport { <PERSON>rkles, ArrowRight, Check, Star, Zap, Shield } from 'lucide-react';\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\n\nconst plans = [\n  {\n    id: 'hobby',\n    name: 'Hobby',\n    icon: Star,\n    price: {\n      monthly: 'Free forever',\n      yearly: 'Free forever',\n    },\n    description:\n      'The perfect starting place for your web app or personal project.',\n    features: [\n      '50 API calls / month',\n      '60 second checks',\n      'Single-user account',\n      '5 monitors',\n      'Basic email support',\n    ],\n    cta: 'Get started for free',\n  },\n  {\n    id: 'pro',\n    name: 'Pro',\n    icon: Zap,\n    price: {\n      monthly: 90,\n      yearly: 75,\n    },\n    description: 'Everything you need to build and scale your business.',\n    features: [\n      'Unlimited API calls',\n      '30 second checks',\n      'Multi-user account',\n      '10 monitors',\n      'Priority email support',\n    ],\n    cta: 'Subscribe to Pro',\n    popular: true,\n  },\n  {\n    id: 'enterprise',\n    name: 'Enterprise',\n    icon: Shield,\n    price: {\n      monthly: 'Get in touch for pricing',\n      yearly: 'Get in touch for pricing',\n    },\n    description: 'Critical security, performance, observability and support.',\n    features: [\n      'You can DDOS our API.',\n      'Nano-second checks.',\n      'Invite your extended family.',\n      'Unlimited monitors.',\n      \"We'll sit on your desk.\",\n    ],\n    cta: 'Contact us',\n  },\n];\n\nexport default function SimplePricing() {\n  const [frequency, setFrequency] = useState<string>('monthly');\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) return null;\n\n  return (\n    <div className=\"not-prose relative flex w-full flex-col gap-16 overflow-hidden px-4 py-24 text-center sm:px-8\">\n      <div className=\"absolute inset-0 -z-10 overflow-hidden\">\n        <div className=\"bg-primary/10 absolute -top-[10%] left-[50%] h-[40%] w-[60%] -translate-x-1/2 rounded-full blur-3xl\" />\n        <div className=\"bg-primary/5 absolute -right-[10%] -bottom-[10%] h-[40%] w-[40%] rounded-full blur-3xl\" />\n        <div className=\"bg-primary/5 absolute -bottom-[10%] -left-[10%] h-[40%] w-[40%] rounded-full blur-3xl\" />\n      </div>\n\n      <div className=\"flex flex-col items-center justify-center gap-8\">\n        <div className=\"flex flex-col items-center space-y-2\">\n          <Badge\n            variant=\"outline\"\n            className=\"border-primary/20 bg-primary/5 mb-4 rounded-full px-4 py-1 text-sm font-medium\"\n          >\n            <Sparkles className=\"text-primary mr-1 h-3.5 w-3.5 animate-pulse\" />\n            Pricing Plans\n          </Badge>\n          <motion.h1\n            initial={{ opacity: 0, y: 10 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n            className=\"from-foreground to-foreground/30 bg-gradient-to-b bg-clip-text text-4xl font-bold text-transparent sm:text-5xl\"\n          >\n            Pick the perfect plan for your needs\n          </motion.h1>\n          <motion.p\n            initial={{ opacity: 0, y: 10 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.1 }}\n            className=\"text-muted-foreground max-w-md pt-2 text-lg\"\n          >\n            Simple, transparent pricing that scales with your business. No\n            hidden fees, no surprises.\n          </motion.p>\n        </div>\n\n        <motion.div\n          initial={{ opacity: 0, scale: 0.95 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.4, delay: 0.2 }}\n        >\n          <Tabs\n            defaultValue={frequency}\n            onValueChange={setFrequency}\n            className=\"bg-muted/30 inline-block rounded-full p-1 shadow-sm\"\n          >\n            <TabsList className=\"bg-transparent\">\n              <TabsTrigger\n                value=\"monthly\"\n                className=\"data-[state=active]:bg-background rounded-full transition-all duration-300 data-[state=active]:shadow-sm\"\n              >\n                Monthly\n              </TabsTrigger>\n              <TabsTrigger\n                value=\"yearly\"\n                className=\"data-[state=active]:bg-background rounded-full transition-all duration-300 data-[state=active]:shadow-sm\"\n              >\n                Yearly\n                <Badge\n                  variant=\"secondary\"\n                  className=\"bg-primary/10 text-primary hover:bg-primary/15 ml-2\"\n                >\n                  20% off\n                </Badge>\n              </TabsTrigger>\n            </TabsList>\n          </Tabs>\n        </motion.div>\n\n        <div className=\"mt-8 grid w-full max-w-6xl grid-cols-1 gap-6 md:grid-cols-3\">\n          {plans.map((plan, index) => (\n            <motion.div\n              key={plan.id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.1 + index * 0.1 }}\n              whileHover={{ y: -5 }}\n              className=\"flex\"\n            >\n              <Card\n                className={cn(\n                  'bg-secondary/20 relative h-full w-full text-left transition-all duration-300 hover:shadow-lg',\n                  plan.popular\n                    ? 'ring-primary/50 dark:shadow-primary/10 shadow-md ring-2'\n                    : 'hover:border-primary/30',\n                  plan.popular &&\n                    'from-primary/[0.03] bg-gradient-to-b to-transparent',\n                )}\n              >\n                {plan.popular && (\n                  <div className=\"absolute -top-3 right-0 left-0 mx-auto w-fit\">\n                    <Badge className=\"bg-primary text-primary-foreground rounded-full px-4 py-1 shadow-sm\">\n                      <Sparkles className=\"mr-1 h-3.5 w-3.5\" />\n                      Popular\n                    </Badge>\n                  </div>\n                )}\n                <CardHeader className={cn('pb-4', plan.popular && 'pt-8')}>\n                  <div className=\"flex items-center gap-2\">\n                    <div\n                      className={cn(\n                        'flex h-8 w-8 items-center justify-center rounded-full',\n                        plan.popular\n                          ? 'bg-primary/10 text-primary'\n                          : 'bg-secondary text-foreground',\n                      )}\n                    >\n                      <plan.icon className=\"h-4 w-4\" />\n                    </div>\n                    <CardTitle\n                      className={cn(\n                        'text-xl font-bold',\n                        plan.popular && 'text-primary',\n                      )}\n                    >\n                      {plan.name}\n                    </CardTitle>\n                  </div>\n                  <CardDescription className=\"mt-3 space-y-2\">\n                    <p className=\"text-sm\">{plan.description}</p>\n                    <div className=\"pt-2\">\n                      {typeof plan.price[\n                        frequency as keyof typeof plan.price\n                      ] === 'number' ? (\n                        <div className=\"flex items-baseline\">\n                          <NumberFlow\n                            className={cn(\n                              'text-3xl font-bold',\n                              plan.popular ? 'text-primary' : 'text-foreground',\n                            )}\n                            format={{\n                              style: 'currency',\n                              currency: 'USD',\n                              maximumFractionDigits: 0,\n                            }}\n                            value={\n                              plan.price[\n                                frequency as keyof typeof plan.price\n                              ] as number\n                            }\n                          />\n                          <span className=\"text-muted-foreground ml-1 text-sm\">\n                            /month, billed {frequency}\n                          </span>\n                        </div>\n                      ) : (\n                        <span\n                          className={cn(\n                            'text-2xl font-bold',\n                            plan.popular ? 'text-primary' : 'text-foreground',\n                          )}\n                        >\n                          {plan.price[frequency as keyof typeof plan.price]}\n                        </span>\n                      )}\n                    </div>\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"grid gap-3 pb-6\">\n                  {plan.features.map((feature, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, x: -5 }}\n                      animate={{ opacity: 1, x: 0 }}\n                      transition={{ duration: 0.3, delay: 0.5 + index * 0.05 }}\n                      className=\"flex items-center gap-2 text-sm\"\n                    >\n                      <div\n                        className={cn(\n                          'flex h-5 w-5 items-center justify-center rounded-full',\n                          plan.popular\n                            ? 'bg-primary/10 text-primary'\n                            : 'bg-secondary text-secondary-foreground',\n                        )}\n                      >\n                        <Check className=\"h-3.5 w-3.5\" />\n                      </div>\n                      <span\n                        className={\n                          plan.popular\n                            ? 'text-foreground'\n                            : 'text-muted-foreground'\n                        }\n                      >\n                        {feature}\n                      </span>\n                    </motion.div>\n                  ))}\n                </CardContent>\n                <CardFooter>\n                  <Button\n                    variant={plan.popular ? 'default' : 'outline'}\n                    className={cn(\n                      'w-full font-medium transition-all duration-300',\n                      plan.popular\n                        ? 'bg-primary hover:bg-primary/90 hover:shadow-primary/20 hover:shadow-md'\n                        : 'hover:border-primary/30 hover:bg-primary/5 hover:text-primary',\n                    )}\n                  >\n                    {plan.cta}\n                    <ArrowRight className=\"ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1\" />\n                  </Button>\n                </CardFooter>\n\n                {/* Subtle gradient effects */}\n                {plan.popular ? (\n                  <>\n                    <div className=\"from-primary/[0.05] pointer-events-none absolute right-0 bottom-0 left-0 h-1/2 rounded-b-lg bg-gradient-to-t to-transparent\" />\n                    <div className=\"border-primary/20 pointer-events-none absolute inset-0 rounded-lg border\" />\n                  </>\n                ) : (\n                  <div className=\"hover:border-primary/10 pointer-events-none absolute inset-0 rounded-lg border border-transparent opacity-0 transition-opacity duration-300 hover:opacity-100\" />\n                )}\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/pricing/simple-pricing.tsx", "target": "components/mvpblocks/simple-pricing.tsx"}]}