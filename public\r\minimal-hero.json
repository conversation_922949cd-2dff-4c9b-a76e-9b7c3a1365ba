{"name": "minimal-hero", "type": "registry:block", "dependencies": ["react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\n\nimport React, { useEffect, useRef } from 'react';\n\nconst colors = {\n  50: '#f8f7f5',\n  100: '#e6e1d7',\n  200: '#c8b4a0',\n  300: '#a89080',\n  400: '#8a7060',\n  500: '#6b5545',\n  600: '#544237',\n  700: '#3c4237',\n  800: '#2a2e26',\n  900: '#1a1d18',\n};\n\nexport default function MinimalHero() {\n  const gradientRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    // Animate words\n    const words = document.querySelectorAll<HTMLElement>('.word');\n    words.forEach((word) => {\n      const delay = parseInt(word.getAttribute('data-delay') || '0', 10);\n      setTimeout(() => {\n        word.style.animation = 'word-appear 0.8s ease-out forwards';\n      }, delay);\n    });\n\n    // Mouse gradient\n    const gradient = gradientRef.current;\n    function onMouseMove(e: MouseEvent) {\n      if (gradient) {\n        gradient.style.left = e.clientX - 192 + 'px';\n        gradient.style.top = e.clientY - 192 + 'px';\n        gradient.style.opacity = '1';\n      }\n    }\n    function onMouseLeave() {\n      if (gradient) gradient.style.opacity = '0';\n    }\n    document.addEventListener('mousemove', onMouseMove);\n    document.addEventListener('mouseleave', onMouseLeave);\n\n    // Word hover effects\n    words.forEach((word) => {\n      word.addEventListener('mouseenter', () => {\n        word.style.textShadow = '0 0 20px rgba(200, 180, 160, 0.5)';\n      });\n      word.addEventListener('mouseleave', () => {\n        word.style.textShadow = 'none';\n      });\n    });\n\n    // Click ripple effect\n    function onClick(e: MouseEvent) {\n      const ripple = document.createElement('div');\n      ripple.style.position = 'fixed';\n      ripple.style.left = e.clientX + 'px';\n      ripple.style.top = e.clientY + 'px';\n      ripple.style.width = '4px';\n      ripple.style.height = '4px';\n      ripple.style.background = 'rgba(200, 180, 160, 0.6)';\n      ripple.style.borderRadius = '50%';\n      ripple.style.transform = 'translate(-50%, -50%)';\n      ripple.style.pointerEvents = 'none';\n      ripple.style.animation = 'pulse-glow 1s ease-out forwards';\n      document.body.appendChild(ripple);\n      setTimeout(() => ripple.remove(), 1000);\n    }\n    document.addEventListener('click', onClick);\n\n    // Floating elements on scroll\n    let scrolled = false;\n    function onScroll() {\n      if (!scrolled) {\n        scrolled = true;\n        document\n          .querySelectorAll<HTMLElement>('.floating-element')\n          .forEach((el, index) => {\n            setTimeout(() => {\n              el.style.animationPlayState = 'running';\n            }, index * 200);\n          });\n      }\n    }\n    window.addEventListener('scroll', onScroll);\n\n    return () => {\n      document.removeEventListener('mousemove', onMouseMove);\n      document.removeEventListener('mouseleave', onMouseLeave);\n      document.removeEventListener('click', onClick);\n      window.removeEventListener('scroll', onScroll);\n    };\n  }, []);\n\n  return (\n    <div className=\"font-primary relative min-h-screen w-full overflow-hidden bg-gradient-to-br from-[#1a1d18] via-black to-[#2a2e26] text-[#e6e1d7]\">\n      <svg\n        className=\"absolute inset-0 h-full w-full\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n      >\n        <defs>\n          <pattern\n            id=\"grid\"\n            width=\"60\"\n            height=\"60\"\n            patternUnits=\"userSpaceOnUse\"\n          >\n            <path\n              d=\"M 60 0 L 0 0 0 60\"\n              fill=\"none\"\n              stroke=\"rgba(200,180,160,0.08)\"\n              strokeWidth=\"0.5\"\n            />\n          </pattern>\n        </defs>\n        <rect width=\"100%\" height=\"100%\" fill=\"url(#grid)\" />\n        <line\n          x1=\"0\"\n          y1=\"20%\"\n          x2=\"100%\"\n          y2=\"20%\"\n          className=\"grid-line\"\n          style={{ animationDelay: '0.5s' }}\n        />\n        <line\n          x1=\"0\"\n          y1=\"80%\"\n          x2=\"100%\"\n          y2=\"80%\"\n          className=\"grid-line\"\n          style={{ animationDelay: '1s' }}\n        />\n        <line\n          x1=\"20%\"\n          y1=\"0\"\n          x2=\"20%\"\n          y2=\"100%\"\n          className=\"grid-line\"\n          style={{ animationDelay: '1.5s' }}\n        />\n        <line\n          x1=\"80%\"\n          y1=\"0\"\n          x2=\"80%\"\n          y2=\"100%\"\n          className=\"grid-line\"\n          style={{ animationDelay: '2s' }}\n        />\n        <line\n          x1=\"50%\"\n          y1=\"0\"\n          x2=\"50%\"\n          y2=\"100%\"\n          className=\"grid-line\"\n          style={{ animationDelay: '2.5s', opacity: 0.05 }}\n        />\n        <line\n          x1=\"0\"\n          y1=\"50%\"\n          x2=\"100%\"\n          y2=\"50%\"\n          className=\"grid-line\"\n          style={{ animationDelay: '3s', opacity: 0.05 }}\n        />\n        <circle\n          cx=\"20%\"\n          cy=\"20%\"\n          r=\"2\"\n          className=\"detail-dot\"\n          style={{ animationDelay: '3s' }}\n        />\n        <circle\n          cx=\"80%\"\n          cy=\"20%\"\n          r=\"2\"\n          className=\"detail-dot\"\n          style={{ animationDelay: '3.2s' }}\n        />\n        <circle\n          cx=\"20%\"\n          cy=\"80%\"\n          r=\"2\"\n          className=\"detail-dot\"\n          style={{ animationDelay: '3.4s' }}\n        />\n        <circle\n          cx=\"80%\"\n          cy=\"80%\"\n          r=\"2\"\n          className=\"detail-dot\"\n          style={{ animationDelay: '3.6s' }}\n        />\n        <circle\n          cx=\"50%\"\n          cy=\"50%\"\n          r=\"1.5\"\n          className=\"detail-dot\"\n          style={{ animationDelay: '4s' }}\n        />\n      </svg>\n\n      {/* Corner elements */}\n      <div\n        className=\"corner-element top-8 left-8\"\n        style={{ animationDelay: '4s' }}\n      >\n        <div\n          className=\"absolute top-0 left-0 h-2 w-2 opacity-30\"\n          style={{ background: colors[200] }}\n        ></div>\n      </div>\n      <div\n        className=\"corner-element top-8 right-8\"\n        style={{ animationDelay: '4.2s' }}\n      >\n        <div\n          className=\"absolute top-0 right-0 h-2 w-2 opacity-30\"\n          style={{ background: colors[200] }}\n        ></div>\n      </div>\n      <div\n        className=\"corner-element bottom-8 left-8\"\n        style={{ animationDelay: '4.4s' }}\n      >\n        <div\n          className=\"absolute bottom-0 left-0 h-2 w-2 opacity-30\"\n          style={{ background: colors[200] }}\n        ></div>\n      </div>\n      <div\n        className=\"corner-element right-8 bottom-8\"\n        style={{ animationDelay: '4.6s' }}\n      >\n        <div\n          className=\"absolute right-0 bottom-0 h-2 w-2 opacity-30\"\n          style={{ background: colors[200] }}\n        ></div>\n      </div>\n\n      {/* Floating elements */}\n      <div\n        className=\"floating-element\"\n        style={{ top: '25%', left: '15%', animationDelay: '5s' }}\n      ></div>\n      <div\n        className=\"floating-element\"\n        style={{ top: '60%', left: '85%', animationDelay: '5.5s' }}\n      ></div>\n      <div\n        className=\"floating-element\"\n        style={{ top: '40%', left: '10%', animationDelay: '6s' }}\n      ></div>\n      <div\n        className=\"floating-element\"\n        style={{ top: '75%', left: '90%', animationDelay: '6.5s' }}\n      ></div>\n\n      <div className=\"relative z-10 flex min-h-screen flex-col items-center justify-between px-8 py-12 md:px-16 md:py-20\">\n        {/* Top tagline */}\n        <div className=\"text-center\">\n          <h2\n            className=\"font-mono text-xs font-light tracking-[0.2em] uppercase opacity-80 md:text-sm\"\n            style={{ color: colors[200] }}\n          >\n            <span className=\"word\" data-delay=\"0\">\n              Welcome\n            </span>\n            <span className=\"word\" data-delay=\"200\">\n              to\n            </span>\n            <span className=\"word\" data-delay=\"400\">\n              <b>StackPilot</b>\n            </span>\n            <span className=\"word\" data-delay=\"600\">\n              — \n            </span>\n            <span className=\"word\" data-delay=\"800\">\n              Powering\n            </span>\n            <span className=\"word\" data-delay=\"1000\">\n              your\n            </span>\n            <span className=\"word\" data-delay=\"1200\">\n              digital\n            </span>\n            <span className=\"word\" data-delay=\"1400\">\n              transformation.\n            </span>\n          </h2>\n          <div\n            className=\"mt-4 h-px w-16 opacity-30\"\n            style={{\n              background: `linear-gradient(to right, transparent, ${colors[200]}, transparent)`,\n            }}\n          ></div>\n        </div>\n\n        {/* Main headline */}\n        <div className=\"mx-auto max-w-5xl text-center\">\n          <h1\n            className=\"text-decoration text-3xl leading-tight font-extralight tracking-tight md:text-5xl lg:text-6xl\"\n            style={{ color: colors[50] }}\n          >\n            <div className=\"mb-4 md:mb-6\">\n              <span className=\"word\" data-delay=\"1600\">\n                Supercharge\n              </span>\n              <span className=\"word\" data-delay=\"1750\">\n                your\n              </span>\n              <span className=\"word\" data-delay=\"1900\">\n                productivity\n              </span>\n              <span className=\"word\" data-delay=\"2050\">\n                with\n              </span>\n              <span className=\"word\" data-delay=\"2200\">\n                AI-driven\n              </span>\n              <span className=\"word\" data-delay=\"2350\">\n                automation.\n              </span>\n            </div>\n            <div\n              className=\"text-2xl leading-relaxed font-thin md:text-3xl lg:text-4xl\"\n              style={{ color: colors[200] }}\n            >\n              <span className=\"word\" data-delay=\"2600\">\n                Integrate,\n              </span>\n              <span className=\"word\" data-delay=\"2750\">\n                orchestrate,\n              </span>\n              <span className=\"word\" data-delay=\"2900\">\n                and\n              </span>\n              <span className=\"word\" data-delay=\"3050\">\n                scale\n              </span>\n              <span className=\"word\" data-delay=\"3200\">\n                your\n              </span>\n              <span className=\"word\" data-delay=\"3350\">\n                business\n              </span>\n              <span className=\"word\" data-delay=\"3500\">\n                — all\n              </span>\n              <span className=\"word\" data-delay=\"3650\">\n                in\n              </span>\n              <span className=\"word\" data-delay=\"3800\">\n                one\n              </span>\n              <span className=\"word\" data-delay=\"3950\">\n                secure\n              </span>\n              <span className=\"word\" data-delay=\"4100\">\n                platform.\n              </span>\n            </div>\n          </h1>\n          <div\n            className=\"absolute top-1/2 -left-8 h-px w-4 opacity-20\"\n            style={{\n              background: colors[200],\n              animation: 'word-appear 1s ease-out forwards',\n              animationDelay: '3.5s',\n            }}\n          ></div>\n          <div\n            className=\"absolute top-1/2 -right-8 h-px w-4 opacity-20\"\n            style={{\n              background: colors[200],\n              animation: 'word-appear 1s ease-out forwards',\n              animationDelay: '3.7s',\n            }}\n          ></div>\n        </div>\n\n        {/* Bottom tagline */}\n        <div className=\"text-center\">\n          <div\n            className=\"mb-4 h-px w-16 opacity-30\"\n            style={{\n              background: `linear-gradient(to right, transparent, ${colors[200]}, transparent)`,\n            }}\n          ></div>\n          <h2\n            className=\"font-mono text-xs font-light tracking-[0.2em] uppercase opacity-80 md:text-sm\"\n            style={{ color: colors[200] }}\n          >\n            <span className=\"word\" data-delay=\"4400\">\n              Real-time\n            </span>\n            <span className=\"word\" data-delay=\"4550\">\n              analytics,\n            </span>\n            <span className=\"word\" data-delay=\"4700\">\n              seamless\n            </span>\n            <span className=\"word\" data-delay=\"4850\">\n              integrations,\n            </span>\n            <span className=\"word\" data-delay=\"5000\">\n              enterprise-grade\n            </span>\n            <span className=\"word\" data-delay=\"5150\">\n              security.\n            </span>\n          </h2>\n          <div\n            className=\"mt-6 flex justify-center space-x-4 opacity-0\"\n            style={{\n              animation: 'word-appear 1s ease-out forwards',\n              animationDelay: '4.5s',\n            }}\n          >\n            <div\n              className=\"h-1 w-1 rounded-full opacity-40\"\n              style={{ background: colors[200] }}\n            ></div>\n            <div\n              className=\"h-1 w-1 rounded-full opacity-60\"\n              style={{ background: colors[200] }}\n            ></div>\n            <div\n              className=\"h-1 w-1 rounded-full opacity-40\"\n              style={{ background: colors[200] }}\n            ></div>\n          </div>\n        </div>\n      </div>\n\n      <div\n        id=\"mouse-gradient\"\n        ref={gradientRef}\n        className=\"pointer-events-none fixed h-96 w-96 rounded-full opacity-0 blur-3xl transition-all duration-500 ease-out\"\n        style={{\n          background: `radial-gradient(circle, ${colors[500]}0D 0%, transparent 100%)`,\n        }}\n      ></div>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/hero/minimal-hero.tsx", "target": "components/mvpblocks/minimal-hero.tsx"}]}