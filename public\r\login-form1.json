{"name": "login-form1", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\nimport { Gith<PERSON>, <PERSON>, <PERSON>Off } from 'lucide-react';\nimport { useState } from 'react';\n\nexport default function LoginForm1() {\n  const [showPassword, setShowPassword] = useState(false);\n  return (\n    <main className=\"bg-background flex min-h-screen w-full flex-col items-center justify-center sm:px-4\">\n      <div className=\"w-full space-y-4 sm:max-w-md\">\n        <div className=\"text-center\">\n          <img src=\"/logo.webp\" width={80} className=\"mx-auto\" />\n          <div className=\"mt-5 space-y-2\">\n            <h3 className=\"text-2xl font-bold sm:text-3xl\">\n              Log in to your account\n            </h3>\n            <p className=\"\">\n              Don&apos;t have an account?{' '}\n              <a\n                href=\"#\"\n                className=\"font-medium text-rose-600 hover:text-rose-500\"\n              >\n                Sign up\n              </a>\n            </p>\n          </div>\n        </div>\n        <div className=\"space-y-6 p-4 py-6 shadow sm:rounded-lg sm:p-6\">\n          <div className=\"grid grid-cols-3 gap-x-3\">\n            <button className=\"hover:bg-secondary active:bg-secondary/40 flex items-center justify-center rounded-lg border py-2.5 duration-150\">\n              <svg\n                className=\"h-5 w-5\"\n                viewBox=\"0 0 48 48\"\n                fill=\"none\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n              >\n                <g clipPath=\"url(#clip0_17_40)\">\n                  <path\n                    d=\"M47.532 24.5528C47.532 22.9214 47.3997 21.2811 47.1175 19.6761H24.48V28.9181H37.4434C36.9055 31.8988 35.177 34.5356 32.6461 36.2111V42.2078H40.3801C44.9217 38.0278 47.532 31.8547 47.532 24.5528Z\"\n                    fill=\"#4285F4\"\n                  />\n                  <path\n                    d=\"M24.48 48.0016C30.9529 48.0016 36.4116 45.8764 40.3888 42.2078L32.6549 36.2111C30.5031 37.675 27.7252 38.5039 24.4888 38.5039C18.2275 38.5039 12.9187 34.2798 11.0139 28.6006H3.03296V34.7825C7.10718 42.8868 15.4056 48.0016 24.48 48.0016Z\"\n                    fill=\"#34A853\"\n                  />\n                  <path\n                    d=\"M11.0051 28.6006C9.99973 25.6199 9.99973 22.3922 11.0051 19.4115V13.2296H3.03298C-0.371021 20.0112 -0.371021 28.0009 3.03298 34.7825L11.0051 28.6006Z\"\n                    fill=\"#FBBC04\"\n                  />\n                  <path\n                    d=\"M24.48 9.49932C27.9016 9.44641 31.2086 10.7339 33.6866 13.0973L40.5387 6.24523C36.2 2.17101 30.4414 -0.068932 24.48 0.00161733C15.4055 0.00161733 7.10718 5.11644 3.03296 13.2296L11.005 19.4115C12.901 13.7235 18.2187 9.49932 24.48 9.49932Z\"\n                    fill=\"#EA4335\"\n                  />\n                </g>\n                <defs>\n                  <clipPath id=\"clip0_17_40\">\n                    <rect width=\"48\" height=\"48\" fill=\"white\" />\n                  </clipPath>\n                </defs>\n              </svg>\n            </button>\n            <button className=\"hover:bg-secondary active:bg-secondary/40 flex items-center justify-center rounded-lg border py-2.5 duration-150\">\n              <svg\n                className=\"h-5 w-5\"\n                viewBox=\"0 0 48 48\"\n                fill=\"none\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n              >\n                <path\n                  d=\"M15.095 43.5014C33.2083 43.5014 43.1155 28.4946 43.1155 15.4809C43.1155 15.0546 43.1155 14.6303 43.0867 14.2079C45.0141 12.8138 46.6778 11.0877 48 9.11033C46.2028 9.90713 44.2961 10.4294 42.3437 10.6598C44.3996 9.42915 45.9383 7.49333 46.6733 5.21273C44.7402 6.35994 42.6253 7.16838 40.4198 7.60313C38.935 6.02428 36.9712 4.97881 34.8324 4.6285C32.6935 4.27818 30.4988 4.64256 28.5879 5.66523C26.677 6.68791 25.1564 8.31187 24.2615 10.2858C23.3665 12.2598 23.1471 14.4737 23.6371 16.5849C19.7218 16.3885 15.8915 15.371 12.3949 13.5983C8.89831 11.8257 5.81353 9.33765 3.3408 6.29561C2.08146 8.4636 1.69574 11.0301 2.2622 13.4725C2.82865 15.9148 4.30468 18.0495 6.38976 19.4418C4.82246 19.3959 3.2893 18.9731 1.92 18.2092V18.334C1.92062 20.6077 2.7077 22.8112 4.14774 24.5707C5.58778 26.3303 7.59212 27.5375 9.8208 27.9878C8.37096 28.3832 6.84975 28.441 5.37408 28.1567C6.00363 30.1134 7.22886 31.8244 8.87848 33.0506C10.5281 34.2768 12.5197 34.9569 14.5747 34.9958C12.5329 36.6007 10.1946 37.7873 7.69375 38.4878C5.19287 39.1882 2.57843 39.3886 0 39.0777C4.50367 41.9677 9.74385 43.5007 15.095 43.4937\"\n                  fill=\"#1DA1F2\"\n                />\n              </svg>\n            </button>\n            <button className=\"hover:bg-secondary active:bg-secondary/40 flex items-center justify-center rounded-lg border py-2.5 duration-150\">\n              <Github size={24} />\n            </button>\n          </div>\n          <div className=\"relative\">\n            <span className=\"bg-secondary block h-px w-full\"></span>\n            <p className=\"absolute inset-x-0 -top-2 mx-auto inline-block w-fit px-2 text-sm\">\n              Or continue with\n            </p>\n          </div>\n          {/* OnSubmit declare yourself */}\n          <form className=\"space-y-5\">\n            <div>\n              <label className=\"font-medium\">Email</label>\n              <input\n                type=\"email\"\n                required\n                className=\"mt-2 w-full rounded-lg border bg-transparent px-3 py-2 shadow-sm outline-none focus:border-rose-600\"\n              />\n            </div>\n            <div className=\"relative\">\n              <label className=\"font-medium\">Password</label>\n              <div className=\"relative\">\n                <input\n                  type={showPassword ? 'text' : 'password'}\n                  required\n                  className=\"mt-2 w-full rounded-lg border bg-transparent px-3 py-2 shadow-sm outline-none focus:border-rose-600\"\n                />\n                <button\n                  type=\"button\"\n                  onClick={() => setShowPassword(!showPassword)}\n                  className=\"absolute inset-y-0 right-0 mt-2 mr-3 flex items-center\"\n                >\n                  {showPassword ? (\n                    <EyeOff size={20} className=\"text-secondary\" />\n                  ) : (\n                    <Eye size={20} className=\"text-secondary\" />\n                  )}\n                </button>\n              </div>\n            </div>\n            <button className=\"w-full rounded-lg bg-rose-600 px-4 py-2 font-medium text-white duration-150 hover:bg-rose-500 active:bg-rose-600\">\n              Sign in\n            </button>\n          </form>\n        </div>\n        <div className=\"text-center\">\n          <a href=\"#\" className=\"hover:text-rose-600\">\n            Forgot password?\n          </a>\n        </div>\n      </div>\n    </main>\n  );\n}\n", "path": "/components/mvpblocks/forms/login-form1.tsx", "target": "components/mvpblocks/login-form1.tsx"}]}