{"name": "conversation1", "type": "registry:block", "dependencies": ["lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/input.json", "https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { useState } from 'react';\nimport { Send, Mic, User, Bot } from 'lucide-react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { cn } from '@/lib/utils';\n\nconst initialMessages = [\n  {\n    id: '1',\n    content:\n      'Hi there! Welcome to our new platform. How can I assist you today?',\n    sender: 'ai',\n    timestamp: new Date(Date.now() - 1000 * 60 * 10).toISOString(),\n  },\n  {\n    id: '2',\n    content:\n      \"Hello! I'm looking for information about your premium subscription plans.\",\n    sender: 'user',\n    timestamp: new Date(Date.now() - 1000 * 60 * 9).toISOString(),\n  },\n  {\n    id: '3',\n    content:\n      \"Great question! Our premium plan offers unlimited access to all features, priority support, and advanced analytics. It's priced at $19.99/month with a 7-day free trial.\",\n    sender: 'ai',\n    timestamp: new Date(Date.now() - 1000 * 60 * 8).toISOString(),\n  },\n  {\n    id: '4',\n    content:\n      'That sounds interesting. Are there any discounts for annual subscriptions?',\n    sender: 'user',\n    timestamp: new Date(Date.now() - 1000 * 60 * 7).toISOString(),\n  },\n  {\n    id: '5',\n    content:\n      'We offer a 20% discount on annual subscriptions, bringing the price down to $191.90 per year, which saves you about $48 compared to the monthly plan.',\n    sender: 'ai',\n    timestamp: new Date(Date.now() - 1000 * 60 * 6).toISOString(),\n  },\n];\n\ninterface ChatBubbleProps {\n  message: string;\n  isUser: boolean;\n  timestamp: Date;\n}\n\nfunction ChatBubble({ message, isUser, timestamp }: ChatBubbleProps) {\n  const formattedTime = timestamp.toLocaleTimeString([], {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: true,\n  });\n\n  return (\n    <div\n      className={cn('flex w-full', isUser ? 'justify-end' : 'justify-start')}\n    >\n      <div\n        className={cn(\n          'flex max-w-[80%] items-start space-x-2',\n          isUser && 'flex-row-reverse space-x-reverse',\n        )}\n      >\n        <div\n          className={cn(\n            'flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full',\n            isUser ? 'bg-primary/10' : 'bg-muted',\n          )}\n        >\n          {isUser ? (\n            <User className=\"text-primary h-4 w-4\" />\n          ) : (\n            <Bot className=\"text-muted-foreground h-4 w-4\" />\n          )}\n        </div>\n\n        <div className=\"flex flex-col\">\n          <div\n            className={cn(\n              'rounded-2xl px-4 py-2 shadow-sm',\n              isUser\n                ? 'bg-primary text-primary-foreground rounded-tr-none'\n                : 'border-border bg-card text-card-foreground rounded-tl-none border',\n            )}\n          >\n            <p className=\"whitespace-pre-wrap\">{message}</p>\n          </div>\n\n          <span\n            className={cn(\n              'text-muted-foreground mt-1 text-xs',\n              isUser ? 'text-right' : 'text-left',\n            )}\n          >\n            {formattedTime}\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default function Conversation1() {\n  const [messages, setMessages] = useState(initialMessages);\n  const [input, setInput] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n\n  const handleSendMessage = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!input.trim()) return;\n    const userMessage = {\n      id: Date.now().toString(),\n      content: input,\n      sender: 'user',\n      timestamp: new Date().toISOString(),\n    };\n\n    setMessages((prev) => [...prev, userMessage]);\n    setInput('');\n    setIsTyping(true);\n    setTimeout(() => {\n      const aiMessage = {\n        id: (Date.now() + 1).toString(),\n        content:\n          \"Thanks for your message! I'm here to help with any other questions you might have about our services or features.\",\n        sender: 'ai',\n        timestamp: new Date().toISOString(),\n      };\n\n      setMessages((prev) => [...prev, aiMessage]);\n      setIsTyping(false);\n    }, 1500);\n  };\n\n  return (\n    <main className=\"bg-background flex min-h-screen flex-col items-center justify-center p-4\">\n      <div className=\"border-border bg-card w-full max-w-2xl overflow-hidden rounded-xl border shadow-lg\">\n        <div className=\"bg-primary p-4\">\n          <h1 className=\"text-primary-foreground text-lg font-semibold\">\n            AI Assistant\n          </h1>\n          <p className=\"text-primary-foreground/80 text-sm\">\n            Always here to help you\n          </p>\n        </div>\n\n        <div className=\"flex h-[600px] flex-col\">\n          <div className=\"flex-1 space-y-4 overflow-y-auto p-4\">\n            {messages.map((message) => (\n              <ChatBubble\n                key={message.id}\n                message={message.content}\n                isUser={message.sender === 'user'}\n                timestamp={new Date(message.timestamp)}\n              />\n            ))}\n\n            {isTyping && (\n              <div className=\"text-muted-foreground flex items-center space-x-2 text-sm\">\n                <div className=\"flex space-x-1\">\n                  <div\n                    className=\"bg-muted-foreground/70 h-2 w-2 animate-bounce rounded-full\"\n                    style={{ animationDelay: '0ms' }}\n                  ></div>\n                  <div\n                    className=\"bg-muted-foreground/70 h-2 w-2 animate-bounce rounded-full\"\n                    style={{ animationDelay: '150ms' }}\n                  ></div>\n                  <div\n                    className=\"bg-muted-foreground/70 h-2 w-2 animate-bounce rounded-full\"\n                    style={{ animationDelay: '300ms' }}\n                  ></div>\n                </div>\n                <span>AI is typing...</span>\n              </div>\n            )}\n          </div>\n\n          <div className=\"border-border border-t p-4\">\n            <form onSubmit={handleSendMessage} className=\"flex space-x-2\">\n              <Input\n                value={input}\n                onChange={(e) => setInput(e.target.value)}\n                placeholder=\"Type your message...\"\n                className=\"flex-1\"\n              />\n              <Button\n                type=\"submit\"\n                size=\"icon\"\n                className=\"bg-primary hover:bg-primary/90\"\n              >\n                <Send className=\"h-4 w-4\" />\n              </Button>\n              <Button type=\"button\" size=\"icon\" variant=\"outline\">\n                <Mic className=\"h-4 w-4\" />\n              </Button>\n            </form>\n          </div>\n        </div>\n      </div>\n    </main>\n  );\n}\n", "path": "/components/mvpblocks/chatbot-ui/conversation1.tsx", "target": "components/mvpblocks/conversation1.tsx"}]}