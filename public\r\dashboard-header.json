{"name": "dashboard-header", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/input.json", "https://blocks.mvp-subha.me/r/sidebar.json", "https://blocks.mvp-subha.me/r/separator.json", "https://blocks.mvp-subha.me/r/sheet.json", "https://blocks.mvp-subha.me/r/skeleton.json", "https://blocks.mvp-subha.me/r/tooltip.json", "https://blocks.mvp-subha.me/r/use-mobile.json", "https://blocks.mvp-subha.me/r/dropdown-menu.json", "https://blocks.mvp-subha.me/r/breadcrumb.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { memo } from 'react';\nimport { motion } from 'framer-motion';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { SidebarTrigger } from '@/components/ui/sidebar';\nimport { Separator } from '@/components/ui/separator';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport {\n  Breadcrumb,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbList,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n} from '@/components/ui/breadcrumb';\nimport {\n  Bell,\n  Search,\n  Filter,\n  Download,\n  RefreshCw,\n  MoreHorizontal,\n} from 'lucide-react';\n\ninterface DashboardHeaderProps {\n  searchQuery: string;\n  onSearchChange: (value: string) => void;\n  onRefresh: () => void;\n  onExport: () => void;\n  isRefreshing: boolean;\n}\n\nexport const DashboardHeader = memo(\n  ({\n    searchQuery,\n    onSearchChange,\n    onRefresh,\n    onExport,\n    isRefreshing,\n  }: DashboardHeaderProps) => {\n    return (\n      <header className=\"bg-background/95 sticky top-0 z-50 flex h-16 w-full shrink-0 items-center gap-2 border-b backdrop-blur transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12\">\n        <div className=\"flex items-center gap-2 px-4\">\n          <SidebarTrigger className=\"-ml-1\" />\n          <Separator orientation=\"vertical\" className=\"mr-2 h-4\" />\n          <Breadcrumb>\n            <BreadcrumbList>\n              <BreadcrumbItem className=\"hidden md:block\">\n                <BreadcrumbLink href=\"#\">Home</BreadcrumbLink>\n              </BreadcrumbItem>\n            </BreadcrumbList>\n          </Breadcrumb>\n        </div>\n\n        <div className=\"ml-auto flex items-center gap-2 px-4\">\n          <motion.div\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            className=\"flex items-center gap-2\"\n          >\n            {/* Search Input - Hide on Mobile */}\n            <div className=\"relative hidden md:block\">\n              <Search className=\"text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform\" />\n              <Input\n                placeholder=\"Search...\"\n                value={searchQuery}\n                onChange={(e) => onSearchChange(e.target.value)}\n                className=\"w-64 pl-10\"\n              />\n            </div>\n\n            {/* Desktop Actions */}\n            <div className=\"hidden items-center gap-2 md:flex\">\n              <Button variant=\"outline\" size=\"sm\">\n                <Filter className=\"mr-2 h-4 w-4\" />\n                Filter\n              </Button>\n\n              <Button variant=\"outline\" size=\"sm\" onClick={onExport}>\n                <Download className=\"mr-2 h-4 w-4\" />\n                Export\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={onRefresh}\n                disabled={isRefreshing}\n              >\n                <RefreshCw\n                  className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`}\n                />\n                Refresh\n              </Button>\n            </div>\n\n            {/* Mobile Menu */}\n            <DropdownMenu>\n              <DropdownMenuTrigger asChild className=\"md:hidden\">\n                <Button variant=\"outline\" size=\"icon\">\n                  <MoreHorizontal className=\"h-4 w-4\" />\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent align=\"end\" className=\"w-48\">\n                <DropdownMenuItem onClick={() => onSearchChange('')}>\n                  <Search className=\"mr-2 h-4 w-4\" />\n                  Search\n                </DropdownMenuItem>\n                <DropdownMenuItem>\n                  <Filter className=\"mr-2 h-4 w-4\" />\n                  Filter\n                </DropdownMenuItem>\n                <DropdownMenuItem onClick={onExport}>\n                  <Download className=\"mr-2 h-4 w-4\" />\n                  Export\n                </DropdownMenuItem>\n                <DropdownMenuItem onClick={onRefresh}>\n                  <RefreshCw className=\"mr-2 h-4 w-4\" />\n                  Refresh\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n\n            <Button variant=\"outline\" size=\"sm\">\n              <Bell className=\"h-4 w-4\" />\n            </Button>\n          </motion.div>\n        </div>\n      </header>\n    );\n  },\n);\n\nDashboardHeader.displayName = 'DashboardHeader';\n", "path": "/components/mvpblocks/dashboards/admin-dashboard-1/ui/dashboard-header.tsx", "target": "components/mvpblocks/ui/dashboard-header.tsx"}]}