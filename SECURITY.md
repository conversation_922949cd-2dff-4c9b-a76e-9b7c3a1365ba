# Security Policy for MVPBlocks

## Supported Versions

Currently, MVPBlocks does not follow versioned security support. We recommend always using the latest release to ensure you have the latest security updates.

## Reporting a Vulnerability

We take security seriously. If you discover a vulnerability in MVPBlocks, please report it promptly.

**How to report:**

- Email us at: [<EMAIL>](mailto:<EMAIL>)  
- Or reach out via Twitter: [@mvp_Subha](https://twitter.com/mvp_Subha)

Please include as much detail as possible, such as:

- Steps to reproduce the issue  
- Impact of the vulnerability  
- Any relevant code snippets or screenshots  

**What to expect:**

- Acknowledgment of your report within 48 hours  
- Regular updates on the investigation and resolution  
- Coordination on disclosure timing if confirmed  
- Public advisory upon fixing (if applicable)  
- Explanation if the vulnerability is declined  

We appreciate responsible disclosure and will credit contributors who report valid security issues.
