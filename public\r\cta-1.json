{"name": "cta-1", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "import { Globe, Mail, Phone } from 'lucide-react';\nimport Link from 'next/link';\n\nexport default function CTA1() {\n  return (\n    <div className=\"w-full\">\n      <section className=\"mx-auto max-w-7xl px-4 py-6 lg:px-8 lg:py-20\">\n        <div\n          className=\"relative isolate w-full overflow-hidden rounded-2xl\"\n          style={{\n            background:\n              'linear-gradient(100.5deg,rgba(57,18,241,.4) 29.55%,rgba(164,129,255,.4) 93.8%),radial-gradient(38.35% 93.72% at 18.31% 6.28%,rgba(170,135,252,.8) 0,rgba(61,27,205,.8) 100%)',\n          }}\n        >\n          <img\n            alt=\"bg\"\n            loading=\"lazy\"\n            width=\"1840\"\n            height=\"694\"\n            className=\"absolute top-0\"\n            src=\"https://blocks.mvp-subha.me/assets/cta/grid.svg\"\n          />\n          <div className=\"relative isolate overflow-hidden px-4 py-12 sm:px-24\">\n            <p className=\"w-fit rounded-xl bg-white px-4 py-1 text-center text-base leading-7 font-semibold text-black uppercase lg:text-left\">\n              Get in touch\n            </p>\n            <h2 className=\"mt-3 max-w-md text-4xl font-semibold text-white md:text-6xl\">\n              How Can You <span className=\"text-primary-2\"> Reach Us</span>?\n            </h2>\n            <p className=\"my-auto mt-3 max-w-2xl text-base text-gray-300 md:text-lg\">\n              If you need to get in touch, there are several ways to contact us.\n            </p>\n            <div className=\"mt-8 flex w-full flex-col justify-between gap-4 text-lg md:flex-row\">\n              <a\n                className=\"flex items-center gap-2 text-white\"\n                href=\"mailto:<EMAIL>\"\n              >\n                <Mail className=\"h-7 w-7 text-red-500\" />\n                <EMAIL>\n              </a>\n              <a className=\"flex items-center gap-2 text-white\" href=\"#\">\n                <Phone className=\"h-7 w-7 text-green-500\" />\n                +91-8637373116\n              </a>\n              <Link className=\"flex items-center gap-2 text-white\" href=\"/\">\n                <Globe className=\"h-7 w-7 text-blue-500\" />\n                mvp-subha.me\n              </Link>\n            </div>\n            <ul className=\"mt-8 ml-4 list-disc text-sm text-gray-300 md:text-base\">\n              <li>Submit your query and state your requirements.</li>\n              <li>\n                Receive a call back from our experts as per your query to help\n                for your need.\n              </li>\n            </ul>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/cta/cta-1.tsx", "target": "components/mvpblocks/cta-1.tsx"}]}