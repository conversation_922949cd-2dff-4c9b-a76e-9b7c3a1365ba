{"name": "pulsating-loader", "type": "registry:block", "dependencies": ["framer-motion"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "import { motion } from 'framer-motion';\n\nexport default function PulsatingDots() {\n  return (\n    <div className=\"flex items-center justify-center\">\n      <div className=\"flex space-x-2\">\n        <motion.div\n          className=\"h-3 w-3 rounded-full bg-red-500\"\n          animate={{\n            scale: [1, 1.5, 1],\n            opacity: [0.5, 1, 0.5],\n          }}\n          transition={{\n            duration: 1,\n            ease: 'easeInOut',\n            repeat: Infinity,\n          }}\n        />\n        <motion.div\n          className=\"h-3 w-3 rounded-full bg-red-500\"\n          animate={{\n            scale: [1, 1.5, 1],\n            opacity: [0.5, 1, 0.5],\n          }}\n          transition={{\n            duration: 1,\n            ease: 'easeInOut',\n            repeat: Infinity,\n            delay: 0.3,\n          }}\n        />\n        <motion.div\n          className=\"h-3 w-3 rounded-full bg-red-500\"\n          animate={{\n            scale: [1, 1.5, 1],\n            opacity: [0.5, 1, 0.5],\n          }}\n          transition={{\n            duration: 1,\n            ease: 'easeInOut',\n            repeat: Infinity,\n            delay: 0.6,\n          }}\n        />\n      </div>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/basics/loaders/pulsating-loader.tsx", "target": "components/mvpblocks/pulsating-loader.tsx"}]}