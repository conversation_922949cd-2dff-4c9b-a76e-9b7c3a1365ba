{"name": "footer-newsletter", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { Instagram, Linkedin, Twitter, Youtube } from 'lucide-react';\n\nconst footerColumns = [\n  {\n    title: 'Solutions',\n    links: [\n      'Business Automation',\n      'Cloud Services',\n      'Analytics',\n      'Integrations',\n      'Support',\n    ],\n  },\n  {\n    title: 'Resources',\n    links: ['Documentation', 'Case Studies', 'Blog', 'Webinars', 'Community'],\n  },\n  {\n    title: 'Company',\n    links: ['About Us', 'Careers', 'Contact', 'Partners', 'Press'],\n  },\n];\n\nconst legalLinks = [\n  'Terms of Service',\n  'Privacy Policy',\n  'Cookie Settings',\n  'Accessibility',\n];\n\nconst socialIcons = [\n  { icon: <Instagram className=\"h-5 w-5\" />, href: '#' },\n  { icon: <Twitter className=\"h-5 w-5\" />, href: '#' },\n  { icon: <Linkedin className=\"h-5 w-5\" />, href: '#' },\n  { icon: <Youtube className=\"h-5 w-5\" />, href: '#' },\n];\n\nexport default function FooterNewsletter() {\n  return (\n    <footer className=\"bg-background text-foreground relative w-full pt-20 pb-10\">\n      <div className=\"pointer-events-none absolute top-0 left-0 z-0 h-full w-full overflow-hidden\">\n        <div className=\"bg-primary absolute top-1/3 left-1/4 h-64 w-64 rounded-full opacity-10 blur-3xl\" />\n        <div className=\"bg-primary absolute right-1/4 bottom-1/4 h-80 w-80 rounded-full opacity-10 blur-3xl\" />\n      </div>\n      <div className=\"relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"glass-effect mb-16 rounded-2xl p-8 md:p-12\">\n          <div className=\"grid items-center gap-8 md:grid-cols-2\">\n            <div>\n              <h3 className=\"mb-4 text-2xl font-bold md:text-3xl\">\n                Stay ahead with Acme Inc.\n              </h3>\n              <p className=\"text-foreground/70 mb-6\">\n                Join thousands of professionals who trust Acme Inc. for\n                innovative business solutions.\n              </p>\n              <div className=\"flex flex-col gap-4 sm:flex-row\">\n                <input\n                  type=\"email\"\n                  placeholder=\"Enter your email\"\n                  className=\"border-foreground/20 bg-background focus:ring-primary rounded-lg border px-4 py-3 focus:ring-2 focus:outline-none\"\n                />\n                <button className=\"bg-primary text-primary-foreground shadow-primary/20 hover:shadow-primary/30 rounded-lg px-6 py-3 font-medium shadow-lg transition\">\n                  Subscribe Now\n                </button>\n              </div>\n            </div>\n            <div className=\"hidden justify-end md:flex\">\n              <div className=\"relative\">\n                <div className=\"bg-primary/20 absolute inset-0 rotate-6 rounded-xl\" />\n                <img\n                  src=\"https://images.unsplash.com/photo-1506744038136-46273834b3fb?ixlib=rb-4.0.3&auto=format&fit=crop&w=320&h=240&q=80\"\n                  alt=\"Acme Inc. team\"\n                  className=\"relative w-80 rounded-xl object-cover\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n        <div className=\"mb-16 grid grid-cols-2 gap-8 md:grid-cols-4 lg:grid-cols-5\">\n          <div className=\"col-span-2 lg:col-span-1\">\n            <div className=\"mb-6 flex items-center space-x-2\">\n              <div className=\"bg-primary flex h-10 w-10 items-center justify-center rounded-full\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  className=\"text-primary-foreground h-6 w-6\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M13 10V3L4 14h7v7l9-11h-7z\"\n                  />\n                </svg>\n              </div>\n              <span className=\"text-xl font-bold\">Acme Inc.</span>\n            </div>\n            <p className=\"text-foreground/60 mb-6\">\n              Empowering businesses with reliable, scalable, and innovative\n              solutions.\n            </p>\n            <div className=\"flex space-x-4\">\n              {socialIcons.map((item, i) => (\n                <a\n                  key={i}\n                  href={item.href}\n                  className=\"glass-effect hover:bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full transition\"\n                >\n                  {item.icon}\n                </a>\n              ))}\n            </div>\n          </div>\n          {footerColumns.map((col) => (\n            <div key={col.title}>\n              <h4 className=\"mb-4 text-lg font-semibold\">{col.title}</h4>\n              <ul className=\"space-y-3\">\n                {col.links.map((text) => (\n                  <li key={text}>\n                    <a\n                      href=\"#\"\n                      className=\"text-foreground/60 hover:text-foreground transition\"\n                    >\n                      {text}\n                    </a>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </div>\n        <div className=\"border-foreground/10 flex flex-col items-center justify-between border-t pt-8 md:flex-row\">\n          <p className=\"text-foreground/60 mb-4 text-sm md:mb-0\">\n            © 2023 Acme Inc. All rights reserved.\n          </p>\n          <div className=\"flex flex-wrap justify-center gap-6\">\n            {legalLinks.map((text) => (\n              <a\n                key={text}\n                href=\"#\"\n                className=\"text-foreground/60 hover:text-foreground text-sm\"\n              >\n                {text}\n              </a>\n            ))}\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n", "path": "/components/mvpblocks/required/footers/footer-newsletter.tsx", "target": "components/mvpblocks/footer-newsletter.tsx"}]}