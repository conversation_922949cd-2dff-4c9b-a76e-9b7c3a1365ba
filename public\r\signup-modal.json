{"name": "signup-modal", "type": "registry:block", "dependencies": ["react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/dialog.json", "https://blocks.mvp-subha.me/r/input.json", "https://blocks.mvp-subha.me/r/label.json"], "files": [{"type": "registry:block", "content": "import { useId } from 'react';\nimport { Button } from '@/components/ui/button';\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n  DialogTrigger,\n} from '@/components/ui/dialog';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\n\nexport default function SignupModal() {\n  const id = useId();\n  return (\n    <Dialog>\n      <DialogTrigger asChild>\n        <Button variant=\"secondary\">Sign up</Button>\n      </DialogTrigger>\n      <DialogContent>\n        <div className=\"flex flex-col items-center gap-2\">\n          <div\n            className=\"flex size-11 shrink-0 items-center justify-center rounded-full border\"\n            aria-hidden=\"true\"\n          >\n            <img src=\"/logo.webp\" alt=\"logo\" className=\"h-8 w-8 rounded-full\" />\n          </div>\n          <DialogHeader>\n            <DialogTitle className=\"sm:text-center\">\n              Sign up to Mvpblocks\n            </DialogTitle>\n            <DialogDescription className=\"sm:text-center\">\n              We just need a few details to get you started.\n            </DialogDescription>\n          </DialogHeader>\n        </div>\n\n        <form className=\"space-y-5\">\n          <div className=\"space-y-4\">\n            <div className=\"*:not-first:mt-2\">\n              <Label htmlFor={`${id}-name`}>Full name</Label>\n              <Input\n                id={`${id}-name`}\n                placeholder=\"Subhadeep Roy\"\n                type=\"text\"\n                required\n              />\n            </div>\n            <div className=\"*:not-first:mt-2\">\n              <Label htmlFor={`${id}-email`}>Email</Label>\n              <Input\n                id={`${id}-email`}\n                placeholder=\"<EMAIL>\"\n                type=\"email\"\n                required\n              />\n            </div>\n            <div className=\"*:not-first:mt-2\">\n              <Label htmlFor={`${id}-password`}>Password</Label>\n              <Input\n                id={`${id}-password`}\n                placeholder=\"Enter your password\"\n                type=\"password\"\n                required\n              />\n            </div>\n          </div>\n          <Button type=\"button\" className=\"w-full\">\n            Sign up\n          </Button>\n        </form>\n\n        <div className=\"before:bg-border after:bg-border flex items-center gap-3 before:h-px before:flex-1 after:h-px after:flex-1\">\n          <span className=\"text-muted-foreground text-xs\">Or</span>\n        </div>\n\n        <Button variant=\"outline\">Continue with Google</Button>\n\n        <p className=\"text-muted-foreground text-center text-xs\">\n          By signing up you agree to our{' '}\n          <a className=\"underline hover:no-underline\" href=\"#\">\n            Terms\n          </a>\n          .\n        </p>\n      </DialogContent>\n    </Dialog>\n  );\n}\n", "path": "/components/mvpblocks/basics/modals/signup-modal.tsx", "target": "components/mvpblocks/signup-modal.tsx"}]}