// Auto-generated file - Do not edit manually
// Last updated: 2025-08-11T19:14:26.667Z

export const githubData = {
  "contributors": [
    {
      "login": "subhadeeproy3902",
      "id": 111780029,
      "node_id": "U_kgDOBqmgvQ",
      "avatar_url": "https://avatars.githubusercontent.com/u/111780029?v=4",
      "gravatar_id": "",
      "url": "https://api.github.com/users/subhadeeproy3902",
      "html_url": "https://github.com/subhadeeproy3902",
      "followers_url": "https://api.github.com/users/subhadeeproy3902/followers",
      "following_url": "https://api.github.com/users/subhadeeproy3902/following{/other_user}",
      "gists_url": "https://api.github.com/users/subhadeeproy3902/gists{/gist_id}",
      "starred_url": "https://api.github.com/users/subhadeeproy3902/starred{/owner}{/repo}",
      "subscriptions_url": "https://api.github.com/users/subhadeeproy3902/subscriptions",
      "organizations_url": "https://api.github.com/users/subhadeeproy3902/orgs",
      "repos_url": "https://api.github.com/users/subhadeeproy3902/repos",
      "events_url": "https://api.github.com/users/subhadeeproy3902/events{/privacy}",
      "received_events_url": "https://api.github.com/users/subhadeeproy3902/received_events",
      "type": "User",
      "user_view_type": "public",
      "site_admin": false,
      "contributions": 594,
      "name": "Subhadeep Roy",
      "bio": "Full Stack Web dev | 700+ Leetcode solver | Creative thinking 👽 | Loves chess♟️| CSE'26 🔥 | Aspiring SDE"
    },
    {
      "login": "mosespace",
      "id": 111237257,
      "node_id": "U_kgDOBqFYiQ",
      "avatar_url": "https://avatars.githubusercontent.com/u/111237257?v=4",
      "gravatar_id": "",
      "url": "https://api.github.com/users/mosespace",
      "html_url": "https://github.com/mosespace",
      "followers_url": "https://api.github.com/users/mosespace/followers",
      "following_url": "https://api.github.com/users/mosespace/following{/other_user}",
      "gists_url": "https://api.github.com/users/mosespace/gists{/gist_id}",
      "starred_url": "https://api.github.com/users/mosespace/starred{/owner}{/repo}",
      "subscriptions_url": "https://api.github.com/users/mosespace/subscriptions",
      "organizations_url": "https://api.github.com/users/mosespace/orgs",
      "repos_url": "https://api.github.com/users/mosespace/repos",
      "events_url": "https://api.github.com/users/mosespace/events{/privacy}",
      "received_events_url": "https://api.github.com/users/mosespace/received_events",
      "type": "User",
      "user_view_type": "public",
      "site_admin": false,
      "contributions": 14,
      "name": "Uncle Moses",
      "bio": "Shipping Code World Wide from London-(UK) 24/7 ✈️🌐🌍"
    },
    {
      "login": "Xeven777",
      "id": 115650165,
      "node_id": "U_kgDOBuSudQ",
      "avatar_url": "https://avatars.githubusercontent.com/u/115650165?v=4",
      "gravatar_id": "",
      "url": "https://api.github.com/users/Xeven777",
      "html_url": "https://github.com/Xeven777",
      "followers_url": "https://api.github.com/users/Xeven777/followers",
      "following_url": "https://api.github.com/users/Xeven777/following{/other_user}",
      "gists_url": "https://api.github.com/users/Xeven777/gists{/gist_id}",
      "starred_url": "https://api.github.com/users/Xeven777/starred{/owner}{/repo}",
      "subscriptions_url": "https://api.github.com/users/Xeven777/subscriptions",
      "organizations_url": "https://api.github.com/users/Xeven777/orgs",
      "repos_url": "https://api.github.com/users/Xeven777/repos",
      "events_url": "https://api.github.com/users/Xeven777/events{/privacy}",
      "received_events_url": "https://api.github.com/users/Xeven777/received_events",
      "type": "User",
      "user_view_type": "public",
      "site_admin": false,
      "contributions": 13,
      "name": "Anish",
      "bio": "♦️Learning.  Growing. Blooming✨ | Passionate developer with a love for problem-solving , efficient and creative thinking 🔥💫"
    },
    {
      "login": "midhunkalarikkal",
      "id": 91378594,
      "node_id": "MDQ6VXNlcjkxMzc4NTk0",
      "avatar_url": "https://avatars.githubusercontent.com/u/91378594?v=4",
      "gravatar_id": "",
      "url": "https://api.github.com/users/midhunkalarikkal",
      "html_url": "https://github.com/midhunkalarikkal",
      "followers_url": "https://api.github.com/users/midhunkalarikkal/followers",
      "following_url": "https://api.github.com/users/midhunkalarikkal/following{/other_user}",
      "gists_url": "https://api.github.com/users/midhunkalarikkal/gists{/gist_id}",
      "starred_url": "https://api.github.com/users/midhunkalarikkal/starred{/owner}{/repo}",
      "subscriptions_url": "https://api.github.com/users/midhunkalarikkal/subscriptions",
      "organizations_url": "https://api.github.com/users/midhunkalarikkal/orgs",
      "repos_url": "https://api.github.com/users/midhunkalarikkal/repos",
      "events_url": "https://api.github.com/users/midhunkalarikkal/events{/privacy}",
      "received_events_url": "https://api.github.com/users/midhunkalarikkal/received_events",
      "type": "User",
      "user_view_type": "public",
      "site_admin": false,
      "contributions": 12,
      "name": "Midhun K Paniker",
      "bio": "\"🚀 Full-Stack Developer | 💻 Passionate about coding and building real-world projects | 📚 Always learning, growing, and sharing knowledge.\""
    },
    {
      "login": "nuelst",
      "id": 79723710,
      "node_id": "MDQ6VXNlcjc5NzIzNzEw",
      "avatar_url": "https://avatars.githubusercontent.com/u/79723710?v=4",
      "gravatar_id": "",
      "url": "https://api.github.com/users/nuelst",
      "html_url": "https://github.com/nuelst",
      "followers_url": "https://api.github.com/users/nuelst/followers",
      "following_url": "https://api.github.com/users/nuelst/following{/other_user}",
      "gists_url": "https://api.github.com/users/nuelst/gists{/gist_id}",
      "starred_url": "https://api.github.com/users/nuelst/starred{/owner}{/repo}",
      "subscriptions_url": "https://api.github.com/users/nuelst/subscriptions",
      "organizations_url": "https://api.github.com/users/nuelst/orgs",
      "repos_url": "https://api.github.com/users/nuelst/repos",
      "events_url": "https://api.github.com/users/nuelst/events{/privacy}",
      "received_events_url": "https://api.github.com/users/nuelst/received_events",
      "type": "User",
      "user_view_type": "public",
      "site_admin": false,
      "contributions": 8,
      "name": "manuel",
      "bio": ""
    },
    {
      "login": "mohit15-web",
      "id": 125914620,
      "node_id": "U_kgDOB4FN_A",
      "avatar_url": "https://avatars.githubusercontent.com/u/125914620?v=4",
      "gravatar_id": "",
      "url": "https://api.github.com/users/mohit15-web",
      "html_url": "https://github.com/mohit15-web",
      "followers_url": "https://api.github.com/users/mohit15-web/followers",
      "following_url": "https://api.github.com/users/mohit15-web/following{/other_user}",
      "gists_url": "https://api.github.com/users/mohit15-web/gists{/gist_id}",
      "starred_url": "https://api.github.com/users/mohit15-web/starred{/owner}{/repo}",
      "subscriptions_url": "https://api.github.com/users/mohit15-web/subscriptions",
      "organizations_url": "https://api.github.com/users/mohit15-web/orgs",
      "repos_url": "https://api.github.com/users/mohit15-web/repos",
      "events_url": "https://api.github.com/users/mohit15-web/events{/privacy}",
      "received_events_url": "https://api.github.com/users/mohit15-web/received_events",
      "type": "User",
      "user_view_type": "public",
      "site_admin": false,
      "contributions": 6,
      "name": "MohitChaudhari",
      "bio": "Full-Stack Developer"
    },
    {
      "login": "AbSrDUO1",
      "id": 163032126,
      "node_id": "U_kgDOCbesPg",
      "avatar_url": "https://avatars.githubusercontent.com/u/163032126?v=4",
      "gravatar_id": "",
      "url": "https://api.github.com/users/AbSrDUO1",
      "html_url": "https://github.com/AbSrDUO1",
      "followers_url": "https://api.github.com/users/AbSrDUO1/followers",
      "following_url": "https://api.github.com/users/AbSrDUO1/following{/other_user}",
      "gists_url": "https://api.github.com/users/AbSrDUO1/gists{/gist_id}",
      "starred_url": "https://api.github.com/users/AbSrDUO1/starred{/owner}{/repo}",
      "subscriptions_url": "https://api.github.com/users/AbSrDUO1/subscriptions",
      "organizations_url": "https://api.github.com/users/AbSrDUO1/orgs",
      "repos_url": "https://api.github.com/users/AbSrDUO1/repos",
      "events_url": "https://api.github.com/users/AbSrDUO1/events{/privacy}",
      "received_events_url": "https://api.github.com/users/AbSrDUO1/received_events",
      "type": "User",
      "user_view_type": "public",
      "site_admin": false,
      "contributions": 4,
      "name": "AbSrDUO1",
      "bio": ""
    },
    {
      "login": "ImgBotApp",
      "id": 31427850,
      "node_id": "MDQ6VXNlcjMxNDI3ODUw",
      "avatar_url": "https://avatars.githubusercontent.com/u/31427850?v=4",
      "gravatar_id": "",
      "url": "https://api.github.com/users/ImgBotApp",
      "html_url": "https://github.com/ImgBotApp",
      "followers_url": "https://api.github.com/users/ImgBotApp/followers",
      "following_url": "https://api.github.com/users/ImgBotApp/following{/other_user}",
      "gists_url": "https://api.github.com/users/ImgBotApp/gists{/gist_id}",
      "starred_url": "https://api.github.com/users/ImgBotApp/starred{/owner}{/repo}",
      "subscriptions_url": "https://api.github.com/users/ImgBotApp/subscriptions",
      "organizations_url": "https://api.github.com/users/ImgBotApp/orgs",
      "repos_url": "https://api.github.com/users/ImgBotApp/repos",
      "events_url": "https://api.github.com/users/ImgBotApp/events{/privacy}",
      "received_events_url": "https://api.github.com/users/ImgBotApp/received_events",
      "type": "User",
      "user_view_type": "public",
      "site_admin": false,
      "contributions": 4,
      "name": "Imgbot",
      "bio": "I commit for Imgbot https://imgbot.net"
    },
    {
      "login": "najeh159",
      "id": 162033142,
      "node_id": "U_kgDOCaht9g",
      "avatar_url": "https://avatars.githubusercontent.com/u/162033142?v=4",
      "gravatar_id": "",
      "url": "https://api.github.com/users/najeh159",
      "html_url": "https://github.com/najeh159",
      "followers_url": "https://api.github.com/users/najeh159/followers",
      "following_url": "https://api.github.com/users/najeh159/following{/other_user}",
      "gists_url": "https://api.github.com/users/najeh159/gists{/gist_id}",
      "starred_url": "https://api.github.com/users/najeh159/starred{/owner}{/repo}",
      "subscriptions_url": "https://api.github.com/users/najeh159/subscriptions",
      "organizations_url": "https://api.github.com/users/najeh159/orgs",
      "repos_url": "https://api.github.com/users/najeh159/repos",
      "events_url": "https://api.github.com/users/najeh159/events{/privacy}",
      "received_events_url": "https://api.github.com/users/najeh159/received_events",
      "type": "User",
      "user_view_type": "public",
      "site_admin": false,
      "contributions": 4,
      "name": "najeh zayoud",
      "bio": ""
    },
    {
      "login": "smalakargh",
      "id": 171774147,
      "node_id": "U_kgDOCj0Qww",
      "avatar_url": "https://avatars.githubusercontent.com/u/171774147?v=4",
      "gravatar_id": "",
      "url": "https://api.github.com/users/smalakargh",
      "html_url": "https://github.com/smalakargh",
      "followers_url": "https://api.github.com/users/smalakargh/followers",
      "following_url": "https://api.github.com/users/smalakargh/following{/other_user}",
      "gists_url": "https://api.github.com/users/smalakargh/gists{/gist_id}",
      "starred_url": "https://api.github.com/users/smalakargh/starred{/owner}{/repo}",
      "subscriptions_url": "https://api.github.com/users/smalakargh/subscriptions",
      "organizations_url": "https://api.github.com/users/smalakargh/orgs",
      "repos_url": "https://api.github.com/users/smalakargh/repos",
      "events_url": "https://api.github.com/users/smalakargh/events{/privacy}",
      "received_events_url": "https://api.github.com/users/smalakargh/received_events",
      "type": "User",
      "user_view_type": "public",
      "site_admin": false,
      "contributions": 4,
      "name": "Supriyo Malakar",
      "bio": "Developer 🔥|| TypeScript || JavaScript 🚀 || Python 🤖 || Next.JS || React.js 🕸️ || C || HTML5/CSS3 ❤️ || TailWind"
    },
    {
      "login": "bforbilly24",
      "id": 93701344,
      "node_id": "U_kgDOBZXE4A",
      "avatar_url": "https://avatars.githubusercontent.com/u/93701344?v=4",
      "gravatar_id": "",
      "url": "https://api.github.com/users/bforbilly24",
      "html_url": "https://github.com/bforbilly24",
      "followers_url": "https://api.github.com/users/bforbilly24/followers",
      "following_url": "https://api.github.com/users/bforbilly24/following{/other_user}",
      "gists_url": "https://api.github.com/users/bforbilly24/gists{/gist_id}",
      "starred_url": "https://api.github.com/users/bforbilly24/starred{/owner}{/repo}",
      "subscriptions_url": "https://api.github.com/users/bforbilly24/subscriptions",
      "organizations_url": "https://api.github.com/users/bforbilly24/orgs",
      "repos_url": "https://api.github.com/users/bforbilly24/repos",
      "events_url": "https://api.github.com/users/bforbilly24/events{/privacy}",
      "received_events_url": "https://api.github.com/users/bforbilly24/received_events",
      "type": "User",
      "user_view_type": "public",
      "site_admin": false,
      "contributions": 3,
      "name": "Muhammad Daniel Krisna Halim Putra",
      "bio": "IT Enthusiast @circleitdev "
    },
    {
      "login": "HarishM-git",
      "id": 141745892,
      "node_id": "U_kgDOCHLe5A",
      "avatar_url": "https://avatars.githubusercontent.com/u/141745892?v=4",
      "gravatar_id": "",
      "url": "https://api.github.com/users/HarishM-git",
      "html_url": "https://github.com/HarishM-git",
      "followers_url": "https://api.github.com/users/HarishM-git/followers",
      "following_url": "https://api.github.com/users/HarishM-git/following{/other_user}",
      "gists_url": "https://api.github.com/users/HarishM-git/gists{/gist_id}",
      "starred_url": "https://api.github.com/users/HarishM-git/starred{/owner}{/repo}",
      "subscriptions_url": "https://api.github.com/users/HarishM-git/subscriptions",
      "organizations_url": "https://api.github.com/users/HarishM-git/orgs",
      "repos_url": "https://api.github.com/users/HarishM-git/repos",
      "events_url": "https://api.github.com/users/HarishM-git/events{/privacy}",
      "received_events_url": "https://api.github.com/users/HarishM-git/received_events",
      "type": "User",
      "user_view_type": "public",
      "site_admin": false,
      "contributions": 1,
      "name": "Harish.M",
      "bio": "Full-stack developer focused on scalable, user-friendly web and app solutions, with a strong commitment to open-source contributions."
    },
    {
      "login": "Koushik-Zzz",
      "id": 144420519,
      "node_id": "U_kgDOCJuupw",
      "avatar_url": "https://avatars.githubusercontent.com/u/144420519?v=4",
      "gravatar_id": "",
      "url": "https://api.github.com/users/Koushik-Zzz",
      "html_url": "https://github.com/Koushik-Zzz",
      "followers_url": "https://api.github.com/users/Koushik-Zzz/followers",
      "following_url": "https://api.github.com/users/Koushik-Zzz/following{/other_user}",
      "gists_url": "https://api.github.com/users/Koushik-Zzz/gists{/gist_id}",
      "starred_url": "https://api.github.com/users/Koushik-Zzz/starred{/owner}{/repo}",
      "subscriptions_url": "https://api.github.com/users/Koushik-Zzz/subscriptions",
      "organizations_url": "https://api.github.com/users/Koushik-Zzz/orgs",
      "repos_url": "https://api.github.com/users/Koushik-Zzz/repos",
      "events_url": "https://api.github.com/users/Koushik-Zzz/events{/privacy}",
      "received_events_url": "https://api.github.com/users/Koushik-Zzz/received_events",
      "type": "User",
      "user_view_type": "public",
      "site_admin": false,
      "contributions": 1,
      "name": "Koushik",
      "bio": "Self-taught programmer, learning to scale. Open to any opportunities!"
    },
    {
      "login": "mainishanhoon",
      "id": 110257833,
      "node_id": "U_kgDOBpJmqQ",
      "avatar_url": "https://avatars.githubusercontent.com/u/110257833?v=4",
      "gravatar_id": "",
      "url": "https://api.github.com/users/mainishanhoon",
      "html_url": "https://github.com/mainishanhoon",
      "followers_url": "https://api.github.com/users/mainishanhoon/followers",
      "following_url": "https://api.github.com/users/mainishanhoon/following{/other_user}",
      "gists_url": "https://api.github.com/users/mainishanhoon/gists{/gist_id}",
      "starred_url": "https://api.github.com/users/mainishanhoon/starred{/owner}{/repo}",
      "subscriptions_url": "https://api.github.com/users/mainishanhoon/subscriptions",
      "organizations_url": "https://api.github.com/users/mainishanhoon/orgs",
      "repos_url": "https://api.github.com/users/mainishanhoon/repos",
      "events_url": "https://api.github.com/users/mainishanhoon/events{/privacy}",
      "received_events_url": "https://api.github.com/users/mainishanhoon/received_events",
      "type": "User",
      "user_view_type": "public",
      "site_admin": false,
      "contributions": 1,
      "name": "Nishan Kashyap",
      "bio": "Normieee"
    }
  ],
  "totalContributions": 669,
  "userStats": {
    "2025-08-10": 2,
    "2025-07-30": 6,
    "2025-07-28": 4,
    "2025-07-25": 4,
    "2025-07-24": 3,
    "2025-07-21": 2,
    "2025-07-20": 6,
    "2025-07-19": 21,
    "2025-07-18": 10,
    "2025-07-17": 2,
    "2025-07-14": 4,
    "2025-07-13": 2,
    "2025-07-05": 6,
    "2025-07-04": 1,
    "2025-06-26": 8,
    "2025-06-16": 1,
    "2025-06-15": 3,
    "2025-06-14": 3,
    "2025-06-12": 5,
    "2025-06-10": 1,
    "2025-06-09": 8,
    "2025-06-07": 2,
    "2025-06-06": 1,
    "2025-06-04": 5,
    "2025-06-03": 8,
    "2025-06-02": 4,
    "2025-06-01": 17,
    "2025-05-31": 8,
    "2025-05-29": 5,
    "2025-05-28": 10,
    "2025-05-27": 1,
    "2025-05-26": 5,
    "2025-05-25": 12,
    "2025-05-24": 11,
    "2025-05-23": 2,
    "2025-05-22": 1,
    "2025-05-21": 2,
    "2025-05-17": 3,
    "2025-05-16": 4,
    "2025-05-13": 2,
    "2025-05-12": 25,
    "2025-05-11": 16,
    "2025-05-09": 13,
    "2025-05-08": 2,
    "2025-05-07": 1,
    "2025-05-06": 3,
    "2025-05-05": 6,
    "2025-05-04": 4,
    "2025-05-03": 1,
    "2025-05-02": 4,
    "2025-05-01": 23,
    "2025-04-30": 8,
    "2025-04-28": 8,
    "2025-04-27": 4,
    "2025-04-26": 3,
    "2025-04-23": 3,
    "2025-04-22": 11,
    "2025-04-21": 15,
    "2025-04-19": 1,
    "2025-04-18": 8,
    "2025-04-17": 21,
    "2025-04-16": 23,
    "2025-04-15": 7,
    "2025-04-14": 29,
    "2025-04-13": 11,
    "2025-04-12": 2,
    "2025-03-31": 1,
    "2025-03-30": 11,
    "2025-03-29": 8,
    "2025-03-28": 53,
    "2025-03-27": 6,
    "2025-03-26": 39,
    "2025-03-25": 40,
    "2025-03-23": 2,
    "2025-03-21": 51,
    "2025-03-10": 1
  },
  "codeFrequency": {
    "2025-08-10": {
      "additions": 3,
      "deletions": 3
    },
    "2025-07-30": {
      "additions": 1630,
      "deletions": 1388
    },
    "2025-07-28": {
      "additions": 175,
      "deletions": 29
    },
    "2025-07-25": {
      "additions": 551,
      "deletions": 24
    },
    "2025-07-24": {
      "additions": 48,
      "deletions": 17
    },
    "2025-07-21": {
      "additions": 224,
      "deletions": 31
    },
    "2025-07-20": {
      "additions": 13896,
      "deletions": 12244
    },
    "2025-07-19": {
      "additions": 12120,
      "deletions": 394
    },
    "2025-07-18": {
      "additions": 4090,
      "deletions": 1997
    },
    "2025-07-17": {
      "additions": 762,
      "deletions": 158
    },
    "2025-07-14": {
      "additions": 413,
      "deletions": 872
    },
    "2025-07-13": {
      "additions": 993,
      "deletions": 450
    },
    "2025-07-05": {
      "additions": 2001,
      "deletions": 270
    },
    "2025-07-04": {
      "additions": 53,
      "deletions": 53
    },
    "2025-06-26": {
      "additions": 736,
      "deletions": 700
    },
    "2025-06-16": {
      "additions": 0,
      "deletions": 0
    },
    "2025-06-15": {
      "additions": 1922,
      "deletions": 217
    },
    "2025-06-14": {
      "additions": 55,
      "deletions": 79
    },
    "2025-06-12": {
      "additions": 1868,
      "deletions": 408
    },
    "2025-06-10": {
      "additions": 226,
      "deletions": 29
    },
    "2025-06-09": {
      "additions": 1338,
      "deletions": 94
    },
    "2025-06-07": {
      "additions": 130,
      "deletions": 106
    },
    "2025-06-06": {
      "additions": 99,
      "deletions": 46
    },
    "2025-06-04": {
      "additions": 103,
      "deletions": 94
    },
    "2025-06-03": {
      "additions": 1100,
      "deletions": 669
    },
    "2025-06-02": {
      "additions": 411,
      "deletions": 240
    },
    "2025-06-01": {
      "additions": 60366,
      "deletions": 28581
    },
    "2025-05-31": {
      "additions": 3217,
      "deletions": 1484
    },
    "2025-05-29": {
      "additions": 601,
      "deletions": 52
    },
    "2025-05-28": {
      "additions": 485,
      "deletions": 424
    },
    "2025-05-27": {
      "additions": 924,
      "deletions": 54
    },
    "2025-05-26": {
      "additions": 255,
      "deletions": 269
    },
    "2025-05-25": {
      "additions": 2782,
      "deletions": 1697
    },
    "2025-05-24": {
      "additions": 603,
      "deletions": 218
    },
    "2025-05-23": {
      "additions": 7,
      "deletions": 1
    },
    "2025-05-22": {
      "additions": 779,
      "deletions": 548
    },
    "2025-05-21": {
      "additions": 789,
      "deletions": 558
    },
    "2025-05-17": {
      "additions": 100,
      "deletions": 0
    },
    "2025-05-16": {
      "additions": 1479,
      "deletions": 186
    },
    "2025-05-13": {
      "additions": 416,
      "deletions": 35
    },
    "2025-05-12": {
      "additions": 4242,
      "deletions": 642
    },
    "2025-05-11": {
      "additions": 4096,
      "deletions": 1478
    },
    "2025-05-09": {
      "additions": 3389,
      "deletions": 1785
    },
    "2025-05-08": {
      "additions": 819,
      "deletions": 147
    },
    "2025-05-07": {
      "additions": 106,
      "deletions": 320
    },
    "2025-05-06": {
      "additions": 754,
      "deletions": 99
    },
    "2025-05-05": {
      "additions": 1967,
      "deletions": 234
    },
    "2025-05-04": {
      "additions": 485,
      "deletions": 97
    },
    "2025-05-03": {
      "additions": 216,
      "deletions": 18
    },
    "2025-05-02": {
      "additions": 420,
      "deletions": 197
    },
    "2025-05-01": {
      "additions": 7114,
      "deletions": 4794
    },
    "2025-04-30": {
      "additions": 1621,
      "deletions": 476
    },
    "2025-04-28": {
      "additions": 492,
      "deletions": 39
    },
    "2025-04-27": {
      "additions": 619,
      "deletions": 95
    },
    "2025-04-26": {
      "additions": 464,
      "deletions": 58
    },
    "2025-04-23": {
      "additions": 87,
      "deletions": 251
    },
    "2025-04-22": {
      "additions": 674,
      "deletions": 187
    },
    "2025-04-21": {
      "additions": 2303,
      "deletions": 644
    },
    "2025-04-19": {
      "additions": 283,
      "deletions": 25
    },
    "2025-04-18": {
      "additions": 498,
      "deletions": 196
    },
    "2025-04-17": {
      "additions": 1737,
      "deletions": 309
    },
    "2025-04-16": {
      "additions": 2660,
      "deletions": 353
    },
    "2025-04-15": {
      "additions": 543,
      "deletions": 136
    },
    "2025-04-14": {
      "additions": 3247,
      "deletions": 1216
    },
    "2025-04-13": {
      "additions": 1958,
      "deletions": 635
    },
    "2025-04-12": {
      "additions": 87,
      "deletions": 112
    },
    "2025-03-31": {
      "additions": 104,
      "deletions": 36
    },
    "2025-03-30": {
      "additions": 577,
      "deletions": 118
    },
    "2025-03-29": {
      "additions": 630,
      "deletions": 297
    },
    "2025-03-28": {
      "additions": 8684,
      "deletions": 1796
    },
    "2025-03-27": {
      "additions": 160,
      "deletions": 223
    },
    "2025-03-26": {
      "additions": 1550,
      "deletions": 244
    },
    "2025-03-25": {
      "additions": 7241,
      "deletions": 6389
    },
    "2025-03-23": {
      "additions": 5594,
      "deletions": 5594
    },
    "2025-03-21": {
      "additions": 7335,
      "deletions": 5233
    },
    "2025-03-10": {
      "additions": 1,
      "deletions": 0
    }
  },
  "lastUpdated": "2025-08-11T19:14:26.667Z"
} as const;

export type GitHubData = typeof githubData;
