export const tweetContents = [
  '🚀 This new UI library Mvpblocks just changed the game for me. Building clean UIs has never been faster.\nblocks.mvp-subha.me\n@mvp_Subha',

  '💡 Plugged Mvpblocks into my project and had a landing page ready in minutes. Absolutely wild.\nblocks.mvp-subha.me\n@mvp_Subha',

  '⚡️ No more fiddling with layout from scratch – Mvpblocks got me from idea to UI in no time.\nblocks.mvp-subha.me\n@mvp_Subha',

  "🎨 If you're picky about design, Mvpblocks will blow your mind. Everything just looks polished.\nblocks.mvp-subha.me\n@mvp_Subha",

  '🔥 Shipped a project in hours using Mvpblocks. Animations? Smooth as hell.\nblocks.mvp-subha.me\n@mvp_Subha',

  '✨ Mvpblocks is one of the cleanest UI kits I’ve used in a while. Great dev experience too.\nblocks.mvp-subha.me\n@mvp_Subha',

  '🎯 Met a tight deadline thanks to Mvpblocks. Legit a lifesaver for fast frontend work.\nblocks.mvp-subha.me\n@mvp_Subha',

  '⭐️ Mvpblocks saved me from hours of repetitive UI grind. This is how components should be done.\nblocks.mvp-subha.me\n@mvp_Subha',

  '💪 My workflow got a serious upgrade after using Mvpblocks. Components are crisp, customizable, and fast.\nblocks.mvp-subha.me\n@mvp_Subha',

  '🌟 Mvpblocks hits the sweet spot between flexibility and ease-of-use. Genuinely impressed.\nblocks.mvp-subha.me\n@mvp_Subha',

  '📱 Building responsive designs doesn’t feel like a chore anymore – thanks to Mvpblocks.\nblocks.mvp-subha.me\n@mvp_Subha',

  '🎉 Just dropped a site using Mvpblocks and I’m shook. Minimal setup, max impact.\nblocks.mvp-subha.me\n@mvp_Subha',

  '💻 If you like clean, scalable UI components, do yourself a favor and check out Mvpblocks.\nblocks.mvp-subha.me\n@mvp_Subha',

  '🚗 Went from zero to launch in a single afternoon using Mvpblocks. No joke.\nblocks.mvp-subha.me\n@mvp_Subha',

  '🎨 Design consistency without the micromanagement. Mvpblocks is actually cracked.\nblocks.mvp-subha.me\n@mvp_Subha',
];
