{"name": "animated-btn1", "type": "registry:block", "dependencies": [], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\n\nexport default function AnimatedBtn1() {\n  return (\n    <div className=\"flex items-center justify-center\">\n      <button className=\"bubbleeffectbtn\" type=\"button\">\n        <style jsx>{`\n          .bubbleeffectbtn {\n            min-width: 130px;\n            height: 40px;\n            color: #fff;\n            cursor: pointer;\n            transition: all 0.3s ease;\n            position: relative;\n            display: inline-block;\n            outline: none;\n            border-radius: 25px;\n            border: none;\n            background: linear-gradient(45deg, #212529, #343a40);\n            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n            z-index: 1;\n            overflow: hidden;\n          }\n\n          .bubbleeffectbtn:before {\n            content: '';\n            position: absolute;\n            top: -50%;\n            left: -50%;\n            width: 200%;\n            height: 200%;\n            background: linear-gradient(\n              45deg,\n              rgba(255, 255, 255, 0.1),\n              rgba(255, 255, 255, 0)\n            );\n            transform: rotate(45deg);\n            transition: all 0.5s ease;\n            z-index: -1;\n          }\n\n          .bubbleeffectbtn:hover:before {\n            top: -100%;\n            left: -100%;\n          }\n\n          .bubbleeffectbtn:after {\n            border-radius: 25px;\n            position: absolute;\n            content: '';\n            width: 0;\n            height: 100%;\n            top: 0;\n            z-index: -1;\n            box-shadow:\n              inset 2px 2px 2px 0px rgba(255, 255, 255, 0.5),\n              7px 7px 20px 0px rgba(0, 0, 0, 0.1),\n              4px 4px 5px 0px rgba(0, 0, 0, 0.1);\n            transition: all 0.3s ease;\n            background: linear-gradient(45deg, #343a40, #495057);\n            right: 0;\n          }\n\n          .bubbleeffectbtn:hover:after {\n            width: 100%;\n            left: 0;\n          }\n\n          .bubbleeffectbtn:active {\n            top: 2px;\n            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n            background: linear-gradient(45deg, #212529, #343a40);\n          }\n\n          .bubbleeffectbtn span {\n            position: relative;\n            z-index: 2;\n          }\n        `}</style>\n\n        <span className=\"text-sm font-medium\">Hover me</span>\n      </button>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/basics/buttons/animated-btn1.tsx", "target": "components/mvpblocks/animated-btn1.tsx"}]}