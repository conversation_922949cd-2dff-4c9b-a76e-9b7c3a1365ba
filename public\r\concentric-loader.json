{"name": "concentric-loader", "type": "registry:block", "dependencies": [], "registryDependencies": [], "files": [{"type": "registry:block", "content": "export default function ConcentricLoader() {\n  return (\n    <div className=\"flex w-full flex-col items-center justify-center gap-4\">\n      <div className=\"flex h-16 w-16 animate-spin items-center justify-center rounded-full border-4 border-transparent border-t-blue-400 text-4xl text-blue-400\">\n        <div className=\"flex h-12 w-12 animate-spin items-center justify-center rounded-full border-4 border-transparent border-t-red-400 text-2xl text-red-400\"></div>\n      </div>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/basics/loaders/concentric-loader.tsx", "target": "components/mvpblocks/concentric-loader.tsx"}]}