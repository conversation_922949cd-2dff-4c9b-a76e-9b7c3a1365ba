---
title: Pricing
description: Pricing sections provide information about the cost of your product or service. They can include details about different pricing tiers, features included in each tier, and any discounts or promotions available.
root: mainsections
updated: true
---

import { ComponentPreview } from '@/components/preview/component-preview';
import { extractSourceCode } from '@/lib/code';

## Simple Pricing

<ComponentPreview
  name="simple-pricing"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode('simple-pricing')).code}
  lang="tsx"
  fromDocs={true}
/>

## Designer Pricing
<ComponentPreview
  name="designer-pricing"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode('designer-pricing')).code}
  lang="tsx"
  fromDocs={true}
/>

## Congested Pricing

<ComponentPreview
  name="congusted-pricing"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode('congusted-pricing')).code}
  lang="tsx"
  fromDocs={true}
/>

## Pricing with Modals

<ComponentPreview
  name="pricing-with-modals"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode('pricing-with-modals')).code}
  lang="tsx"
  fromDocs={true}
/>

## 4 pricing tiers

<ComponentPreview
  name="pricing-5"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode('pricing-5')).code}
  lang="tsx"
  fromDocs={true}
/>

## Modern Pricing

<ComponentPreview
  name="pricing-2"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode('pricing-2')).code}
  lang="tsx"
  fromDocs={true}
/>



## Basic Pricing

<ComponentPreview
  name="pricing-3"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode('pricing-3')).code}
  lang="tsx"
  fromDocs={true}
/>

## Beautiful Pricing

<ComponentPreview
  name="pricing-4"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode('pricing-4')).code}
  lang="tsx"
  fromDocs={true}
/>
