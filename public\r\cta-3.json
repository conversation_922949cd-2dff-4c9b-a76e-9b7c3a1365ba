{"name": "cta-3", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "import { ArrowRight } from 'lucide-react';\n\nexport default function CTA3() {\n  return (\n    <section className=\"relative px-4 py-24 md:px-6\">\n      <div className=\"mx-auto max-w-7xl\">\n        <div className=\"relative overflow-hidden rounded-2xl border border-[#1A3C6C]/30 bg-[#1A2649]/80 shadow-2xl transition-all duration-500 hover:border-[#2E9BFF]/30 hover:shadow-[0_0_30px_rgba(88,192,255,0.2)]\">\n          <div className=\"relative z-10 grid gap-0 lg:grid-cols-2\">\n            <div className=\"p-8 md:p-12 lg:p-16\">\n              <div className=\"mb-6 inline-block rounded-full border border-[#1A3C6C]/30 bg-[#0A0F2C] px-4 py-1 text-[#2E9BFF]\">\n                TAKE ACTION NOW\n              </div>\n              <h2 className=\"mb-6 text-3xl font-bold tracking-tight text-white md:text-4xl lg:text-5xl\">\n                Ready to{' '}\n                <span className=\"bg-gradient-to-r from-blue-500 to-blue-700 bg-clip-text text-transparent\">\n                  Transform\n                </span>{' '}\n                Your Marketing?\n              </h2>\n              <p className=\"text-muted-foreground mb-8 text-lg\">\n                96% of visitors leave websites without taking action. Our\n                conversion-optimized funnels recapture those lost opportunities\n                and turn them into predictable revenue.\n              </p>\n\n              <div className=\"space-y-6\">\n                <div className=\"flex items-start\">\n                  <div className=\"mt-1 mr-4 rounded-full bg-[#0A0F2C] p-2 text-[#2E9BFF]\">\n                    <svg\n                      width=\"20\"\n                      height=\"20\"\n                      viewBox=\"0 0 20 20\"\n                      fill=\"none\"\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                    >\n                      <path\n                        d=\"M7.5 10L9 11.5L12.5 8\"\n                        stroke=\"currentColor\"\n                        strokeWidth=\"1.5\"\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                      />\n                      <path\n                        d=\"M10 17.5C14.1421 17.5 17.5 14.1421 17.5 10C17.5 5.85786 14.1421 2.5 10 2.5C5.85786 2.5 2.5 5.85786 2.5 10C2.5 14.1421 5.85786 17.5 10 17.5Z\"\n                        stroke=\"currentColor\"\n                        strokeWidth=\"1.5\"\n                      />\n                    </svg>\n                  </div>\n                  <div>\n                    <h3 className=\"font-heading text-lg font-bold text-white\">\n                      Free Strategy Session\n                    </h3>\n                    <p className=\"text-muted-foreground\">\n                      No obligation, just pure value for your business.\n                    </p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start\">\n                  <div className=\"mt-1 mr-4 rounded-full bg-[#0A0F2C] p-2 text-[#2E9BFF]\">\n                    <svg\n                      width=\"20\"\n                      height=\"20\"\n                      viewBox=\"0 0 20 20\"\n                      fill=\"none\"\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                    >\n                      <path\n                        d=\"M7.5 10L9 11.5L12.5 8\"\n                        stroke=\"currentColor\"\n                        strokeWidth=\"1.5\"\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                      />\n                      <path\n                        d=\"M10 17.5C14.1421 17.5 17.5 14.1421 17.5 10C17.5 5.85786 14.1421 2.5 10 2.5C5.85786 2.5 2.5 5.85786 2.5 10C2.5 14.1421 5.85786 17.5 10 17.5Z\"\n                        stroke=\"currentColor\"\n                        strokeWidth=\"1.5\"\n                      />\n                    </svg>\n                  </div>\n                  <div>\n                    <h3 className=\"text-lg font-bold text-white\">\n                      Custom Funnel Blueprint\n                    </h3>\n                    <p className=\"text-muted-foreground\">\n                      Walk away with actionable insights regardless of who you\n                      work with.\n                    </p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start\">\n                  <div className=\"mt-1 mr-4 rounded-full bg-[#0A0F2C] p-2 text-[#2E9BFF]\">\n                    <svg\n                      width=\"20\"\n                      height=\"20\"\n                      viewBox=\"0 0 20 20\"\n                      fill=\"none\"\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                    >\n                      <path\n                        d=\"M7.5 10L9 11.5L12.5 8\"\n                        stroke=\"currentColor\"\n                        strokeWidth=\"1.5\"\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                      />\n                      <path\n                        d=\"M10 17.5C14.1421 17.5 17.5 14.1421 17.5 10C17.5 5.85786 14.1421 2.5 10 2.5C5.85786 2.5 2.5 5.85786 2.5 10C2.5 14.1421 5.85786 17.5 10 17.5Z\"\n                        stroke=\"currentColor\"\n                        strokeWidth=\"1.5\"\n                      />\n                    </svg>\n                  </div>\n                  <div>\n                    <h3 className=\"text-lg font-bold text-white\">\n                      ROI Projection\n                    </h3>\n                    <p className=\"text-muted-foreground\">\n                      See the potential impact on your revenue before you\n                      invest.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"relative flex flex-col justify-center overflow-hidden bg-gradient-to-br from-blue-500 to-blue-700 p-8 text-white md:p-12 lg:p-16\">\n              <div className=\"absolute top-0 right-0 h-full w-full opacity-10\"></div>\n              <div className=\"relative z-10\">\n                <h3 className=\"mb-6 text-2xl font-bold tracking-tight md:text-3xl\">\n                  Don&apos;t Let Another Day Pass With A Leaky Marketing Funnel\n                </h3>\n                <p className=\"mb-8 text-white/80\">\n                  Every day with an underperforming funnel is costing you:\n                </p>\n\n                <div className=\"space-y-6\">\n                  <div className=\"flex items-center\">\n                    <div className=\"mr-4 flex h-10 w-10 items-center justify-center rounded-full border-2 border-white text-lg font-bold\">\n                      1\n                    </div>\n                    <p className=\"text-lg\">\n                      Qualified leads that never convert\n                    </p>\n                  </div>\n\n                  <div className=\"flex items-center\">\n                    <div className=\"mr-4 flex h-10 w-10 items-center justify-center rounded-full border-2 border-white text-lg font-bold\">\n                      2\n                    </div>\n                    <p className=\"text-lg\">Revenue that goes to competitors</p>\n                  </div>\n\n                  <div className=\"flex items-center\">\n                    <div className=\"mr-4 flex h-10 w-10 items-center justify-center rounded-full border-2 border-white text-lg font-bold\">\n                      3\n                    </div>\n                    <p className=\"text-lg\">\n                      Wasted ad spend on traffic that doesn&apos;t convert\n                    </p>\n                  </div>\n                </div>\n\n                <div className=\"mt-10 rounded-xl bg-white/10 p-6 backdrop-blur\">\n                  <p className=\"text-lg font-medium\">\n                    &quot;We increased our conversion rate from 1.7% to 6.3% in\n                    just 31 days after implementing Freedom Funnels&apos;\n                    system.&quot;\n                  </p>\n                  <p className=\"mt-3 font-medium text-white/70\">\n                    — Ryan Miller, CEO, Apex Solutions\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/cta/cta-3.tsx", "target": "components/mvpblocks/cta-3.tsx"}]}