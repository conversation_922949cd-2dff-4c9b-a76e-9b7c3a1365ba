name: Vercel Production Deployment

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

on:
  push:
    branches:
      - main

jobs:
  Deploy-Production:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - uses: oven-sh/setup-bun@v2

      - name: Install Vercel CLI
        run: bun install --global vercel@latest

      - name: Deploy Project to Vercel
        run: |
          cd frontend
          vercel deploy --prod --token=${{ secrets.VERCEL_TOKEN }}
