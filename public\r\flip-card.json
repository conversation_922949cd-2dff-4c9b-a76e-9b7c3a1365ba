{"name": "flip-card", "author": "nuelst", "type": "registry:block", "dependencies": ["lucide-react", "react", "cn"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\n\n/**\n * @author: @nuelst\n * @description: Card Flip - MVP Development Theme\n * @version: 1.1.0\n * @date: 2025-01-14\n * @license: MIT\n * @website: https://nueslt.vercel.app\n * @github: https://github.com/nuelst\n */\n\nimport { cn } from '@/lib/utils';\nimport { ArrowRight, Code2, Copy, Rocket, Zap } from 'lucide-react';\nimport { useState } from 'react';\n\nexport interface CardFlipProps {\n  title?: string;\n  subtitle?: string;\n  description?: string;\n  features?: string[];\n}\n\nexport default function CardFlip({\n  title = 'Build MVPs Fast',\n  subtitle = 'Launch your idea in record time',\n  description = 'Copy, paste, customize—and launch your MVP faster than ever with our developer-first component library.',\n  features = [\n    'Copy & Paste Ready',\n    'Developer-First',\n    'MVP Optimized',\n    'Zero Setup Required',\n  ],\n}: CardFlipProps) {\n  const [isFlipped, setIsFlipped] = useState(false);\n\n  return (\n    <div\n      className=\"group relative h-[360px] w-full max-w-[300px] [perspective:2000px]\"\n      onMouseEnter={() => setIsFlipped(true)}\n      onMouseLeave={() => setIsFlipped(false)}\n    >\n      <div\n        className={cn(\n          'relative h-full w-full',\n          '[transform-style:preserve-3d]',\n          'transition-all duration-700',\n          isFlipped\n            ? '[transform:rotateY(180deg)]'\n            : '[transform:rotateY(0deg)]',\n        )}\n      >\n        {/* Front of card */}\n        <div\n          className={cn(\n            'absolute inset-0 h-full w-full',\n            '[transform:rotateY(0deg)] [backface-visibility:hidden]',\n            'overflow-hidden rounded-2xl',\n            'bg-gradient-to-br from-white via-slate-50 to-slate-100',\n            'dark:from-zinc-900 dark:via-zinc-900/95 dark:to-zinc-800',\n            'border border-slate-200 dark:border-zinc-800/50',\n            'shadow-lg dark:shadow-xl',\n            'transition-all duration-700',\n            'group-hover:shadow-xl dark:group-hover:shadow-2xl',\n            'group-hover:border-primary/20 dark:group-hover:border-primary/30',\n            isFlipped ? 'opacity-0' : 'opacity-100',\n          )}\n        >\n          {/* Background gradient effect */}\n          <div className=\"from-primary/5 dark:from-primary/10 absolute inset-0 bg-gradient-to-br via-transparent to-blue-500/5 dark:to-blue-500/10\" />\n\n          {/* Animated code blocks */}\n          <div className=\"absolute inset-0 flex items-center justify-center pt-20\">\n            <div className=\"relative flex h-[100px] w-[200px] flex-col items-center justify-center gap-2\">\n              {/* Code blocks animation */}\n              {[...Array(6)].map((_, i) => (\n                <div\n                  key={i}\n                  className={cn(\n                    'h-3 w-full rounded-sm',\n                    'from-primary/20 via-primary/30 to-primary/20 bg-gradient-to-r',\n                    'animate-[slideIn_2s_ease-in-out_infinite]',\n                    'opacity-0',\n                  )}\n                  style={{\n                    width: `${60 + Math.random() * 40}%`,\n                    animationDelay: `${i * 0.2}s`,\n                    marginLeft: `${Math.random() * 20}%`,\n                  }}\n                />\n              ))}\n\n              {/* Central rocket icon */}\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <div\n                  className={cn(\n                    'h-12 w-12 rounded-xl',\n                    'from-primary via-primary/90 to-primary/80 bg-gradient-to-br',\n                    'flex items-center justify-center',\n                    'shadow-primary/25 shadow-lg',\n                    'animate-pulse',\n                    'transition-all duration-500 group-hover:scale-110 group-hover:rotate-12',\n                  )}\n                >\n                  <Rocket className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Bottom content */}\n          <div className=\"absolute right-0 bottom-0 left-0 p-5\">\n            <div className=\"flex items-center justify-between gap-3\">\n              <div className=\"space-y-1.5\">\n                <h3 className=\"text-lg leading-snug font-semibold tracking-tight text-zinc-900 transition-all duration-500 ease-out group-hover:translate-y-[-4px] dark:text-white\">\n                  {title}\n                </h3>\n                <p className=\"line-clamp-2 text-sm tracking-tight text-zinc-600 transition-all delay-[50ms] duration-500 ease-out group-hover:translate-y-[-4px] dark:text-zinc-300\">\n                  {subtitle}\n                </p>\n              </div>\n              <div className=\"group/icon relative\">\n                <div\n                  className={cn(\n                    'absolute inset-[-8px] rounded-lg transition-opacity duration-300',\n                    'from-primary/20 via-primary/10 bg-gradient-to-br to-transparent',\n                    'opacity-0 group-hover/icon:opacity-100',\n                  )}\n                />\n                <Zap className=\"text-primary relative z-10 h-5 w-5 transition-all duration-300 group-hover/icon:scale-110 group-hover/icon:rotate-12\" />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Back of card */}\n        <div\n          className={cn(\n            'absolute inset-0 h-full w-full',\n            '[transform:rotateY(180deg)] [backface-visibility:hidden]',\n            'rounded-2xl p-5',\n            'bg-gradient-to-br from-white via-slate-50 to-slate-100',\n            'dark:from-zinc-900 dark:via-zinc-900/95 dark:to-zinc-800',\n            'border border-slate-200 dark:border-zinc-800',\n            'shadow-lg dark:shadow-xl',\n            'flex flex-col',\n            'transition-all duration-700',\n            'group-hover:shadow-xl dark:group-hover:shadow-2xl',\n            'group-hover:border-primary/20 dark:group-hover:border-primary/30',\n            !isFlipped ? 'opacity-0' : 'opacity-100',\n          )}\n        >\n          {/* Background gradient */}\n          <div className=\"from-primary/5 dark:from-primary/10 absolute inset-0 rounded-2xl bg-gradient-to-br via-transparent to-blue-500/5 dark:to-blue-500/10\" />\n\n          <div className=\"relative z-10 flex-1 space-y-5\">\n            <div className=\"space-y-2\">\n              <div className=\"mb-2 flex items-center gap-2\">\n                <div className=\"from-primary via-primary/90 to-primary/80 flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br\">\n                  <Code2 className=\"h-4 w-4 text-white\" />\n                </div>\n                <h3 className=\"text-lg leading-snug font-semibold tracking-tight text-zinc-900 transition-all duration-500 ease-out group-hover:translate-y-[-2px] dark:text-white\">\n                  {title}\n                </h3>\n              </div>\n              <p className=\"line-clamp-2 text-sm tracking-tight text-zinc-600 transition-all duration-500 ease-out group-hover:translate-y-[-2px] dark:text-zinc-400\">\n                {description}\n              </p>\n            </div>\n\n            <div className=\"space-y-2.5\">\n              {features.map((feature, index) => {\n                const icons = [Copy, Code2, Rocket, Zap];\n                const IconComponent = icons[index % icons.length];\n\n                return (\n                  <div\n                    key={feature}\n                    className=\"flex items-center gap-3 text-sm text-zinc-700 transition-all duration-500 dark:text-zinc-300\"\n                    style={{\n                      transform: isFlipped\n                        ? 'translateX(0)'\n                        : 'translateX(-10px)',\n                      opacity: isFlipped ? 1 : 0,\n                      transitionDelay: `${index * 100 + 200}ms`,\n                    }}\n                  >\n                    <div className=\"bg-primary/10 dark:bg-primary/20 flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-md\">\n                      <IconComponent className=\"text-primary h-3 w-3\" />\n                    </div>\n                    <span className=\"font-medium\">{feature}</span>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n\n          <div className=\"relative z-10 mt-auto border-t border-slate-200 pt-4 dark:border-zinc-800\">\n            <div\n              className={cn(\n                'group/start relative',\n                'flex items-center justify-between',\n                'rounded-lg p-2.5',\n                'transition-all duration-300',\n                'bg-gradient-to-r from-slate-100 via-slate-100 to-slate-100',\n                'dark:from-zinc-800 dark:via-zinc-800 dark:to-zinc-800',\n                'hover:from-primary/10 hover:via-primary/5 hover:to-transparent',\n                'dark:hover:from-primary/20 dark:hover:via-primary/10 dark:hover:to-transparent',\n                'hover:scale-[1.02] hover:cursor-pointer',\n                'hover:border-primary/20 border border-transparent',\n              )}\n            >\n              <span className=\"group-hover/start:text-primary text-sm font-semibold text-zinc-900 transition-colors duration-300 dark:text-white\">\n                Start Building\n              </span>\n              <div className=\"group/icon relative\">\n                <div\n                  className={cn(\n                    'absolute inset-[-6px] rounded-lg transition-all duration-300',\n                    'from-primary/20 via-primary/10 bg-gradient-to-br to-transparent',\n                    'scale-90 opacity-0 group-hover/start:scale-100 group-hover/start:opacity-100',\n                  )}\n                />\n                <ArrowRight className=\"text-primary relative z-10 h-4 w-4 transition-all duration-300 group-hover/start:translate-x-1 group-hover/start:scale-110\" />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <style jsx>{`\n        @keyframes slideIn {\n          0% {\n            transform: translateX(-100px);\n            opacity: 0;\n          }\n          50% {\n            transform: translateX(0);\n            opacity: 0.8;\n          }\n          100% {\n            transform: translateX(100px);\n            opacity: 0;\n          }\n        }\n      `}</style>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/cards/basic/card-flip.tsx", "target": "components/mvpblocks/card-flip.tsx"}]}