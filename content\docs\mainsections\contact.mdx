---
title: Contact
description: Contact sections allow users to get in touch with you through a form or other contact methods. They are essential for gathering user feedback, inquiries, and support requests.
root: mainsections
new: true
---

import {ComponentPreview} from "@/components/preview/component-preview";
import {extractSourceCode} from "@/lib/code";

## Contact Us 1

<ComponentPreview
  name="contact-us-1"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode('contact-us-1')).code}
  lang="tsx"
/>

## Contact Us 2

<ComponentPreview
  name="contact-us-2"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode('contact-us-2')).code}
  lang="tsx"
  fromDocs={true}
/>