{"name": "team-8", "author": "mosespace", "type": "registry:block", "dependencies": ["lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "import { <PERSON>ed<PERSON>, Gith<PERSON> } from 'lucide-react';\nimport Link from 'next/link';\n\n// Team member data type\ntype TeamMember = {\n  id: number;\n  name: string;\n  role: string;\n  image: string;\n  socialMedia?: {\n    linkedin?: string;\n    github?: string;\n  };\n};\n\nconst image =\n  'https://img.freepik.com/premium-photo/png-headset-headphones-portrait-cartoon_53876-762197.jpg?ga=GA1.1.1818589012.1*********&semt=ais_hybrid';\n\nconst dami_data: TeamMember[] = [\n  {\n    id: 1,\n    name: '<PERSON>',\n    role: 'Founder & CEO',\n    image:\n      'https://img.freepik.com/premium-photo/png-headset-headphones-portrait-glasses_53876-855224.jpg?ga=GA1.1.1818589012.1*********&semt=ais_hybrid',\n    socialMedia: {\n      linkedin: '#',\n      github: '#',\n    },\n  },\n  {\n    id: 2,\n    name: '<PERSON>',\n    role: 'Founder & CTO',\n    image:\n      'https://img.freepik.com/premium-photo/png-cartoon-portrait-glasses-white-background_53876-905385.jpg?ga=GA1.1.1818589012.1*********&semt=ais_hybrid',\n    socialMedia: {\n      linkedin: '#',\n      github: '#',\n    },\n  },\n  {\n    id: 3,\n    name: 'Aileen Gomes',\n    role: 'Chief of Staff',\n    image:\n      'https://img.freepik.com/premium-psd/3d-avatar-character_975163-690.jpg?ga=GA1.1.1818589012.1*********&semt=ais_hybrid',\n    socialMedia: {\n      linkedin: '#',\n      github: '#',\n    },\n  },\n  {\n    id: 4,\n    name: 'Rambo',\n    role: 'Blockchain Reliability Engineer',\n    image: image,\n    socialMedia: {\n      linkedin: '#',\n      github: '#',\n    },\n  },\n  {\n    id: 5,\n    name: 'Thomas Stätter',\n    role: 'Backend Engineer',\n    image:\n      'https://img.freepik.com/free-psd/3d-rendering-avatar_23-2150833554.jpg?ga=GA1.1.1818589012.1*********&semt=ais_hybrid',\n    socialMedia: {\n      linkedin: '#',\n      github: '#',\n    },\n  },\n  {\n    id: 6,\n    name: 'Oleksii Suslov',\n    role: 'Systems Engineer',\n    image:\n      'https://img.freepik.com/premium-photo/png-cartoon-female-adult-doll_53876-747120.jpg?ga=GA1.1.1818589012.1*********&semt=ais_hybrid',\n    socialMedia: {\n      linkedin: '#',\n      github: '#',\n    },\n  },\n  {\n    id: 7,\n    name: 'Joe Harrison',\n    role: 'Frontend Engineer',\n    image:\n      'https://img.freepik.com/free-psd/3d-rendering-avatar_23-2150833578.jpg?ga=GA1.1.1818589012.1*********&semt=ais_hybrid',\n    socialMedia: {\n      linkedin: '#',\n      github: '#',\n    },\n  },\n  {\n    id: 8,\n    name: 'Jason Alex',\n    role: 'Site Reliability Engineer',\n    image:\n      'https://img.freepik.com/free-photo/3d-portrait-businessman_23-2150793883.jpg?ga=GA1.1.1818589012.1*********&semt=ais_hybrid',\n    socialMedia: {\n      linkedin: '#',\n      github: '#',\n    },\n  },\n];\n\nexport default function Team8({\n  teamMembers = dami_data,\n  title1 = 'BLOCKJOY STARTED AS A PROJECT TO',\n  title2 = ' MANAGE STAKING WITH FRIENDS AND FAMILY.',\n  headline1 = 'A TEAM OF',\n  headline2 = 'WEB3 EXPERTS',\n  description1 = 'We built advanced tooling to optimize validator management for teams of two. But, due to the sheer difficulty of installing and running nodes, our little VaaS company grew crazy fast.',\n  description2 = \"e quickly realized that VaaS can't work for a network because it centralizes control. We turned our platform into a SaaS tool so that anyone could launch and run their own node with full control over where and on what infrastructure it runs. Now, network operators can run their own nodes; BlockJoy just takes the headaches away.\",\n}: {\n  title1?: string;\n  title2?: string;\n  headline1?: string;\n  headline2?: string;\n  description1?: string;\n  description2?: string;\n  teamMembers: TeamMember[];\n}) {\n  return (\n    <section className=\"w-full bg-slate-50 py-16\">\n      <div className=\"container mx-auto max-w-6xl px-4\">\n        <div className=\"mb-12 flex flex-col gap-12 md:flex-row\">\n          <div className=\"md:w-1/2\">\n            <div className=\"mb-4 flex items-center gap-2 text-black\">\n              <span className=\"text-black\">★</span>\n              <span className=\"rounded-full bg-gray-200 px-3 py-1 text-sm\">\n                WHO\n              </span>\n              <span className=\"rounded-full bg-gray-200 px-3 py-1 text-sm\">\n                WE\n              </span>\n              <span className=\"rounded-full bg-gray-200 px-3 py-1 text-sm\">\n                ARE\n              </span>\n            </div>\n            <h2 className=\"mb-4 text-4xl font-bold text-black\">\n              {headline1} <br />\n              {headline2}\n            </h2>\n            <p className=\"mb-4 text-gray-700\">\n              {title1}\n              <br />\n              {title2}\n            </p>\n          </div>\n          <div className=\"md:w-1/2\">\n            <p className=\"mb-4 text-gray-700\">{description1}</p>\n            <p className=\"text-gray-700\">{description2}</p>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4\">\n          {teamMembers.slice(0, 4).map((member) => (\n            <div\n              key={member.id}\n              className={`h-fit rounded-lg p-6 ${\n                member.id === 4 ? 'bg-lime-300' : 'bg-white'\n              }`}\n            >\n              <div className=\"relative mb-4 h-48 w-full overflow-hidden rounded-lg\">\n                <img\n                  src={member.image || '/placeholder.svg'}\n                  alt={member.name}\n                  className=\"object-cover\"\n                />\n              </div>\n              <div\n                className={`mb-2 inline-block rounded-full px-4 py-1 text-sm font-medium text-black ${\n                  member.id === 4 ? 'bg-white' : 'bg-gray-100'\n                }`}\n              >\n                {member.id === 4 ? `\"Rambo\"` : member.name}\n              </div>\n              <p className=\"text-gray-700\">{member.role}</p>\n              {member.id === 4 && (\n                <p className=\"mt-4 text-sm italic\">\n                  &quot;You miss 100% of the shots you don&apos;t take&quot;\n                  -Wayne Gretzky -Michael Scott\n                </p>\n              )}\n              <div className=\"mt-2 flex space-x-2\">\n                {member.socialMedia?.linkedin && (\n                  <Link\n                    href={member.socialMedia.linkedin}\n                    className=\"text-gray-500 hover:text-blue-700\"\n                  >\n                    <Linkedin size={18} />\n                  </Link>\n                )}\n                {member.socialMedia?.github && (\n                  <Link\n                    href={member.socialMedia.github}\n                    className=\"text-gray-500 hover:text-gray-900\"\n                  >\n                    <Github size={18} />\n                  </Link>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"mt-6 grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4\">\n          {teamMembers.slice(4).map((member) => (\n            <div key={member.id} className=\"rounded-lg bg-white p-6\">\n              <div className=\"relative mb-4 h-48 w-full overflow-hidden rounded-lg\">\n                <img\n                  src={member.image || '/placeholder.svg'}\n                  alt={member.name}\n                  className=\"object-cover\"\n                />\n              </div>\n              <div className=\"mb-2 inline-block rounded-full bg-gray-100 px-4 py-1 text-sm font-medium text-black\">\n                {member.name}\n              </div>\n              <p className=\"text-gray-700\">{member.role}</p>\n              <div className=\"mt-2 flex space-x-2\">\n                {member.socialMedia?.linkedin && (\n                  <Link\n                    href={member.socialMedia.linkedin}\n                    className=\"text-gray-500 hover:text-blue-700\"\n                  >\n                    <Linkedin size={18} />\n                  </Link>\n                )}\n                {member.socialMedia?.github && (\n                  <Link\n                    href={member.socialMedia.github}\n                    className=\"text-gray-500 hover:text-gray-900\"\n                  >\n                    <Github size={18} />\n                  </Link>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/team/team-8.tsx", "target": "components/mvpblocks/team-8.tsx"}]}