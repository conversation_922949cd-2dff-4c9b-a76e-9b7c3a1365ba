---
title: TanStack Router
description: Install and configure shadcn/ui for TanStack Router.
---

import { Callout } from "fumadocs-ui/components/callout";

### Create project

Start by creating a new TanStack Router project:

```bash
npx create-tsrouter-app@latest my-app --template file-router --tailwind --add-ons shadcn
```

### Add Components

You can now start adding components to your project.

```bash
npx shadcn@canary add button
```

The command above will add the `Button` component to your project. You can then import it like this:

```tsx title="src/routes/index.tsx" showLineNumbers {3,12}
import { createFileRoute } from "@tanstack/react-router"

import { Button } from "@/components/ui/button"

export const Route = createFileRoute("/")({
  component: App,
})

function App() {
  return (
    <div>
      <Button>Click me</Button>
    </div>
  )
}
```
