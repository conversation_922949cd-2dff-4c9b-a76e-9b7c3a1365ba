{"name": "v0-chat", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/use-auto-resize-textarea.json", "https://blocks.mvp-subha.me/r/textarea.json", "https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/button.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { useState } from 'react';\nimport { Textarea } from '@/components/ui/textarea';\nimport { cn } from '@/lib/utils';\nimport { useAutoResizeTextarea } from '@/hooks/use-auto-resize-textarea';\nimport {\n  ImageIcon,\n  FileUp,\n  Figma,\n  MonitorIcon,\n  CircleUserRound,\n  ArrowUpIcon,\n  Paperclip,\n  PlusIcon,\n} from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nexport function VercelV0Chat() {\n  const [value, setValue] = useState('');\n  const { textareaRef, adjustHeight } = useAutoResizeTextarea({\n    minHeight: 60,\n    maxHeight: 200,\n  });\n\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      if (value.trim()) {\n        setValue('');\n        adjustHeight(true);\n      }\n    }\n  };\n\n  return (\n    <div className=\"mx-auto flex w-full max-w-4xl flex-col items-center space-y-4 p-4 py-24 sm:space-y-8\">\n      <h1 className=\"text-foreground text-center text-2xl font-bold sm:text-4xl\">\n        What can I help you ship?\n      </h1>\n\n      <div className=\"w-full\">\n        <div className=\"border-border bg-secondary/20 relative rounded-xl border\">\n          <div className=\"overflow-y-auto\">\n            <Textarea\n              ref={textareaRef}\n              value={value}\n              onChange={(e) => {\n                setValue(e.target.value);\n                adjustHeight();\n              }}\n              onKeyDown={handleKeyDown}\n              placeholder=\"Ask v0 a question...\"\n              className={cn(\n                'w-full px-4 py-3',\n                'resize-none',\n                'bg-transparent',\n                'border-none',\n                'text-sm',\n                'focus:outline-none',\n                'focus-visible:ring-0 focus-visible:ring-offset-0',\n                'placeholder:text-sm',\n                'min-h-[60px]',\n              )}\n              style={{\n                overflow: 'hidden',\n              }}\n            />\n          </div>\n\n          <div className=\"flex items-center justify-between p-3\">\n            <div className=\"flex items-center gap-2\">\n              <Button\n                type=\"button\"\n                size=\"sm\"\n                variant=\"outline\"\n                className=\"group hover:bg-secondary/50 flex items-center gap-1 rounded-lg p-2\"\n              >\n                <Paperclip className=\"h-4 w-4\" />\n                <span className=\"hidden text-xs transition-opacity group-hover:inline\">\n                  Attach\n                </span>\n              </Button>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <Button\n                type=\"button\"\n                size=\"sm\"\n                variant=\"secondary\"\n                className=\"border-border flex items-center justify-between gap-1 rounded-lg border border-dashed px-2 py-1 text-sm transition-colors\"\n              >\n                <PlusIcon className=\"h-4 w-4\" />\n                Project\n              </Button>\n              <button\n                type=\"button\"\n                className={cn(\n                  'border-border flex items-center justify-between gap-1 rounded-lg border px-1.5 py-1.5 text-sm transition-colors',\n                  value.trim() ? 'bg-white text-black' : 'text-zinc-400',\n                )}\n              >\n                <ArrowUpIcon\n                  className={cn(\n                    'h-4 w-4',\n                    value.trim() ? 'text-black' : 'text-zinc-400',\n                  )}\n                />\n                <span className=\"sr-only\">Send</span>\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"-mx-4 mt-4 px-4 sm:mx-0 sm:px-0\">\n          <div className=\"flex flex-col flex-wrap items-start gap-2 sm:flex-row sm:items-center sm:justify-center sm:gap-3 sm:overflow-x-auto sm:pb-2\">\n            <ActionButton\n              icon={<ImageIcon className=\"h-4 w-4\" />}\n              label=\"Clone a Screenshot\"\n            />\n            <ActionButton\n              icon={<Figma className=\"h-4 w-4\" />}\n              label=\"Import from Figma\"\n            />\n            <ActionButton\n              icon={<FileUp className=\"h-4 w-4\" />}\n              label=\"Upload a Project\"\n            />\n            <ActionButton\n              icon={<MonitorIcon className=\"h-4 w-4\" />}\n              label=\"Landing Page\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\ninterface ActionButtonProps {\n  icon: React.ReactNode;\n  label: string;\n}\n\nfunction ActionButton({ icon, label }: ActionButtonProps) {\n  return (\n    <Button\n      type=\"button\"\n      variant=\"secondary\"\n      className=\"border-border bg-secondary/20 flex w-full flex-shrink-0 items-center gap-2 rounded-full border px-3 py-2 whitespace-nowrap transition-colors sm:w-auto sm:px-4\"\n    >\n      {icon}\n      <span className=\"text-xs\">{label}</span>\n    </Button>\n  );\n}\n\nexport default VercelV0Chat;\n", "path": "/components/mvpblocks/chatbot-ui/v0-chat.tsx", "target": "components/mvpblocks/v0-chat.tsx"}]}