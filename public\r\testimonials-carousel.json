{"name": "testimonials-carousel", "type": "registry:block", "dependencies": ["embla-carousel-react", "framer-motion", "lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/avatar.json", "https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport React, { useEffect } from 'react';\nimport useEmblaCarousel from 'embla-carousel-react';\nimport { cn } from '@/lib/utils';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { motion } from 'framer-motion';\nimport { Quote } from 'lucide-react';\n\nconst defaultTestimonials = [\n  {\n    text: 'MVPBlocks has completely changed the way I build UIs. Copy-paste, done. No more design stress.',\n    imageSrc: '/assets/avatars/avatar-1.webp',\n    name: '<PERSON><PERSON><PERSON>',\n    username: '@arjdev',\n    role: 'Frontend Developer',\n  },\n  {\n    text: 'Honestly shocked at how smooth the animations and styling are out of the box. Just works.',\n    imageSrc: '/assets/avatars/avatar-2.webp',\n    name: '<PERSON>',\n    username: '@sara.codes',\n    role: 'UX Designer',\n  },\n  {\n    text: 'Our team launched a client site in 2 days using MVPBlocks. Saved so much time.',\n    imageSrc: '/assets/avatars/avatar-3.webp',\n    name: '<PERSON>',\n    username: '@devninja',\n    role: 'Product Manager',\n  },\n  {\n    text: 'Plugged a few blocks into our existing codebase and everything blended perfectly. Massive W.',\n    imageSrc: '/assets/avatars/avatar-4.webp',\n    name: 'Priya Shah',\n    username: '@priyacodes',\n    role: 'Full Stack Developer',\n  },\n  {\n    text: 'Found a beautiful hero section, dropped it into V0, tweaked copy, and shipped in 15 minutes.',\n    imageSrc: '/assets/avatars/avatar-5.webp',\n    name: 'Leo Martin',\n    username: '@leobuilds',\n    role: 'Startup Founder',\n  },\n  {\n    text: 'MVPBlocks helped us prototype multiple landing pages without writing CSS once.',\n    imageSrc: '/assets/avatars/avatar-6.webp',\n    name: 'Chloe Winters',\n    username: '@chloewinters',\n    role: 'UI Designer',\n  },\n];\n\ninterface TestimonialProps {\n  testimonials?: {\n    text: string;\n    imageSrc: string;\n    name: string;\n    username: string;\n    role?: string;\n  }[];\n  title?: string;\n  subtitle?: string;\n  autoplaySpeed?: number;\n  className?: string;\n}\n\nexport default function TestimonialsCarousel({\n  testimonials = defaultTestimonials,\n  title = 'What our users say',\n  subtitle = 'From intuitive design to powerful features, our components have become essential tools for developers around the world.',\n  autoplaySpeed = 3000,\n  className,\n}: TestimonialProps) {\n  const [emblaRef, emblaApi] = useEmblaCarousel({\n    loop: true,\n    align: 'center',\n    containScroll: 'trimSnaps',\n    dragFree: true,\n  });\n\n  useEffect(() => {\n    if (!emblaApi) return;\n\n    const autoplay = setInterval(() => {\n      emblaApi.scrollNext();\n    }, autoplaySpeed);\n\n    return () => {\n      clearInterval(autoplay);\n    };\n  }, [emblaApi, autoplaySpeed]);\n\n  const allTestimonials = [...testimonials, ...testimonials];\n\n  return (\n    <section\n      className={cn('relative overflow-hidden py-16 md:py-24', className)}\n    >\n      <div className=\"absolute inset-0 -z-10\">\n        <div className=\"absolute inset-0 bg-[radial-gradient(ellipse_at_center,hsl(var(--primary)/0.2),transparent_60%)]\" />\n        <div className=\"bg-primary/5 absolute top-1/4 left-1/4 h-32 w-32 rounded-full blur-3xl\" />\n        <div className=\"bg-primary/10 absolute right-1/4 bottom-1/4 h-40 w-40 rounded-full blur-3xl\" />\n      </div>\n\n      <div className=\"container mx-auto px-4 md:px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          viewport={{ once: true }}\n          className=\"relative mb-12 text-center md:mb-16\"\n        >\n          <h1 className=\"from-foreground to-foreground/40 mb-4 bg-gradient-to-b bg-clip-text text-3xl font-bold text-transparent md:text-5xl lg:text-6xl\">\n            {title}\n          </h1>\n\n          <motion.p\n            className=\"text-muted-foreground mx-auto max-w-2xl text-base md:text-lg\"\n            initial={{ opacity: 0, y: 10 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.3 }}\n            viewport={{ once: true }}\n          >\n            {subtitle}\n          </motion.p>\n        </motion.div>\n\n        {/* Testimonials carousel */}\n        <div className=\"overflow-hidden\" ref={emblaRef}>\n          <div className=\"flex\">\n            {allTestimonials.map((testimonial, index) => (\n              <div\n                key={`${testimonial.name}-${index}`}\n                className=\"flex justify-center px-4\"\n              >\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.95 }}\n                  whileInView={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"border-border from-secondary/20 to-card relative h-full w-fit rounded-2xl border bg-gradient-to-b p-6 shadow-md backdrop-blur-sm\"\n                >\n                  {/* Enhanced decorative gradients */}\n                  <div className=\"from-primary/15 to-card absolute -top-5 -left-5 -z-10 h-40 w-40 rounded-full bg-gradient-to-b blur-md\" />\n                  <div className=\"from-primary/10 absolute -right-10 -bottom-10 -z-10 h-32 w-32 rounded-full bg-gradient-to-t to-transparent opacity-70 blur-xl\" />\n\n                  <motion.div\n                    initial={{ opacity: 0, y: -5 }}\n                    whileInView={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.3, delay: 0.2 + index * 0.05 }}\n                    viewport={{ once: true }}\n                    className=\"text-primary mb-4\"\n                  >\n                    <div className=\"relative\">\n                      <Quote className=\"h-10 w-10 -rotate-180\" />\n                    </div>\n                  </motion.div>\n\n                  <motion.p\n                    initial={{ opacity: 0 }}\n                    whileInView={{ opacity: 1 }}\n                    transition={{ duration: 0.5, delay: 0.3 + index * 0.05 }}\n                    viewport={{ once: true }}\n                    className=\"text-foreground/90 relative mb-6 text-base leading-relaxed\"\n                  >\n                    <span className=\"relative\">{testimonial.text}</span>\n                  </motion.p>\n\n                  {/* Enhanced user info with animation */}\n                  <motion.div\n                    initial={{ opacity: 0, y: 5 }}\n                    whileInView={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.4, delay: 0.4 + index * 0.05 }}\n                    viewport={{ once: true }}\n                    className=\"border-border/40 mt-auto flex items-center gap-3 border-t pt-2\"\n                  >\n                    <Avatar className=\"border-border ring-primary/10 ring-offset-background h-10 w-10 border ring-2 ring-offset-1\">\n                      <AvatarImage\n                        src={testimonial.imageSrc}\n                        alt={testimonial.name}\n                      />\n                      <AvatarFallback>\n                        {testimonial.name.charAt(0)}\n                      </AvatarFallback>\n                    </Avatar>\n                    <div className=\"flex flex-col\">\n                      <h4 className=\"text-foreground font-medium whitespace-nowrap\">\n                        {testimonial.name}\n                      </h4>\n                      <div className=\"flex items-center gap-2\">\n                        <p className=\"text-primary/80 text-sm whitespace-nowrap\">\n                          {testimonial.username}\n                        </p>\n                        {testimonial.role && (\n                          <>\n                            <span className=\"text-muted-foreground flex-shrink-0\">\n                              •\n                            </span>\n                            <p className=\"text-muted-foreground text-sm whitespace-nowrap\">\n                              {testimonial.role}\n                            </p>\n                          </>\n                        )}\n                      </div>\n                    </div>\n                  </motion.div>\n                </motion.div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/testimonials/testimonials-carousel.tsx", "target": "components/mvpblocks/testimonials-carousel.tsx"}]}