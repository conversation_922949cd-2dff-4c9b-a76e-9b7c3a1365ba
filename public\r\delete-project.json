{"name": "delete-project", "type": "registry:block", "dependencies": ["lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/dialog.json", "https://blocks.mvp-subha.me/r/input.json", "https://blocks.mvp-subha.me/r/label.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { useId, useState } from 'react';\nimport { CircleAlertIcon } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n  DialogTrigger,\n} from '@/components/ui/dialog';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\n\nconst PROJECT_NAME = 'Mvpblocks';\n\nexport default function DeleteProject() {\n  const id = useId();\n  const [inputValue, setInputValue] = useState('');\n\n  return (\n    <Dialog>\n      <DialogTrigger asChild>\n        <Button variant=\"destructive\">Delete project</Button>\n      </DialogTrigger>\n      <DialogContent>\n        <div className=\"flex flex-col items-center gap-2\">\n          <div\n            className=\"flex size-9 shrink-0 items-center justify-center rounded-full border\"\n            aria-hidden=\"true\"\n          >\n            <CircleAlertIcon className=\"opacity-80\" size={16} />\n          </div>\n          <DialogHeader>\n            <DialogTitle className=\"sm:text-center\">\n              Final confirmation\n            </DialogTitle>\n            <DialogDescription className=\"sm:text-center\">\n              This action cannot be undone. To confirm, please enter the project\n              name <span className=\"text-primary\">{PROJECT_NAME}</span>.\n            </DialogDescription>\n          </DialogHeader>\n        </div>\n\n        <form className=\"space-y-5\">\n          <div className=\"*:not-first:mt-2\">\n            <Label htmlFor={id}>Project name</Label>\n            <Input\n              id={id}\n              type=\"text\"\n              placeholder={`Type ${PROJECT_NAME} to confirm`}\n              value={inputValue}\n              onChange={(e) => setInputValue(e.target.value)}\n            />\n          </div>\n          <DialogFooter>\n            <DialogClose asChild>\n              <Button type=\"button\" variant=\"outline\" className=\"flex-1\">\n                Cancel\n              </Button>\n            </DialogClose>\n            <Button\n              type=\"button\"\n              variant=\"destructive\"\n              className=\"flex-1\"\n              disabled={inputValue !== PROJECT_NAME}\n            >\n              Delete\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  );\n}\n", "path": "/components/mvpblocks/basics/modals/delete-project.tsx", "target": "components/mvpblocks/delete-project.tsx"}]}