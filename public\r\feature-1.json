{"name": "feature-1", "type": "registry:block", "dependencies": [], "registryDependencies": [], "files": [{"type": "registry:block", "content": "import {\n  Code,\n  Terminal,\n  Paintbrush,\n  Rocket,\n  Book,\n  PlusCircle,\n} from 'lucide-react';\n\nconst features = [\n  {\n    icon: <Code className=\"h-6 w-6\" />,\n    title: 'Developer-Friendly',\n    desc: 'Tailored for developers to create and iterate fast, with minimal overhead and maximum flexibility.',\n  },\n  {\n    icon: <Terminal className=\"h-6 w-6\" />,\n    title: 'CLI Support',\n    desc: 'Command-line interface support for seamless development and workflow integration.',\n  },\n  {\n    icon: <Paintbrush className=\"h-6 w-6\" />,\n    title: 'Easily Customizable',\n    desc: 'Every block is built to be editable. From layout to logic, style to structure—make it your own.',\n  },\n  {\n    icon: <Rocket className=\"h-6 w-6\" />,\n    title: 'v0 Support',\n    desc: 'Launch fast with confidence. Perfect for MVPs, prototypes, and weekend projects.',\n  },\n  {\n    icon: <Book className=\"h-6 w-6\" />,\n    title: 'Full Documentation',\n    desc: 'Comprehensive documentation to understand every feature and maximize your development experience.',\n  },\n  {\n    icon: <PlusCircle className=\"h-6 w-6\" />,\n    title: 'Contribute Yours',\n    desc: 'Add your own blocks to the library and become part of the MVPBlocks community.',\n  },\n];\nexport default function Feature1() {\n  return (\n    <section className=\"relative py-14\">\n      <div className=\"mx-auto max-w-screen-xl px-4 md:px-8\">\n        <div className=\"relative mx-auto max-w-2xl sm:text-center\">\n          <div className=\"relative z-10\">\n            <h3 className=\"font-geist mt-4 text-3xl font-normal tracking-tighter sm:text-4xl md:text-5xl\">\n              Let’s help build your MVP\n            </h3>\n            <p className=\"font-geist text-foreground/60 mt-3\">\n              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec\n              congue, nisl eget molestie varius, enim ex faucibus purus.\n            </p>\n          </div>\n          <div\n            className=\"absolute inset-0 mx-auto h-44 max-w-xs blur-[118px]\"\n            style={{\n              background:\n                'linear-gradient(152.92deg, rgba(192, 15, 102, 0.2) 4.54%, rgba(192, 11, 109, 0.26) 34.2%, rgba(192, 15, 102, 0.1) 77.55%)',\n            }}\n          ></div>\n        </div>\n        <hr className=\"bg-foreground/30 mx-auto mt-5 h-px w-1/2\" />\n        <div className=\"relative mt-12\">\n          <ul className=\"grid gap-8 sm:grid-cols-2 lg:grid-cols-3\">\n            {features.map((item, idx) => (\n              <li\n                key={idx}\n                className=\"transform-gpu space-y-3 rounded-xl border bg-transparent p-4 [box-shadow:0_-20px_80px_-20px_#ff7aa42f_inset]\"\n              >\n                <div className=\"text-primary w-fit transform-gpu rounded-full border p-4 [box-shadow:0_-20px_80px_-20px_#ff7aa43f_inset] dark:[box-shadow:0_-20px_80px_-20px_#ff7aa40f_inset]\">\n                  {item.icon}\n                </div>\n                <h4 className=\"font-geist text-lg font-bold tracking-tighter\">\n                  {item.title}\n                </h4>\n                <p className=\"text-gray-500\">{item.desc}</p>\n              </li>\n            ))}\n          </ul>\n        </div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/features/feature-1.tsx", "target": "components/mvpblocks/feature-1.tsx"}]}