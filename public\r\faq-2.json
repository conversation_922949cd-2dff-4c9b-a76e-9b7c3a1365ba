{"name": "faq-2", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/badge.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { cn } from '@/lib/utils';\nimport { Badge } from '@/components/ui/badge';\nimport { MinusIcon, PlusIcon } from 'lucide-react';\n\ninterface FaqItem {\n  id: string;\n  question: string;\n  answer: string;\n  category: 'general' | 'pricing' | 'technical' | 'support';\n}\n\nconst faqItems: FaqItem[] = [\n  {\n    id: '1',\n    question: 'What is MVPBlocks?',\n    answer:\n      'MVPBlocks is a collection of ready-to-use UI components built with Next.js and Tailwind CSS. It helps developers quickly build beautiful, responsive websites without starting from scratch.',\n    category: 'general',\n  },\n  {\n    id: '2',\n    question: 'Is MVPBlocks free to use?',\n    answer:\n      'Yes, MVPBlocks is completely free and open-source. You can use it for personal and commercial projects without any restrictions or attribution requirements.',\n    category: 'general',\n  },\n  {\n    id: '3',\n    question: 'Do I need to know Tailwind CSS to use MVPBlocks?',\n    answer:\n      \"While having Tailwind CSS knowledge is helpful, it's not required. You can simply copy and paste our components into your project and make basic modifications without deep Tailwind expertise.\",\n    category: 'technical',\n  },\n  {\n    id: '4',\n    question: 'How do I install MVPBlocks?',\n    answer:\n      \"You don't need to install MVPBlocks as a package. Simply browse our component library, find the components you need, and copy the code into your project. Make sure you have the required dependencies installed.\",\n    category: 'technical',\n  },\n  {\n    id: '5',\n    question: 'Can I customize the components?',\n    answer:\n      'Absolutely! All components are built with customization in mind. You can modify colors, spacing, typography, and more using Tailwind classes or by editing the component code directly.',\n    category: 'technical',\n  },\n  {\n    id: '6',\n    question: 'Do MVPBlocks components work with dark mode?',\n    answer:\n      \"Yes, all MVPBlocks components are designed to work seamlessly with both light and dark modes. They automatically adapt to your site's theme settings.\",\n    category: 'technical',\n  },\n  {\n    id: '7',\n    question: 'How often are new components added?',\n    answer:\n      'We regularly add new components to the library. Our goal is to provide a comprehensive set of components for all common UI patterns and website sections.',\n    category: 'general',\n  },\n  {\n    id: '8',\n    question: 'How can I contribute to MVPBlocks?',\n    answer:\n      'We welcome contributions! You can contribute by creating new components, improving existing ones, fixing bugs, or enhancing documentation. Check our GitHub repository for contribution guidelines.',\n    category: 'support',\n  },\n];\n\nconst categories = [\n  { id: 'all', label: 'All' },\n  { id: 'general', label: 'General' },\n  { id: 'technical', label: 'Technical' },\n  { id: 'pricing', label: 'Pricing' },\n  { id: 'support', label: 'Support' },\n];\n\nexport default function Faq2() {\n  const [activeCategory, setActiveCategory] = useState<string>('all');\n  const [expandedId, setExpandedId] = useState<string | null>(null);\n\n  const filteredFaqs =\n    activeCategory === 'all'\n      ? faqItems\n      : faqItems.filter((item) => item.category === activeCategory);\n\n  const toggleExpand = (id: string) => {\n    setExpandedId(expandedId === id ? null : id);\n  };\n\n  return (\n    <section className=\"bg-background py-16\">\n      <div className=\"container mx-auto max-w-6xl px-4 md:px-6\">\n        <div className=\"mb-12 flex flex-col items-center\">\n          <Badge\n            variant=\"outline\"\n            className=\"border-primary mb-4 px-3 py-1 text-xs font-medium tracking-wider uppercase\"\n          >\n            FAQs\n          </Badge>\n\n          <h2 className=\"text-foreground mb-6 text-center text-4xl font-bold tracking-tight md:text-5xl\">\n            Frequently Asked Questions\n          </h2>\n\n          <p className=\"text-muted-foreground max-w-2xl text-center\">\n            Find answers to common questions about MVPBlocks and how to use our\n            components to build your next project.\n          </p>\n        </div>\n\n        {/* Category Tabs */}\n        <div className=\"mb-10 flex flex-wrap justify-center gap-2\">\n          {categories.map((category) => (\n            <button\n              key={category.id}\n              onClick={() => setActiveCategory(category.id)}\n              className={cn(\n                'rounded-full px-4 py-2 text-sm font-medium transition-all',\n                activeCategory === category.id\n                  ? 'bg-primary text-primary-foreground'\n                  : 'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n              )}\n            >\n              {category.label}\n            </button>\n          ))}\n        </div>\n\n        {/* FAQ Grid */}\n        <div className=\"grid grid-cols-1 gap-6 md:grid-cols-2\">\n          <AnimatePresence>\n            {filteredFaqs.map((faq, index) => (\n              <motion.div\n                key={faq.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3, delay: index * 0.05 }}\n                className={cn(\n                  'border-border h-fit overflow-hidden rounded-xl border',\n                  expandedId === faq.id\n                    ? 'shadow-3xl bg-card/50'\n                    : 'bg-card/50',\n                )}\n                style={{ minHeight: '88px' }}\n              >\n                <button\n                  onClick={() => toggleExpand(faq.id)}\n                  className=\"flex w-full items-center justify-between p-6 text-left\"\n                >\n                  <h3 className=\"text-foreground text-lg font-medium\">\n                    {faq.question}\n                  </h3>\n                  <div className=\"ml-4 flex-shrink-0\">\n                    {expandedId === faq.id ? (\n                      <MinusIcon className=\"text-primary h-5 w-5\" />\n                    ) : (\n                      <PlusIcon className=\"text-primary h-5 w-5\" />\n                    )}\n                  </div>\n                </button>\n\n                <AnimatePresence>\n                  {expandedId === faq.id && (\n                    <motion.div\n                      initial={{ height: 0, opacity: 0 }}\n                      animate={{ height: 'auto', opacity: 1 }}\n                      exit={{ height: 0, opacity: 0 }}\n                      transition={{ duration: 0.3 }}\n                      className=\"overflow-hidden\"\n                    >\n                      <div className=\"border-border border-t px-6 pt-2 pb-6\">\n                        <p className=\"text-muted-foreground\">{faq.answer}</p>\n                      </div>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n              </motion.div>\n            ))}\n          </AnimatePresence>\n        </div>\n\n        {/* Contact CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.5, duration: 0.5 }}\n          className=\"mt-16 text-center\"\n        >\n          <p className=\"text-muted-foreground mb-4\">\n            Can&apos;t find what you&apos;re looking for?\n          </p>\n          <a\n            href=\"#\"\n            className=\"border-primary text-foreground hover:bg-primary hover:text-primary-foreground inline-flex items-center justify-center rounded-lg border-2 px-6 py-3 font-medium transition-colors\"\n          >\n            Contact Support\n          </a>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/faqs/faq-2.tsx", "target": "components/mvpblocks/faq-2.tsx"}]}