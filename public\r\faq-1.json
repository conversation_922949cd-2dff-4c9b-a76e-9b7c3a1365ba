{"name": "faq-1", "type": "registry:block", "dependencies": ["@radix-ui/react-accordion", "framer-motion", "lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/accordion.json"], "files": [{"type": "registry:block", "content": "import * as AccordionPrimitive from '@radix-ui/react-accordion';\nimport { PlusIcon } from 'lucide-react';\nimport { motion } from 'framer-motion';\nimport { cn } from '@/lib/utils';\nimport {\n  Accordion,\n  AccordionContent,\n  AccordionItem,\n} from '@/components/ui/accordion';\n\nconst items = [\n  {\n    id: '1',\n    title: 'What makes MVPBlocks different?',\n    content:\n      'MVPBlocks is a fully open-source, developer-first component library built using Next.js and TailwindCSS, designed to help you launch your MVPs in record time. No bloated packages, no unnecessary installs—just clean, copyable code to plug right into your next big thing.',\n  },\n  {\n    id: '2',\n    title: 'How can I customize the components?',\n    content:\n      'All components are built with Tailwind CSS, making them highly customizable. Simply modify the class names or use our theme variables to match your brand. Components also support both light and dark modes out of the box.',\n  },\n  {\n    id: '3',\n    title: 'Are MVPBlocks components responsive?',\n    content:\n      \"Absolutely! All components are designed to be fully responsive and work beautifully on all devices, from mobile phones to large desktop screens. We've carefully crafted each component to provide an optimal experience regardless of screen size.\",\n  },\n  {\n    id: '4',\n    title: 'Can I use MVPBlocks for commercial projects?',\n    content:\n      'Yes, all MVPBlocks components are free to use for both personal and commercial projects. No attribution required—just build and launch your MVP faster than ever before.',\n  },\n  {\n    id: '5',\n    title: 'How do I get started with MVPBlocks?',\n    content:\n      \"Simply browse our component library, find the components you need, and copy the code into your project. It's that easy! Our documentation provides clear instructions for installation and usage.\",\n  },\n];\n\nconst fadeInAnimationVariants = {\n  initial: {\n    opacity: 0,\n    y: 10,\n  },\n  animate: (index: number) => ({\n    opacity: 1,\n    y: 0,\n    transition: {\n      delay: 0.05 * index,\n      duration: 0.4,\n    },\n  }),\n};\n\nexport default function Faq1() {\n  return (\n    <section className=\"py-12 md:py-16\">\n      <div className=\"container mx-auto max-w-6xl px-4 md:px-6\">\n        <div className=\"mb-10 text-center\">\n          <motion.h2\n            className=\"mb-4 text-3xl font-bold tracking-tight md:text-4xl\"\n            initial={{ opacity: 0, y: -10 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n          >\n            Frequently Asked{' '}\n            <span className=\"from-primary bg-gradient-to-r to-rose-400 bg-clip-text text-transparent\">\n              Questions\n            </span>\n          </motion.h2>\n          <motion.p\n            className=\"text-muted-foreground mx-auto max-w-2xl\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.5, delay: 0.2 }}\n          >\n            Everything you need to know about MVPBlocks and how to use our\n            components to build your next project quickly.\n          </motion.p>\n        </div>\n\n        <motion.div\n          className=\"relative mx-auto max-w-3xl\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 0.3 }}\n        >\n          {/* Decorative gradient */}\n          <div className=\"bg-primary/10 absolute -top-4 -left-4 -z-10 h-72 w-72 rounded-full blur-3xl\" />\n          <div className=\"bg-primary/10 absolute -right-4 -bottom-4 -z-10 h-72 w-72 rounded-full blur-3xl\" />\n\n          <Accordion\n            type=\"single\"\n            collapsible\n            className=\"border-border/40 bg-card/30 w-full rounded-xl border p-2 backdrop-blur-sm\"\n            defaultValue=\"1\"\n          >\n            {items.map((item, index) => (\n              <motion.div\n                key={item.id}\n                custom={index}\n                variants={fadeInAnimationVariants}\n                initial=\"initial\"\n                whileInView=\"animate\"\n                viewport={{ once: true }}\n              >\n                <AccordionItem\n                  value={item.id}\n                  className={cn(\n                    'bg-card/50 my-1 overflow-hidden rounded-lg border-none px-2 shadow-sm transition-all',\n                    'data-[state=open]:bg-card/80 data-[state=open]:shadow-md',\n                  )}\n                >\n                  <AccordionPrimitive.Header className=\"flex\">\n                    <AccordionPrimitive.Trigger\n                      className={cn(\n                        'group flex flex-1 items-center justify-between gap-4 py-4 text-left text-base font-medium',\n                        'hover:text-primary transition-all duration-300 outline-none',\n                        'focus-visible:ring-primary/50 focus-visible:ring-2',\n                        'data-[state=open]:text-primary',\n                      )}\n                    >\n                      {item.title}\n                      <PlusIcon\n                        size={18}\n                        className={cn(\n                          'text-primary/70 shrink-0 transition-transform duration-300 ease-out',\n                          'group-data-[state=open]:rotate-45',\n                        )}\n                        aria-hidden=\"true\"\n                      />\n                    </AccordionPrimitive.Trigger>\n                  </AccordionPrimitive.Header>\n                  <AccordionContent\n                    className={cn(\n                      'text-muted-foreground overflow-hidden pt-0 pb-4',\n                      'data-[state=open]:animate-accordion-down',\n                      'data-[state=closed]:animate-accordion-up',\n                    )}\n                  >\n                    <div className=\"border-border/30 border-t pt-3\">\n                      {item.content}\n                    </div>\n                  </AccordionContent>\n                </AccordionItem>\n              </motion.div>\n            ))}\n          </Accordion>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/faqs/faq-1.tsx", "target": "components/mvpblocks/faq-1.tsx"}]}