{"name": "system-status", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { memo } from 'react';\nimport { motion } from 'framer-motion';\nimport { Shield, Database, Zap, Activity } from 'lucide-react';\n\nconst statusItems = [\n  {\n    label: 'Server Status',\n    status: 'Online',\n    color: 'text-green-500',\n    icon: Shield,\n    percentage: 100,\n  },\n  {\n    label: 'Database',\n    status: 'Healthy',\n    color: 'text-green-500',\n    icon: Database,\n    percentage: 95,\n  },\n  {\n    label: 'API Response',\n    status: 'Fast',\n    color: 'text-green-500',\n    icon: Zap,\n    percentage: 98,\n  },\n  {\n    label: 'Storage',\n    status: '85% Used',\n    color: 'text-yellow-500',\n    icon: Activity,\n    percentage: 85,\n  },\n];\n\nexport const SystemStatus = memo(() => {\n  return (\n    <div className=\"border-border bg-card/40 rounded-xl border p-6\">\n      <h3 className=\"mb-4 text-xl font-semibold\">System Status</h3>\n      <div className=\"space-y-4\">\n        {statusItems.map((item, index) => {\n          const Icon = item.icon;\n          return (\n            <motion.div\n              key={item.label}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: index * 0.1 }}\n              className=\"hover:bg-accent/50 flex cursor-pointer items-center justify-between rounded-lg p-3 transition-colors\"\n            >\n              <div className=\"flex items-center gap-3\">\n                <Icon className={`h-4 w-4 ${item.color}`} />\n                <span className=\"text-sm font-medium\">{item.label}</span>\n              </div>\n              <div className=\"flex items-center gap-3\">\n                <div className=\"bg-muted h-2 w-16 overflow-hidden rounded-full\">\n                  <motion.div\n                    initial={{ width: 0 }}\n                    animate={{ width: `${item.percentage}%` }}\n                    transition={{ duration: 1, delay: index * 0.1 }}\n                    className={`h-full rounded-full ${item.color.replace('text-', 'bg-')}`}\n                  />\n                </div>\n                <span\n                  className={`text-sm font-medium ${item.color} min-w-[60px] text-right`}\n                >\n                  {item.status}\n                </span>\n              </div>\n            </motion.div>\n          );\n        })}\n      </div>\n    </div>\n  );\n});\n\nSystemStatus.displayName = 'SystemStatus';\n", "path": "/components/mvpblocks/dashboards/admin-dashboard-1/ui/system-status.tsx", "target": "components/mvpblocks/ui/system-status.tsx"}]}