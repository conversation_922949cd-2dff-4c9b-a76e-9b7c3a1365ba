{"name": "hero-1", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "import React from 'react';\nimport { ArrowRight } from 'lucide-react';\n\nexport default function Hero1() {\n  return (\n    <div className=\"relative w-full bg-neutral-950\">\n      <div className=\"absolute top-0 z-[0] h-full w-full bg-neutral-900/10 bg-[radial-gradient(ellipse_20%_80%_at_50%_-20%,rgba(120,119,198,0.3),rgba(255,255,255,0))]\"></div>\n      <section className=\"relative z-1 mx-auto max-w-full\">\n        <div className=\"pointer-events-none absolute h-full w-full overflow-hidden opacity-50 [perspective:200px]\">\n          <div className=\"absolute inset-0 [transform:rotateX(35deg)]\">\n            <div className=\"animate-grid [inset:0%_0px] [margin-left:-50%] [height:300vh] [width:600vw] [transform-origin:100%_0_0] [background-image:linear-gradient(to_right,rgba(255,255,255,0.25)_1px,transparent_0),linear-gradient(to_bottom,rgba(255,255,255,0.2)_1px,transparent_0)] [background-size:120px_120px] [background-repeat:repeat]\"></div>\n          </div>\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black to-transparent to-90%\"></div>\n        </div>\n\n        <div className=\"z-10 mx-auto max-w-screen-xl gap-12 px-4 py-28 text-gray-600 md:px-8\">\n          <div className=\"mx-auto max-w-3xl space-y-5 text-center leading-0 lg:leading-5\">\n            <h1 className=\"font-geist group mx-auto w-fit rounded-3xl border-[2px] border-white/5 bg-gradient-to-tr from-zinc-300/5 via-gray-400/5 to-transparent px-5 py-2 text-sm text-gray-400\">\n              Build products for everyone\n              <ArrowRight className=\"ml-2 inline h-4 w-4 duration-300 group-hover:translate-x-1\" />\n            </h1>\n\n            <h2 className=\"font-geist mx-auto bg-[linear-gradient(180deg,_#FFF_0%,_rgba(255,_255,_255,_0.00)_202.08%)] bg-clip-text text-4xl tracking-tighter text-transparent md:text-6xl\">\n              Designing your projects faster with{' '}\n              <span className=\"bg-gradient-to-r from-purple-300 to-orange-200 bg-clip-text text-transparent\">\n                the largest figma UI kit.\n              </span>\n            </h2>\n\n            <p className=\"mx-auto max-w-2xl text-gray-300\">\n              Sed ut perspiciatis unde omnis iste natus voluptatem accusantium\n              doloremque laudantium, totam rem aperiam, eaque ipsa quae.\n            </p>\n            <div className=\"items-center justify-center space-y-3 gap-x-3 sm:flex sm:space-y-0\">\n              <span className=\"relative inline-block overflow-hidden rounded-full p-[1.5px]\">\n                <span className=\"absolute inset-[-1000%] animate-[spin_2s_linear_infinite] bg-[conic-gradient(from_90deg_at_50%_50%,#E2CBFF_0%,#393BB2_50%,#E2CBFF_100%)]\" />\n                <div className=\"inline-flex h-full w-full cursor-pointer items-center justify-center rounded-full bg-gray-950 text-xs font-medium text-gray-50 backdrop-blur-3xl\">\n                  <a\n                    href=\"#\"\n                    className=\"group border-input inline-flex w-full items-center justify-center rounded-full border-[1px] bg-gradient-to-tr from-zinc-300/5 via-purple-400/20 to-transparent px-10 py-4 text-center text-white transition-colors hover:bg-transparent/90 sm:w-auto\"\n                  >\n                    Browse courses\n                  </a>\n                </div>\n              </span>\n            </div>\n          </div>\n          <div className=\"mx-10 mt-32\">\n            <img\n              src=\"https://i.postimg.cc/0yk8Vz7t/dashboard.webp\"\n              className=\"w-full rounded-lg border shadow-lg\"\n              alt=\"\"\n            />\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/hero/hero-1.tsx", "target": "components/mvpblocks/hero-1.tsx"}]}