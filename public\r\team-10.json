{"name": "team-10", "author": "mosespace", "type": "registry:block", "dependencies": ["lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport type React from 'react';\nimport { cn } from '@/lib/utils';\nimport { useState, useRef, useEffect } from 'react';\nimport { ChevronLeft, ChevronRight, Phone } from 'lucide-react';\n\ntype TeamMember = {\n  id: number;\n  name: string;\n  role: string;\n  image: string;\n  socialMedia?: {\n    linkedin?: string;\n    github?: string;\n  };\n};\n\ninterface TeamSectionProps {\n  title?: string;\n  subtitle?: string;\n  description?: string;\n  teamMembers: TeamMember[];\n  backgroundColor?: string;\n  textColor?: string;\n  accentColor?: string;\n  secondaryColor?: string;\n  className?: string;\n}\n\nconst dami_data: TeamMember[] = [\n  {\n    id: 1,\n    name: '<PERSON><PERSON>',\n    role: 'Chief Executive Officer',\n    image:\n      'https://img.freepik.com/free-psd/3d-rendering-avatar_23-2150833554.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 2,\n    name: '<PERSON>',\n    role: 'Chief Technology Officer',\n    image:\n      'https://img.freepik.com/premium-photo/png-headset-headphones-portrait-cartoon_53876-762197.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 3,\n    name: 'Zainab Rahman',\n    role: 'Chief Operations Officer',\n    image:\n      'https://img.freepik.com/premium-photo/png-cartoon-portrait-glasses-white-background_53876-905385.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 4,\n    name: 'Aiden Davis',\n    role: 'Chief Marketing Officer',\n    image:\n      'https://img.freepik.com/premium-psd/3d-avatar-character_975163-690.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 5,\n    name: 'Aysha Hussain',\n    role: 'UX Designer',\n    image:\n      'https://img.freepik.com/free-photo/fun-3d-illustration-american-referee_183364-81231.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 6,\n    name: 'Samira Shah',\n    role: 'Product Manager',\n    image:\n      'https://img.freepik.com/premium-psd/lego-character-with-blue-button-his-chest_1217673-223400.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 7,\n    name: 'Ethan Williams',\n    role: 'Backend Developer',\n    image:\n      'https://img.freepik.com/premium-photo/there-is-black-girl-with-headphones-yellow-jacket_1034474-106535.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 8,\n    name: 'Amina Khan',\n    role: 'Frontend Developer',\n    image:\n      'https://img.freepik.com/free-photo/portrait-young-student-with-book-education-day_23-2150980030.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n];\n\nexport interface SupportTeamProps extends TeamSectionProps {\n  ctaButtons?: Array<{\n    label: string;\n    href?: string;\n    isPrimary?: boolean;\n    icon?: React.ReactNode;\n    onClick?: () => void;\n  }>;\n}\n\nexport default function TeamSectionVariant6({\n  title = 'Our team of experts are here to help',\n  subtitle = 'Get support 24/7, with our award-winning support network of growth experts.',\n  teamMembers = dami_data,\n  accentColor = '#4f46e5',\n  secondaryColor = '#6b7280',\n  className,\n  ctaButtons = [\n    {\n      label: 'Book a call',\n      isPrimary: false,\n      icon: <Phone size={16} className=\"mr-2\" />,\n    },\n    { label: 'Book a demo', isPrimary: true },\n  ],\n}: SupportTeamProps) {\n  const scrollContainerRef = useRef<HTMLDivElement>(null);\n  const [canScrollLeft, setCanScrollLeft] = useState(false);\n  const [canScrollRight, setCanScrollRight] = useState(true);\n\n  const checkScrollButtons = () => {\n    if (scrollContainerRef.current) {\n      const { scrollLeft, scrollWidth, clientWidth } =\n        scrollContainerRef.current;\n      setCanScrollLeft(scrollLeft > 0);\n      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 10);\n    }\n  };\n\n  useEffect(() => {\n    checkScrollButtons();\n    window.addEventListener('resize', checkScrollButtons);\n    return () => window.removeEventListener('resize', checkScrollButtons);\n  }, []);\n\n  const scroll = (direction: 'left' | 'right') => {\n    if (scrollContainerRef.current) {\n      const { clientWidth } = scrollContainerRef.current;\n      const scrollAmount = clientWidth * 0.8;\n      scrollContainerRef.current.scrollBy({\n        left: direction === 'left' ? -scrollAmount : scrollAmount,\n        behavior: 'smooth',\n      });\n    }\n  };\n\n  return (\n    <section className={cn('w-full', className)}>\n      <div className=\"container mx-auto max-w-6xl px-4\">\n        <div className=\"rounded-2xl p-8 shadow-sm md:p-12\">\n          <div className=\"mb-8 text-center\">\n            <h2 className=\"mb-4 text-3xl font-semibold md:text-4xl\">{title}</h2>\n            <p\n              className=\"mx-auto max-w-2xl text-base\"\n              style={{ color: secondaryColor }}\n            >\n              {subtitle}\n            </p>\n\n            <div className=\"mt-8 flex flex-wrap justify-center gap-4\">\n              {ctaButtons.map((button, index) => (\n                <a\n                  key={index}\n                  href={button.href || '#'}\n                  className={cn(\n                    'flex items-center justify-center rounded-full px-6 py-2.5 text-sm font-medium transition-all',\n                    button.isPrimary\n                      ? 'text-white'\n                      : 'bg-gray-100 text-gray-800 hover:bg-gray-200',\n                  )}\n                  style={\n                    button.isPrimary ? { backgroundColor: accentColor } : {}\n                  }\n                  onClick={button.onClick}\n                >\n                  {button.icon}\n                  {button.label}\n                </a>\n              ))}\n            </div>\n          </div>\n\n          <div className=\"relative mt-12\">\n            {canScrollLeft && (\n              <button\n                onClick={() => scroll('left')}\n                className=\"bg-primary absolute top-1/2 left-0 z-10 -translate-y-1/2 rounded-full p-2 shadow-md\"\n                aria-label=\"Scroll left\"\n              >\n                <ChevronLeft size={20} />\n              </button>\n            )}\n\n            <div\n              ref={scrollContainerRef}\n              className=\"hide-scrollbar flex gap-4 overflow-x-auto pb-4\"\n              onScroll={checkScrollButtons}\n            >\n              {teamMembers.map((member) => (\n                <div\n                  key={member.id}\n                  className=\"bg-card/90 w-64 flex-shrink-0 overflow-hidden rounded-lg border border-white/10 opacity-100 shadow-sm transition-opacity transition-shadow hover:opacity-75 hover:shadow-md\"\n                >\n                  <div className=\"relative aspect-[4/3] overflow-hidden\">\n                    <img\n                      src={member.image || '/placeholder.svg'}\n                      alt={member.name}\n                      className=\"object-cover\"\n                    />\n                  </div>\n                  <div className=\"p-4\">\n                    <h3 className=\"font-medium\">{member.name}</h3>\n                    <p className=\"text-sm\" style={{ color: secondaryColor }}>\n                      {member.role}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {canScrollRight && (\n              <button\n                onClick={() => scroll('right')}\n                className=\"bg-primary absolute top-1/2 right-0 z-10 -translate-y-1/2 rounded-full p-2 shadow-md\"\n                aria-label=\"Scroll right\"\n              >\n                <ChevronRight size={20} />\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n\n      <style jsx global>{`\n        .hide-scrollbar::-webkit-scrollbar {\n          display: none;\n        }\n        .hide-scrollbar {\n          -ms-overflow-style: none;\n          scrollbar-width: none;\n        }\n      `}</style>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/team/team-10.tsx", "target": "components/mvpblocks/team-10.tsx"}]}