{"name": "login-form-3", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { useState } from 'react';\nimport {\n  Mail,\n  <PERSON>,\n  Eye,\n  EyeOff,\n  Loader2,\n  <PERSON><PERSON>,\n  Users,\n  Cloud,\n  ShieldCheck,\n  Github,\n} from 'lucide-react';\n\nexport default function SignInPage() {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setTimeout(() => {\n      alert('Login successful! (This is a demo)');\n      setLoading(false);\n    }, 2000);\n  };\n\n  return (\n    <div className=\"relative flex min-h-screen w-full items-center justify-center overflow-hidden p-4\">\n      <style jsx>{`\n        .login-btn {\n          background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);\n          position: relative;\n          overflow: hidden;\n        }\n        .login-btn::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: -100%;\n          width: 100%;\n          height: 100%;\n          background: linear-gradient(\n            90deg,\n            transparent,\n            rgba(255, 255, 255, 0.3),\n            transparent\n          );\n          transition: left 0.5s;\n        }\n        .login-btn:hover::before {\n          left: 100%;\n        }\n      `}</style>\n      <div className=\"z-10 w-full max-w-6xl\">\n        <div className=\"bg-secondary/50 overflow-hidden rounded-[40px] shadow-2xl\">\n          <div className=\"grid min-h-[700px] lg:grid-cols-2\">\n            {/* Left Side */}\n            <div className=\"brand-side relative m-4 rounded-3xl bg-[url('https://cdn.midjourney.com/299f94f9-ecb9-4b26-bead-010b8d8b01d9/0_0.png?w=800&q=80')] bg-cover p-12 text-white\">\n              <div>\n                <div className=\"mb-12 text-lg font-semibold uppercase\">\n                  PixelForge Studio\n                </div>\n                <h1 className=\"mb-4 text-6xl font-medium\">\n                  Create, Design, and Innovate\n                </h1>\n                <p className=\"mb-12 text-xl opacity-80\">\n                  Join thousands of creators who trust PixelForge Studio to\n                  bring their vision to life\n                </p>\n\n                <div className=\"space-y-6\">\n                  {[\n                    {\n                      icon: <Palette size={16} />,\n                      title: 'Advanced Design Tools',\n                      desc: 'Professional-grade tools for every project',\n                    },\n                    {\n                      icon: <Users size={16} />,\n                      title: 'Team Collaboration',\n                      desc: 'Work together seamlessly in real-time',\n                    },\n                    {\n                      icon: <Cloud size={16} />,\n                      title: 'Cloud Storage',\n                      desc: 'Access your projects from anywhere',\n                    },\n                    {\n                      icon: <ShieldCheck size={16} />,\n                      title: 'Enterprise Security',\n                      desc: 'Bank-level security for your data',\n                    },\n                  ].map(({ icon, title, desc }, i) => (\n                    <div\n                      key={i}\n                      className=\"feature-item animate-fadeInUp flex items-center\"\n                      style={{ animationDelay: `${0.2 * (i + 1)}s` }}\n                    >\n                      <div className=\"mr-4 flex h-8 w-8 items-center justify-center rounded-lg bg-white/20 text-white backdrop-blur-sm\">\n                        {icon}\n                      </div>\n                      <div>\n                        <div className=\"font-semibold\">{title}</div>\n                        <div className=\"text-sm opacity-70\">{desc}</div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Right Side */}\n            <div className=\"flex flex-col justify-center p-12\">\n              <div className=\"mx-auto w-full max-w-md\">\n                <div className=\"mb-8 text-center\">\n                  <h2 className=\"text-3xl font-light uppercase\">\n                    Welcome back\n                  </h2>\n                  <p className=\"mt-2 text-sm text-stone-600\">\n                    Sign in to continue your creative journey\n                  </p>\n                </div>\n\n                <form onSubmit={handleSubmit} className=\"space-y-6\">\n                  <div>\n                    <label\n                      htmlFor=\"email\"\n                      className=\"mb-2 block text-sm font-medium uppercase\"\n                    >\n                      Email address\n                    </label>\n                    <div className=\"relative\">\n                      <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n                        <Mail className=\"h-5 w-5 text-gray-400\" />\n                      </div>\n                      <input\n                        id=\"email\"\n                        type=\"email\"\n                        value={email}\n                        onChange={(e) => setEmail(e.target.value)}\n                        required\n                        className=\"border-border bg-input block w-full rounded-lg border py-3 pr-3 pl-10 text-sm\"\n                        placeholder=\"Enter your email\"\n                      />\n                    </div>\n                  </div>\n\n                  <div>\n                    <label\n                      htmlFor=\"password\"\n                      className=\"mb-2 block text-sm font-medium uppercase\"\n                    >\n                      Password\n                    </label>\n                    <div className=\"relative\">\n                      <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n                        <Lock className=\"h-5 w-5 text-gray-400\" />\n                      </div>\n                      <input\n                        id=\"password\"\n                        type={showPassword ? 'text' : 'password'}\n                        value={password}\n                        onChange={(e) => setPassword(e.target.value)}\n                        required\n                        className=\"border-border bg-input block w-full rounded-lg border py-3 pr-12 pl-10 text-sm\"\n                        placeholder=\"Enter your password\"\n                      />\n                      <button\n                        type=\"button\"\n                        className=\"absolute inset-y-0 right-0 flex items-center pr-3\"\n                        onClick={() => setShowPassword(!showPassword)}\n                      >\n                        {showPassword ? (\n                          <EyeOff className=\"h-5 w-5 text-gray-400\" />\n                        ) : (\n                          <Eye className=\"h-5 w-5 text-gray-400\" />\n                        )}\n                      </button>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <label className=\"text-muted-foreground flex items-center text-sm\">\n                      <input\n                        type=\"checkbox\"\n                        className=\"border-border text-primary h-4 w-4 rounded\"\n                      />\n                      <span className=\"ml-2\">Remember me</span>\n                    </label>\n                    <a\n                      href=\"#\"\n                      className=\"text-primary hover:text-primary/80 text-sm\"\n                    >\n                      Forgot password?\n                    </a>\n                  </div>\n\n                  <button\n                    type=\"submit\"\n                    className=\"login-btn relative flex w-full items-center justify-center rounded-lg px-4 py-3 text-sm font-medium text-white transition-all duration-300\"\n                    disabled={loading}\n                  >\n                    {loading ? (\n                      <>\n                        <Loader2 className=\"h-5 w-5 animate-spin\" />\n                        <span className=\"ml-2\">Signing in...</span>\n                      </>\n                    ) : (\n                      'Sign in to your account'\n                    )}\n                  </button>\n\n                  <div className=\"relative text-center text-sm text-stone-500\">\n                    <div className=\"absolute inset-0 flex items-center\">\n                      <div className=\"border-border w-full border-t\"></div>\n                    </div>\n                    <span className=\"relative px-2\">Or continue with</span>\n                  </div>\n\n                  <div className=\"grid grid-cols-2 gap-3\">\n                    <button\n                      type=\"button\"\n                      className=\"border-border bg-secondary text-foreground hover:bg-secondary/80 flex items-center justify-center rounded-lg border px-4 py-2.5 text-sm shadow-sm\"\n                    >\n                      <img\n                        src=\"https://www.svgrepo.com/show/475656/google-color.svg\"\n                        className=\"h-5 w-5\"\n                        alt=\"Google\"\n                      />\n                      <span className=\"ml-2\">Google</span>\n                    </button>\n                    <button\n                      type=\"button\"\n                      className=\"border-border bg-secondary text-foreground hover:bg-secondary/80 flex items-center justify-center rounded-lg border px-4 py-2.5 text-sm shadow-sm\"\n                    >\n                      <Github className=\"h-5 w-5\" />\n                      <span className=\"ml-2\">GitHub</span>\n                    </button>\n                  </div>\n                </form>\n\n                <div className=\"text-muted-foreground mt-8 text-center text-sm\">\n                  Don&apos;t have an account?{' '}\n                  <a href=\"#\" className=\"text-primary hover:text-primary/80\">\n                    Sign up for free\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/forms/login-form-3.tsx", "target": "components/mvpblocks/login-form-3.tsx"}]}