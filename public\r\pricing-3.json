{"name": "pricing-3", "author": "mosespace", "type": "registry:block", "dependencies": ["lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/button.json"], "files": [{"type": "registry:block", "content": "import { Button } from '@/components/ui/button';\nimport { Check } from 'lucide-react';\nimport Link from 'next/link';\n\n// Pricing data\ntype ButtonVariant =\n  | 'outline'\n  | 'default'\n  | 'link'\n  | 'destructive'\n  | 'secondary'\n  | 'ghost';\n\ntype Plan = {\n  name: string;\n  price: string;\n  description: string;\n  features: string[];\n  buttonVariant: ButtonVariant;\n  span: number;\n  highlight: boolean;\n};\n\nconst plans: Plan[] = [\n  {\n    name: 'Free',\n    price: '$0 / mo',\n    description: 'Per editor',\n    features: [\n      'Basic Analytics Dashboard',\n      '5GB Cloud Storage',\n      'Email and Chat Support',\n    ],\n    buttonVariant: 'outline',\n    span: 2,\n    highlight: false,\n  },\n  {\n    name: 'Pro',\n    price: '$19 / mo',\n    description: 'Per editor',\n    features: [\n      'Everything in Free Plan',\n      '5GB Cloud Storage',\n      'Email and Chat Support',\n      'Access to Community Forum',\n      'Single User Access',\n      'Access to Basic Templates',\n      'Mobile App Access',\n      '1 Custom Report Per Month',\n      'Monthly Product Updates',\n      'Standard Security Features',\n    ],\n    buttonVariant: 'default',\n    span: 3,\n    highlight: true,\n  },\n];\n\nexport default function PricingThree() {\n  return (\n    <section className=\"note-prose relative w-full py-16 md:py-32\">\n      <div className=\"absolute inset-0 -z-10 overflow-hidden\">\n        <div className=\"bg-primary/10 absolute -top-[10%] left-[50%] h-[40%] w-[60%] -translate-x-1/2 rounded-full blur-3xl\" />\n        <div className=\"bg-primary/5 absolute -right-[10%] -bottom-[10%] h-[40%] w-[40%] rounded-full blur-3xl\" />\n        <div className=\"bg-primary/5 absolute -bottom-[10%] -left-[10%] h-[40%] w-[40%] rounded-full blur-3xl\" />\n      </div>\n      <div className=\"mx-auto max-w-5xl px-6\">\n        <div className=\"mx-auto max-w-2xl space-y-6 text-center\">\n          <h1 className=\"text-center text-4xl font-semibold lg:text-5xl\">\n            Pricing that Scales with You\n          </h1>\n          <p>\n            Gemini is evolving to be more than just the models. It supports an\n            entire suite of APIs and platforms helping developers and businesses\n            innovate.\n          </p>\n        </div>\n\n        <div className=\"mt-8 grid gap-6 md:mt-20 md:grid-cols-5 md:gap-0\">\n          {plans.map((plan, index) => (\n            <div\n              key={index}\n              className={`flex flex-col justify-between space-y-8 rounded-(--radius) border p-6 lg:p-10 ${\n                plan.highlight\n                  ? 'dark:bg-muted shadow-lg shadow-gray-950/5 md:col-span-3 dark:[--color-muted:var(--color-zinc-900)]'\n                  : 'md:col-span-2 md:my-2 md:rounded-r-none md:border-r-0'\n              }`}\n            >\n              {/* Header */}\n              <div className=\"space-y-4\">\n                <div>\n                  <h2 className=\"font-medium\">{plan.name}</h2>\n                  <span className=\"my-3 block text-2xl font-semibold\">\n                    {plan.price}\n                  </span>\n                  <p className=\"text-muted-foreground text-sm\">\n                    {plan.description}\n                  </p>\n                </div>\n\n                <Button asChild variant={plan.buttonVariant} className=\"w-full\">\n                  <Link href=\"\">Get Started</Link>\n                </Button>\n              </div>\n\n              {/* Divider */}\n              {!plan.highlight && <hr className=\"border-dashed\" />}\n\n              {/* Features */}\n              {plan.highlight ? (\n                <div>\n                  <div className=\"text-sm font-medium\">\n                    Everything in free plus:\n                  </div>\n                  <ul className=\"mt-4 list-outside space-y-3 text-sm\">\n                    {plan.features.map((item, i) => (\n                      <li key={i} className=\"flex items-center gap-2\">\n                        <Check className=\"size-3\" />\n                        {item}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              ) : (\n                <ul className=\"list-outside space-y-3 text-sm\">\n                  {plan.features.map((item, i) => (\n                    <li key={i} className=\"flex items-center gap-2\">\n                      <Check className=\"size-3\" />\n                      {item}\n                    </li>\n                  ))}\n                </ul>\n              )}\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/pricing/pricing-3.tsx", "target": "components/mvpblocks/pricing-3.tsx"}]}