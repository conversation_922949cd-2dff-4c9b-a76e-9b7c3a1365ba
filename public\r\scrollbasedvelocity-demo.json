{"name": "scrollbasedvelocity-demo", "type": "registry:block", "dependencies": [], "registryDependencies": ["https://blocks.mvp-subha.me/r/scrollbasedvelocity.json", "https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "import { VelocityScroll } from '@/components/ui/scrollbasedvelocity';\n\nexport default function ScrollBasedVelocityDemo() {\n  return (\n    <VelocityScroll\n      className=\"px-6 text-center text-4xl font-bold tracking-tight md:text-7xl md:leading-[5rem]\"\n      text=\"Welcome to Mvpblocks\"\n      default_velocity={5}\n    />\n  );\n}\n", "path": "/components/mvpblocks/text-animations/scrollbasedvelocity-demo.tsx", "target": "components/mvpblocks/scrollbasedvelocity-demo.tsx"}]}