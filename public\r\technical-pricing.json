{"name": "technical-pricing", "type": "registry:block", "dependencies": ["react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { useState } from 'react';\n\nexport default function TechnicalPricing() {\n  const [isAnnual, setIsAnnual] = useState(true);\n  const prices = isAnnual ? ['$15', '$39', '$159'] : ['$19', '$49', '$199'];\n\n  return (\n    <>\n      <style jsx global>{`\n        body {\n          margin: 0;\n          background: #ffffff;\n          font-family: 'Courier New', monospace;\n        }\n        .dimension-line {\n          position: relative;\n        }\n        .dimension-line::before {\n          content: '';\n          position: absolute;\n          background: #000;\n          z-index: 1;\n        }\n        .dimension-h::before {\n          height: 1px;\n          width: 20px;\n          top: 50%;\n          left: -30px;\n        }\n        .dimension-v::before {\n          width: 1px;\n          height: 20px;\n          left: 50%;\n          top: -30px;\n        }\n        .dimension-bracket {\n          position: relative;\n        }\n        .dimension-bracket::after {\n          content: '';\n          position: absolute;\n          border: 1px solid #000;\n          z-index: 1;\n        }\n        .bracket-top::after {\n          top: -15px;\n          left: 0;\n          right: 0;\n          height: 10px;\n          border-bottom: none;\n        }\n        .bracket-left::after {\n          left: -15px;\n          top: 0;\n          bottom: 0;\n          width: 10px;\n          border-right: none;\n        }\n        .tech-card {\n          border: 2px solid #000;\n          background: #fff;\n          position: relative;\n        }\n        .tech-card::before {\n          content: '';\n          position: absolute;\n          top: 10px;\n          right: 10px;\n          width: 8px;\n          height: 8px;\n          border: 1px solid #000;\n          background: #fff;\n        }\n        .popular-card {\n          border: 3px solid #000;\n          background: #000;\n          color: #fff;\n        }\n        .popular-card::before {\n          background: #000;\n          border-color: #fff;\n        }\n        .grid-bg {\n          background-image:\n            linear-gradient(to right, #e5e5e5 1px, transparent 1px),\n            linear-gradient(to bottom, #e5e5e5 1px, transparent 1px);\n          background-size: 20px 20px;\n        }\n      `}</style>\n\n      <div className=\"grid-bg relative flex min-h-screen w-full flex-col items-center justify-center px-4 py-8\">\n        <div className=\"dimension-line dimension-v mb-8 text-center\">\n          <div className=\"mb-4 inline-block border border-black px-4 py-2\">\n            <span className=\"text-xs tracking-wider\">SPECIFICATION SHEET</span>\n          </div>\n          <h1 className=\"mb-3 text-4xl font-bold tracking-wider text-black\">\n            PRICING MATRIX\n          </h1>\n          <p className=\"mx-auto max-w-xl px-4 font-mono text-base text-black\">\n            TECHNICAL CONFIGURATIONS &amp; RESOURCE ALLOCATION\n          </p>\n        </div>\n\n        <div className=\"dimension-line dimension-h mb-10 flex items-center justify-center gap-4\">\n          <span className=\"font-mono text-sm text-black\">MONTHLY</span>\n          <button\n            type=\"button\"\n            aria-label=\"Toggle monthly / annual pricing\"\n            onClick={() => setIsAnnual((prev) => !prev)}\n            className=\"relative inline-block h-6 w-12 cursor-pointer border-2 border-black bg-white focus:outline-none\"\n          >\n            <span\n              className=\"absolute top-0 h-6 w-6 border border-black bg-white transition-transform duration-300\"\n              style={{\n                transform: isAnnual ? 'translateX(24px)' : 'translateX(0)',\n              }}\n            />\n          </button>\n          <span className=\"font-mono text-sm text-black\">\n            ANNUAL <span className=\"text-xs\">[-20%]</span>\n          </span>\n        </div>\n\n        <div className=\"dimension-bracket bracket-top grid w-full max-w-6xl grid-cols-1 gap-8 lg:grid-cols-3\">\n          <div className=\"tech-card dimension-line dimension-h flex flex-col p-6\">\n            <div className=\"mb-6 flex items-center\">\n              <div className=\"flex h-10 w-10 items-center justify-center border border-black bg-white\">\n                <i className=\"fas fa-rocket text-sm text-black\" />\n              </div>\n              <div className=\"ml-4\">\n                <h3 className=\"text-xl font-bold tracking-wider text-black\">\n                  STARTER\n                </h3>\n                <span className=\"font-mono text-xs\">CONFIG-001</span>\n              </div>\n            </div>\n            <div className=\"mb-6 border-b border-black pb-4\">\n              <div className=\"flex items-baseline\">\n                <span className=\"price font-mono text-4xl font-bold text-black\">\n                  {prices[0]}\n                </span>\n                <span className=\"ml-2 font-mono text-sm text-black\">\n                  /MONTH\n                </span>\n              </div>\n              <p className=\"mt-1 font-mono text-sm text-black\">\n                INDIVIDUAL DEPLOYMENT\n              </p>\n            </div>\n            <ul className=\"mb-8 flex-grow space-y-3 font-mono text-sm\">\n              <li className=\"flex items-center text-black\">\n                <span className=\"mr-3 w-3 text-xs\">▪</span>1M TOKENS/MONTH\n              </li>\n              <li className=\"flex items-center text-black\">\n                <span className=\"mr-3 w-3 text-xs\">▪</span>5 CUSTOM MODELS\n              </li>\n              <li className=\"flex items-center text-black\">\n                <span className=\"mr-3 w-3 text-xs\">▪</span>BASIC API ACCESS\n              </li>\n              <li className=\"flex items-center text-black\">\n                <span className=\"mr-3 w-3 text-xs\">▪</span>EMAIL SUPPORT\n              </li>\n            </ul>\n            <button className=\"w-full border-2 border-black py-3 font-mono text-sm tracking-wider text-black transition-colors duration-300 hover:bg-black hover:text-white\">\n              INITIALIZE\n            </button>\n            <p className=\"mt-2 text-center font-mono text-xs text-black\">\n              NO PAYMENT REQUIRED\n            </p>\n          </div>\n\n          <div className=\"popular-card dimension-line dimension-v relative flex transform flex-col p-6 lg:scale-105\">\n            <span className=\"absolute -top-4 left-1/2 -translate-x-1/2 border border-white bg-black px-4 py-1 font-mono text-xs tracking-wider text-white\">\n              RECOMMENDED\n            </span>\n            <div className=\"mb-6 flex items-center\">\n              <div className=\"flex h-10 w-10 items-center justify-center border border-white bg-black\">\n                <i className=\"fas fa-bolt text-sm text-white\" />\n              </div>\n              <div className=\"ml-4\">\n                <h3 className=\"text-xl font-bold tracking-wider text-white\">\n                  PROFESSIONAL\n                </h3>\n                <span className=\"font-mono text-xs text-gray-300\">\n                  CONFIG-002\n                </span>\n              </div>\n            </div>\n            <div className=\"mb-6 border-b border-white pb-4\">\n              <div className=\"flex items-baseline\">\n                <span className=\"price font-mono text-4xl font-bold text-white\">\n                  {prices[1]}\n                </span>\n                <span className=\"ml-2 font-mono text-sm text-white\">\n                  /MONTH\n                </span>\n              </div>\n              <p className=\"mt-1 font-mono text-sm text-white\">\n                TEAM DEPLOYMENT\n              </p>\n            </div>\n            <ul className=\"mb-8 flex-grow space-y-3 font-mono text-sm text-white\">\n              <li className=\"flex items-center\">\n                <span className=\"mr-3 w-3 text-xs\">▪</span>10M TOKENS/MONTH\n              </li>\n              <li className=\"flex items-center\">\n                <span className=\"mr-3 w-3 text-xs\">▪</span>20 CUSTOM MODELS\n              </li>\n              <li className=\"flex items-center\">\n                <span className=\"mr-3 w-3 text-xs\">▪</span>ADVANCED API\n              </li>\n              <li className=\"flex items-center\">\n                <span className=\"mr-3 w-3 text-xs\">▪</span>PRIORITY SUPPORT\n              </li>\n              <li className=\"flex items-center\">\n                <span className=\"mr-3 w-3 text-xs\">▪</span>CUSTOM TRAINING\n              </li>\n            </ul>\n            <button className=\"w-full border-2 border-white bg-white py-3 font-mono text-sm tracking-wider text-black transition-colors duration-300 hover:border-black hover:bg-black hover:text-white\">\n              DEPLOY\n            </button>\n            <p className=\"mt-2 text-center font-mono text-xs text-gray-300\">\n              14-DAY TRIAL PERIOD\n            </p>\n          </div>\n\n          <div className=\"tech-card dimension-line dimension-h flex flex-col p-6\">\n            <div className=\"mb-6 flex items-center\">\n              <div className=\"flex h-10 w-10 items-center justify-center border border-black bg-white\">\n                <i className=\"fas fa-building text-sm text-black\" />\n              </div>\n              <div className=\"ml-4\">\n                <h3 className=\"text-xl font-bold tracking-wider text-black\">\n                  ENTERPRISE\n                </h3>\n                <span className=\"font-mono text-xs\">CONFIG-003</span>\n              </div>\n            </div>\n            <div className=\"mb-6 border-b border-black pb-4\">\n              <div className=\"flex items-baseline\">\n                <span className=\"price font-mono text-4xl font-bold text-black\">\n                  {prices[2]}\n                </span>\n                <span className=\"ml-2 font-mono text-sm text-black\">\n                  /MONTH\n                </span>\n              </div>\n              <p className=\"mt-1 font-mono text-sm text-black\">\n                ENTERPRISE DEPLOYMENT\n              </p>\n            </div>\n            <ul className=\"mb-8 flex-grow space-y-3 font-mono text-sm\">\n              <li className=\"flex items-center text-black\">\n                <span className=\"mr-3 w-3 text-xs\">▪</span>UNLIMITED TOKENS\n              </li>\n              <li className=\"flex items-center text-black\">\n                <span className=\"mr-3 w-3 text-xs\">▪</span>UNLIMITED MODELS\n              </li>\n              <li className=\"flex items-center text-black\">\n                <span className=\"mr-3 w-3 text-xs\">▪</span>FULL API ECOSYSTEM\n              </li>\n              <li className=\"flex items-center text-black\">\n                <span className=\"mr-3 w-3 text-xs\">▪</span>24/7 SUPPORT\n              </li>\n              <li className=\"flex items-center text-black\">\n                <span className=\"mr-3 w-3 text-xs\">▪</span>DEDICATED RESOURCES\n              </li>\n            </ul>\n            <button className=\"w-full border-2 border-black py-3 font-mono text-sm tracking-wider text-black transition-colors duration-300 hover:bg-black hover:text-white\">\n              CONTACT ENGINEERING\n            </button>\n            <p className=\"mt-2 text-center font-mono text-xs text-black\">\n              CUSTOM ARCHITECTURE\n            </p>\n          </div>\n        </div>\n\n        <div className=\"dimension-bracket bracket-left mt-12 max-w-4xl px-4 text-center\">\n          <div className=\"border border-black bg-white p-4\">\n            <p className=\"mb-4 font-mono text-sm text-black\">\n              ALL CONFIGURATIONS INCLUDE: 99.9% UPTIME SLA • ENTERPRISE SECURITY\n              • COMMUNITY ACCESS\n            </p>\n            <div className=\"flex flex-wrap justify-center gap-4\">\n              <span className=\"border border-black px-3 py-1 font-mono text-xs text-black\">\n                GDPR-COMPLIANT\n              </span>\n              <span className=\"border border-black px-3 py-1 font-mono text-xs text-black\">\n                SOC-2-CERTIFIED\n              </span>\n              <span className=\"border border-black px-3 py-1 font-mono text-xs text-black\">\n                HIPAA-READY\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/pricing/technical-pricing.tsx", "target": "components/mvpblocks/technical-pricing.tsx"}]}