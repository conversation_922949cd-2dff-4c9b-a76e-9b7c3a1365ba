{"name": "skeleton-table-two", "author": "midhunkalarikkal", "type": "registry:block", "dependencies": [], "registryDependencies": ["https://blocks.mvp-subha.me/r/input.json", "https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/dropdown-menu.json"], "files": [{"type": "registry:block", "content": "import { Input } from '@/components/ui/input';\nimport { Button } from '@/components/ui/button';\nimport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport {\n  ErrorMessage,\n  ShimmerTable,\n  SkeletonTableProps,\n} from './skeleton-table-1';\n\nconst RealTopBar: React.FC<SkeletonTableProps> = ({\n  showFilter,\n  showColumnToggle,\n}) => (\n  <div className=\"flex items-center py-4\">\n    {showFilter && <Input placeholder=\"Filter values\" className=\"max-w-sm\" />}\n    {showColumnToggle && (\n      <DropdownMenu>\n        <div className=\"ml-auto\">\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"outline\">Columns</Button>\n          </DropdownMenuTrigger>\n        </div>\n      </DropdownMenu>\n    )}\n  </div>\n);\n\nconst TableHeading: React.FC<SkeletonTableProps> = ({\n  columnCount,\n  showTableHeading,\n  tableHeadings,\n  columnWidthArray,\n}) => {\n  if (!columnCount || columnCount === 0)\n    return (\n      <ErrorMessage\n        message={'Please ensure that columnCount is greater than 0'}\n      />\n    );\n\n  if (columnWidthArray && columnCount !== columnWidthArray.length) {\n    return (\n      <ErrorMessage\n        message={\n          'Please ensure that columnCount and columnWidthArray length is equal'\n        }\n      />\n    );\n  }\n\n  const headings = showTableHeading\n    ? tableHeadings?.length === columnCount\n      ? tableHeadings\n      : Array.from({ length: columnCount }, () => '—')\n    : [];\n\n  return (\n    <div\n      className={`mb-1 flex h-10 items-center border-b-2 ${showTableHeading ? 'block' : 'hidden'}`}\n    >\n      {headings.map((heading, idx) => (\n        <div\n          key={idx}\n          className={`flex justify-center ${columnWidthArray ? columnWidthArray[idx] : 'w-full'} ${idx !== headings.length && 'border-l-2'}`}\n        >\n          <h3 className=\"truncate overflow-hidden text-xs font-semibold whitespace-nowrap md:text-[15px]\">\n            {heading}\n          </h3>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default function SkeletonTableTwoWrapper({\n  rowCount = 5,\n  columnCount = 5,\n  showTopBar = true,\n  showFilter = true,\n  showColumnToggle = true,\n  bodyClassName = 'px-10',\n  tableHeadings = ['Name', 'Email', 'Phone', 'Verified', 'Options'],\n  columnWidthArray = ['w-2/12', 'w-4/12', 'w-3/12', 'w-2/12', 'w-1/12'],\n}: SkeletonTableProps) {\n  return (\n    <div className={`w-full ${bodyClassName}`}>\n      {showTopBar && (\n        <RealTopBar\n          showFilter={showFilter}\n          showColumnToggle={showColumnToggle}\n        />\n      )}\n\n      <ShimmerTable\n        rowCount={rowCount}\n        columnCount={columnCount}\n        columnWidthArray={columnWidthArray}\n        renderHeading={\n          <TableHeading\n            columnCount={columnCount}\n            showTableHeading={true}\n            tableHeadings={tableHeadings}\n            columnWidthArray={columnWidthArray}\n          />\n        }\n      />\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/shimmers/skeleton-table-2.tsx", "target": "components/mvpblocks/skeleton-table-2.tsx"}]}