{"name": "textarea", "type": "registry:ui", "files": [{"type": "registry:ui", "content": "import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nconst Textarea = React.forwardRef<\n  HTMLTextAreaElement,\n  React.ComponentProps<'textarea'>\n>(({ className, ...props }, ref) => {\n  return (\n    <textarea\n      className={cn(\n        'border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex min-h-[80px] w-full rounded-md border px-3 py-2 text-base focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\n        className,\n      )}\n      ref={ref}\n      {...props}\n    />\n  );\n});\nTextarea.displayName = 'Textarea';\n\nexport { Textarea };\n", "path": "/components/ui/textarea.tsx", "target": "components/ui/textarea.tsx"}]}