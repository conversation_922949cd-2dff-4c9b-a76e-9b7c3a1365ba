{"name": "pricing-5", "type": "registry:block", "dependencies": ["lucide-react", "react", "@number-flow/react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/badge.json", "https://blocks.mvp-subha.me/r/button.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { useState } from 'react';\nimport { cn } from '@/lib/utils';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport NumberFlow from '@number-flow/react';\nimport { BadgeCheck } from 'lucide-react';\nimport { motion } from 'framer-motion';\n\nconst PAYMENT_FREQUENCIES: ('monthly' | 'yearly')[] = ['monthly', 'yearly'];\nconst TIERS = [\n  {\n    id: 'individuals',\n    name: 'Individuals',\n    price: {\n      monthly: 'Free',\n      yearly: 'Free',\n    },\n    description: 'For your hobby projects',\n    features: [\n      'Free email alerts',\n      '3-minute checks',\n      'Automatic data enrichment',\n      '10 monitors',\n      'Up to 3 seats',\n    ],\n    cta: 'Get started',\n  },\n  {\n    id: 'teams',\n    name: 'Teams',\n    price: {\n      monthly: 90,\n      yearly: 75,\n    },\n    description: 'Great for small businesses',\n    features: [\n      'Unlimited phone calls',\n      '30 second checks',\n      'Single-user account',\n      '20 monitors',\n      'Up to 6 seats',\n    ],\n    cta: 'Get started',\n    popular: true,\n  },\n  {\n    id: 'organizations',\n    name: 'Organizations',\n    price: {\n      monthly: 120,\n      yearly: 100,\n    },\n    description: 'Great for large businesses',\n    features: [\n      'Unlimited phone calls',\n      '15 second checks',\n      'Single-user account',\n      '50 monitors',\n      'Up to 10 seats',\n    ],\n    cta: 'Get started',\n  },\n  {\n    id: 'enterprise',\n    name: 'Enterprise',\n    price: {\n      monthly: 'Custom',\n      yearly: 'Custom',\n    },\n    description: 'For multiple teams',\n    features: [\n      'Everything in Organizations',\n      'Up to 5 team members',\n      '100 monitors',\n      '15 status pages',\n      '200+ integrations',\n    ],\n    cta: 'Contact Us',\n    highlighted: true,\n  },\n];\n\nconst HighlightedBackground = () => (\n  <div className=\"absolute inset-0 bg-[linear-gradient(to_right,#4f4f4f2e_1px,transparent_1px),linear-gradient(to_bottom,#4f4f4f2e_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_110%)] bg-[size:45px_45px] opacity-100 dark:opacity-30\" />\n);\n\nconst PopularBackground = () => (\n  <div className=\"absolute inset-0 bg-[radial-gradient(ellipse_80%_80%_at_50%_-20%,rgba(240,119,119,0.1),rgba(255,255,255,0))] dark:bg-[radial-gradient(ellipse_80%_80%_at_50%_-20%,rgba(220,119,118,0.3),rgba(255,255,255,0))]\" />\n);\n\nconst Tab = ({\n  text,\n  selected,\n  setSelected,\n  discount = false,\n}: {\n  text: string;\n  selected: boolean;\n  setSelected: (text: string) => void;\n  discount?: boolean;\n}) => {\n  return (\n    <button\n      onClick={() => setSelected(text)}\n      className={cn(\n        'text-foreground relative w-fit px-4 py-2 text-sm font-semibold capitalize transition-colors',\n        discount && 'flex items-center justify-center gap-2.5',\n      )}\n    >\n      <span className=\"relative z-10\">{text}</span>\n      {selected && (\n        <motion.span\n          layoutId=\"tab\"\n          transition={{ type: 'spring', duration: 0.4 }}\n          className=\"bg-background absolute inset-0 z-0 rounded-full shadow-sm\"\n        ></motion.span>\n      )}\n      {discount && (\n        <Badge\n          className={cn(\n            'relative z-10 bg-gray-100 text-xs whitespace-nowrap text-black shadow-none hover:bg-gray-100',\n            selected\n              ? 'bg-[#F3F4F6] hover:bg-[#F3F4F6]'\n              : 'bg-gray-300 hover:bg-gray-300',\n          )}\n        >\n          Save 35%\n        </Badge>\n      )}\n    </button>\n  );\n};\n\nconst PricingCard = ({\n  tier,\n  paymentFrequency,\n}: {\n  tier: (typeof TIERS)[0];\n  paymentFrequency: keyof typeof tier.price;\n}) => {\n  const price = tier.price[paymentFrequency];\n  const isHighlighted = tier.highlighted;\n  const isPopular = tier.popular;\n\n  return (\n    <div\n      className={cn(\n        'relative flex flex-col gap-8 overflow-hidden rounded-2xl border p-6 shadow',\n        isHighlighted\n          ? 'bg-foreground text-background'\n          : 'bg-background text-foreground',\n        isPopular && 'outline outline-[#eb638a]',\n      )}\n    >\n      {isHighlighted && <HighlightedBackground />}\n      {isPopular && <PopularBackground />}\n\n      <h2 className=\"flex items-center gap-3 text-xl font-medium capitalize\">\n        {tier.name}\n        {isPopular && (\n          <Badge className=\"mt-1 bg-orange-900 px-1 py-0 text-white hover:bg-orange-900\">\n            🔥 Most Popular\n          </Badge>\n        )}\n      </h2>\n\n      <div className=\"relative h-12\">\n        {typeof price === 'number' ? (\n          <>\n            <NumberFlow\n              format={{\n                style: 'currency',\n                currency: 'USD',\n                trailingZeroDisplay: 'stripIfInteger',\n              }}\n              value={price}\n              className=\"text-4xl font-medium\"\n            />\n            <p className=\"-mt-2 text-xs font-medium\">Per month/user</p>\n          </>\n        ) : (\n          <h1 className=\"text-4xl font-medium\">{price}</h1>\n        )}\n      </div>\n\n      <div className=\"flex-1 space-y-2\">\n        <h3 className=\"text-sm font-medium\">{tier.description}</h3>\n        <ul className=\"space-y-2\">\n          {tier.features.map((feature, index) => (\n            <li\n              key={index}\n              className={cn(\n                'flex items-center gap-2 text-sm font-medium',\n                isHighlighted ? 'text-background' : 'text-foreground/60',\n              )}\n            >\n              <BadgeCheck strokeWidth={1} size={16} />\n              {feature}\n            </li>\n          ))}\n        </ul>\n      </div>\n\n      <Button\n        className={cn(\n          'h-fit w-full rounded-lg',\n          isHighlighted && 'bg-accent text-foreground hover:bg-accent/95',\n        )}\n      >\n        {tier.cta}\n      </Button>\n    </div>\n  );\n};\n\nexport default function PricingSection() {\n  const [selectedPaymentFreq, setSelectedPaymentFreq] = useState<\n    'monthly' | 'yearly'\n  >(PAYMENT_FREQUENCIES[0]);\n\n  return (\n    <section className=\"flex flex-col items-center gap-10 py-10\">\n      <div className=\"space-y-7 text-center\">\n        <div className=\"space-y-4\">\n          <h1 className=\"text-4xl font-medium md:text-5xl\">\n            Plans and Pricing\n          </h1>\n          <p>\n            Receive unlimited credits when you pay yearly, and save on your\n            plan.\n          </p>\n        </div>\n        <div className=\"mx-auto flex w-fit rounded-full bg-[#F3F4F6] p-1 dark:bg-[#222]\">\n          {PAYMENT_FREQUENCIES.map((freq) => (\n            <Tab\n              key={freq}\n              text={freq}\n              selected={selectedPaymentFreq === freq}\n              setSelected={(text) =>\n                setSelectedPaymentFreq(text as 'monthly' | 'yearly')\n              }\n              discount={freq === 'yearly'}\n            />\n          ))}\n        </div>\n      </div>\n\n      <div className=\"grid w-full max-w-6xl grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4\">\n        {TIERS.map((tier, i) => (\n          <PricingCard\n            key={i}\n            tier={tier}\n            paymentFrequency={selectedPaymentFreq}\n          />\n        ))}\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/pricing/pricing-5.tsx", "target": "components/mvpblocks/pricing-5.tsx"}]}