---
title: Authentication
description: Authentication forms are essential for user registration and login. They help secure your application and manage user access.
root: forms
---

import {ComponentPreview} from "@/components/preview/component-preview";
import {extractSourceCode} from "@/lib/code";

## Sign-In Page

<ComponentPreview
  name="login-form-3"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode('login-form-3')).code}
  lang="tsx"
  fromDocs={true}
/>

## Login Page

<ComponentPreview
  name="login-form-2"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode('login-form-2')).code}
  lang="tsx"
  fromDocs={true}
  hasReTrigger={true}
/>

## Login Form

<ComponentPreview
  name="login-form1"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode('login-form1')).code}
  lang="tsx"
/>