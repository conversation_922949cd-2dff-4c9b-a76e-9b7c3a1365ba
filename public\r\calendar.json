{"name": "calendar", "type": "registry:ui", "dependencies": ["react-day-picker@8.10.1", "date-fns"], "registryDependencies": ["button"], "files": [{"type": "registry:ui", "content": "'use client';\n\nimport * as React from 'react';\nimport { ChevronLeft, ChevronRight } from 'lucide-react';\nimport { DayPicker } from 'react-day-picker';\n\nimport { cn } from '@/lib/utils';\nimport { buttonVariants } from '@/components/ui/button';\n\nexport type CalendarProps = React.ComponentProps<typeof DayPicker>;\n\nfunction Calendar({\n  className,\n  classNames,\n  showOutsideDays = true,\n  ...props\n}: CalendarProps) {\n  return (\n    <DayPicker\n      showOutsideDays={showOutsideDays}\n      className={cn('p-3', className)}\n      classNames={{\n        months: 'flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0',\n        month: 'space-y-4',\n        caption: 'flex justify-center pt-1 relative items-center',\n        caption_label: 'text-sm font-medium',\n        nav: 'space-x-1 flex items-center',\n        nav_button: cn(\n          buttonVariants({ variant: 'outline' }),\n          'h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100',\n        ),\n        nav_button_previous: 'absolute left-1',\n        nav_button_next: 'absolute right-1',\n        table: 'w-full border-collapse space-y-1',\n        head_row: 'flex',\n        head_cell:\n          'text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]',\n        row: 'flex w-full mt-2',\n        cell: 'h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20',\n        day: cn(\n          buttonVariants({ variant: 'ghost' }),\n          'h-9 w-9 p-0 font-normal aria-selected:opacity-100',\n        ),\n        day_range_end: 'day-range-end',\n        day_selected:\n          'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',\n        day_today: 'bg-accent text-accent-foreground',\n        day_outside:\n          'day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground',\n        day_disabled: 'text-muted-foreground opacity-50',\n        day_range_middle:\n          'aria-selected:bg-accent aria-selected:text-accent-foreground',\n        day_hidden: 'invisible',\n        ...classNames,\n      }}\n      // Remove unsupported 'components' prop; DayPicker uses default icons or you can customize navigation via 'components.Nav'\n      {...props}\n    />\n  );\n}\nCalendar.displayName = 'Calendar';\n\nexport { Calendar };\n", "path": "/components/ui/calendar.tsx", "target": "components/ui/calendar.tsx"}]}