{"name": "slider", "type": "registry:ui", "dependencies": ["@radix-ui/react-slider"], "files": [{"type": "registry:ui", "content": "'use client';\n\nimport * as React from 'react';\nimport * as SliderPrimitive from '@radix-ui/react-slider';\n\nimport { cn } from '@/lib/utils';\n\nconst Slider = React.forwardRef<\n  React.ElementRef<typeof SliderPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <SliderPrimitive.Root\n    ref={ref}\n    className={cn(\n      'relative flex w-full touch-none items-center select-none',\n      className,\n    )}\n    {...props}\n  >\n    <SliderPrimitive.Track className=\"bg-secondary relative h-2 w-full grow overflow-hidden rounded-full\">\n      <SliderPrimitive.Range className=\"bg-primary absolute h-full\" />\n    </SliderPrimitive.Track>\n    <SliderPrimitive.Thumb className=\"border-primary bg-background ring-offset-background focus-visible:ring-ring block h-5 w-5 rounded-full border-2 transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50\" />\n  </SliderPrimitive.Root>\n));\nSlider.displayName = SliderPrimitive.Root.displayName;\n\nexport { Slider };\n", "path": "/components/ui/slider.tsx", "target": "components/ui/slider.tsx"}]}