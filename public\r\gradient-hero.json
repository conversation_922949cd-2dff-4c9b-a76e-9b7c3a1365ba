{"name": "gradient-hero", "author": "Xeven777", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { <PERSON>R<PERSON>, ChevronRight, ExternalLink, Github } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { cn } from '@/lib/utils';\n\nexport default function GradientHero() {\n  return (\n    <div className=\"bg-background relative w-full overflow-hidden\">\n      {/* Background gradient */}\n      <div className=\"absolute inset-0 z-0\">\n        <div className=\"from-primary/20 via-background to-background absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))]\"></div>\n        <div className=\"bg-primary/5 absolute top-0 left-1/2 -z-10 h-[1000px] w-[1000px] -translate-x-1/2 rounded-full blur-3xl\"></div>\n      </div>\n      <div className=\"absolute inset-0 bg-[linear-gradient(to_right,#8882_1px,transparent_1px),linear-gradient(to_bottom,#8882_1px,transparent_1px)] bg-[size:16px_16px] opacity-15\"></div>\n\n      <div className=\"relative z-10 container mx-auto px-4 py-24 sm:px-6 lg:px-8 lg:py-32\">\n        <div className=\"mx-auto max-w-5xl\">\n          {/* Badge */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n            className=\"mx-auto mb-6 flex justify-center\"\n          >\n            <div className=\"border-border bg-background/80 inline-flex items-center rounded-full border px-3 py-1 text-sm backdrop-blur-sm\">\n              <span className=\"bg-primary mr-2 rounded-full px-2 py-0.5 text-xs font-semibold text-white\">\n                New\n              </span>\n              <span className=\"text-muted-foreground\">\n                Introducing our latest component library\n              </span>\n              <ChevronRight className=\"text-muted-foreground ml-1 h-4 w-4\" />\n            </div>\n          </motion.div>\n\n          {/* Heading */}\n          <motion.h1\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.1 }}\n            className=\"from-primary/10 via-foreground/85 to-foreground/50 bg-gradient-to-tl bg-clip-text text-center text-4xl tracking-tighter text-balance text-transparent sm:text-5xl md:text-6xl lg:text-7xl\"\n          >\n            Build beautiful interfaces with speed and precision\n          </motion.h1>\n\n          {/* Description */}\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.2 }}\n            className=\"text-muted-foreground mx-auto mt-6 max-w-2xl text-center text-lg\"\n          >\n            A modern UI component library designed to help developers create\n            stunning web applications with minimal effort. Fully customizable,\n            responsive, and accessible.\n          </motion.p>\n\n          {/* CTA Buttons */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.3 }}\n            className=\"mt-10 flex flex-col items-center justify-center gap-4 sm:flex-row\"\n          >\n            <Button\n              size=\"lg\"\n              className=\"group bg-primary text-primary-foreground hover:shadow-primary/30 relative overflow-hidden rounded-full px-6 shadow-lg transition-all duration-300\"\n            >\n              <span className=\"relative z-10 flex items-center\">\n                Get Started\n                <ArrowRight className=\"ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1\" />\n              </span>\n              <span className=\"from-primary via-primary/90 to-primary/80 absolute inset-0 z-0 bg-gradient-to-r opacity-0 transition-opacity duration-300 group-hover:opacity-100\"></span>\n            </Button>\n\n            <Button\n              variant=\"outline\"\n              size=\"lg\"\n              className=\"border-border bg-background/50 flex items-center gap-2 rounded-full backdrop-blur-sm\"\n            >\n              <Github className=\"h-4 w-4\" />\n              Star on GitHub\n            </Button>\n          </motion.div>\n\n          {/* Feature Image */}\n          <motion.div\n            initial={{ opacity: 0, y: 40 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{\n              duration: 0.8,\n              delay: 0.5,\n              type: 'spring',\n              stiffness: 50,\n            }}\n            className=\"relative mx-auto mt-16 max-w-4xl\"\n          >\n            <div className=\"border-border/40 bg-background/50 overflow-hidden rounded-xl border shadow-xl backdrop-blur-sm\">\n              <div className=\"border-border/40 bg-muted/50 flex h-10 items-center border-b px-4\">\n                <div className=\"flex space-x-2\">\n                  <div className=\"h-3 w-3 rounded-full bg-red-500\"></div>\n                  <div className=\"h-3 w-3 rounded-full bg-yellow-500\"></div>\n                  <div className=\"h-3 w-3 rounded-full bg-green-500\"></div>\n                </div>\n                <div className=\"bg-background/50 text-muted-foreground mx-auto flex items-center rounded-md px-3 py-1 text-xs\">\n                  https://your-awesome-app.com\n                </div>\n              </div>\n              <div className=\"relative\">\n                <img\n                  src=\"https://i.postimg.cc/0yk8Vz7t/dashboard.webp\"\n                  alt=\"Dashboard Preview\"\n                  className=\"w-full\"\n                />\n                <div className=\"from-background absolute inset-0 bg-gradient-to-t to-transparent opacity-0\"></div>\n              </div>\n            </div>\n\n            {/* Floating elements for visual interest */}\n            <div className=\"border-border/40 bg-background/80 absolute -top-6 -right-6 h-12 w-12 rounded-lg border p-3 shadow-lg backdrop-blur-md\">\n              <div className=\"bg-primary/20 h-full w-full rounded-md\"></div>\n            </div>\n            <div className=\"border-border/40 bg-background/80 absolute -bottom-4 -left-4 h-8 w-8 rounded-full border shadow-lg backdrop-blur-md\"></div>\n            <div className=\"border-border/40 bg-background/80 absolute right-12 -bottom-6 h-10 w-10 rounded-lg border p-2 shadow-lg backdrop-blur-md\">\n              <div className=\"h-full w-full rounded-md bg-green-500/20\"></div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/hero/gradient-hero.tsx", "target": "components/mvpblocks/gradient-hero.tsx"}]}