{"name": "team-5", "author": "mosespace", "type": "registry:block", "dependencies": ["lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { cn } from '@/lib/utils';\nimport { Poppins } from 'next/font/google';\n\nconst mont = Poppins({\n  subsets: ['latin'],\n  weight: ['400', '500', '600', '700'],\n});\n\n// Team member data type\ntype TeamMember = {\n  id: number;\n  name: string;\n  role: string;\n  image: string;\n  troubleMaker?: boolean;\n};\n\ntype TeamSectionProps = {\n  teamMembers: TeamMember[];\n};\n\nconst teamMembers3D: TeamMember[] = [\n  {\n    id: 1,\n    name: '<PERSON><PERSON>',\n    role: 'Chief Executive Officer',\n    image:\n      'https://img.freepik.com/free-psd/3d-rendering-avatar_23-2150833554.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 2,\n    name: '<PERSON>',\n    role: 'Chief Technology Officer',\n    image:\n      'https://img.freepik.com/premium-photo/png-headset-headphones-portrait-cartoon_53876-762197.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 3,\n    name: '<PERSON><PERSON><PERSON>',\n    role: 'Chief Operations Officer',\n    image:\n      'https://img.freepik.com/premium-photo/png-cartoon-portrait-glasses-white-background_53876-905385.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 4,\n    name: 'Aiden Davis',\n    role: 'Chief Marketing Officer',\n    image:\n      'https://img.freepik.com/premium-psd/3d-avatar-character_975163-690.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 5,\n    name: 'Aysha Hussain',\n    role: 'UX Designer',\n    image:\n      'https://img.freepik.com/free-photo/fun-3d-illustration-american-referee_183364-81231.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 6,\n    name: 'Samira Shah',\n    role: 'Product Manager',\n    image:\n      'https://img.freepik.com/premium-psd/lego-character-with-blue-button-his-chest_1217673-223400.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 7,\n    name: 'Ethan Williams',\n    role: 'Backend Developer',\n    image:\n      'https://img.freepik.com/premium-photo/there-is-black-girl-with-headphones-yellow-jacket_1034474-106535.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 8,\n    name: 'Amina Khan',\n    role: 'Frontend Developer',\n    image:\n      'https://img.freepik.com/free-photo/portrait-young-student-with-book-education-day_23-2150980030.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n];\n\nexport default function Team5({\n  teamMembers = teamMembers3D.slice(0, 4),\n}: TeamSectionProps) {\n  return (\n    <section className=\"bg-background w-full py-16\">\n      <div className=\"container mx-auto max-w-6xl px-4\">\n        <div className=\"mx-auto mb-16 max-w-4xl text-center\">\n          <h2\n            className={cn(\n              'mb-6 text-4xl leading-tight font-semibold md:text-5xl',\n              mont.className,\n            )}\n          >\n            We bring a wealth of experience from a wide range of backgrounds\n          </h2>\n          <p className=\"text-foreground/80 text-lg\">\n            Our philosophy is simple; hire great people and give them the\n            resources and support to do their best work.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4\">\n          {teamMembers.map((member) => (\n            <div key={member.id} className=\"group\">\n              <div className=\"relative mb-4 aspect-square overflow-hidden rounded-sm\">\n                <img\n                  src={member.image}\n                  alt={member.name}\n                  className=\"object-cover transition-transform duration-300 ease-in-out group-hover:scale-110\"\n                />\n              </div>\n              <h3 className=\"text-xl font-medium\">{member.name}</h3>\n              <p className=\"text-primary text-base\">{member.role}</p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/team/team-5.tsx", "target": "components/mvpblocks/team-5.tsx"}]}