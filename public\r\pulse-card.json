{"name": "pulse-card", "type": "registry:ui", "dependencies": ["framer-motion", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:ui", "content": "import React from 'react';\nimport { motion } from 'framer-motion';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps {\n  icon: React.ReactNode;\n  title: string;\n  description: string;\n  className?: string;\n  variant?: 'emerald' | 'blue' | 'purple' | 'amber' | 'rose';\n  size?: 'sm' | 'md' | 'lg';\n  glowEffect?: boolean;\n  hoverScale?: number;\n  interactive?: boolean;\n  showGridLines?: boolean;\n}\n\nexport const VARIANTS = {\n  emerald: {\n    accent: 'rose-500',\n    gradient: 'from-rose-500/20 to-rose-500/0',\n    shine:\n      '205deg, transparent 0deg, hsl(160deg 95% 39%) 20deg, hsl(160deg 100% 85% / 0.3) 280deg',\n    border: 'rose-500/20',\n    color: 'rgb(244 63 94)',\n  },\n  blue: {\n    accent: 'blue-500',\n    gradient: 'from-blue-500/20 to-blue-500/0',\n    shine:\n      '205deg, transparent 0deg, hsl(220deg 95% 39%) 20deg, hsl(220deg 100% 85% / 0.3) 280deg',\n    border: 'blue-500/20',\n    color: 'rgb(244 63 94)',\n  },\n  purple: {\n    accent: 'purple-500',\n    gradient: 'from-purple-500/20 to-purple-500/0',\n    shine:\n      '205deg, transparent 0deg, hsl(280deg 95% 39%) 20deg, hsl(280deg 100% 85% / 0.3) 280deg',\n    border: 'purple-500/20',\n    color: 'rgb(244 63 94)',\n  },\n  amber: {\n    accent: 'amber-500',\n    gradient: 'from-amber-500/20 to-amber-500/0',\n    shine:\n      '205deg, transparent 0deg, hsl(40deg 95% 39%) 20deg, hsl(40deg 100% 85% / 0.3) 280deg',\n    border: 'amber-500/20',\n    color: 'rgb(244 63 94)',\n  },\n  rose: {\n    accent: 'rose-500',\n    gradient: 'from-rose-500/20 to-rose-500/0',\n    shine:\n      '205deg, transparent 0deg, hsl(340deg 95% 39%) 20deg, hsl(340deg 100% 85% / 0.3) 280deg',\n    border: 'rose-500/20',\n    color: 'rgb(244 63 94)',\n  },\n};\n\nconst SIZES = {\n  sm: {\n    padding: 'p-6 pt-12',\n    iconSize: 'h-5 w-5',\n    titleSize: 'text-sm',\n    descSize: 'text-xs',\n  },\n  md: {\n    padding: 'p-8 pt-16',\n    iconSize: 'h-6 w-6',\n    titleSize: 'text-base',\n    descSize: 'text-[15px]',\n  },\n  lg: {\n    padding: 'p-6 pt-16',\n    iconSize: 'h-7 w-7',\n    titleSize: 'text-lg',\n    descSize: 'text-base',\n  },\n};\n\nexport function CardHoverEffect({\n  icon,\n  title,\n  description,\n  className,\n  variant = 'emerald',\n  size = 'md',\n  glowEffect = false,\n  hoverScale = 1.02,\n  interactive = true,\n  showGridLines = true,\n}: CardProps) {\n  const variantConfig = VARIANTS[variant];\n  const sizeConfig = SIZES[size];\n\n  const Div = interactive ? motion.div : 'div';\n  const IconWrapper = interactive ? motion.span : 'span';\n\n  return (\n    <Div\n      whileHover={interactive ? { scale: hoverScale } : undefined}\n      transition={{ duration: 0.3, ease: 'easeInOut', type: 'keyframes' }}\n      className={cn(\n        'group relative z-30 w-full cursor-pointer overflow-hidden rounded-2xl',\n        sizeConfig.padding,\n        // Light mode styles\n        'bg-white/80 before:bg-linear-to-b before:from-white/5 before:to-white/20 before:backdrop-blur-3xl',\n        'after:bg-linear-to-b after:from-transparent after:via-transparent after:to-white/20',\n        // Dark mode styles\n        'dark:bg-black/5 dark:before:bg-linear-to-b dark:before:from-black/5 dark:before:to-black/20',\n        'dark:after:to-black/20',\n        // Common styles\n        \"before:absolute before:inset-0 before:rounded-[inherit] before:content-['']\",\n        \"after:absolute after:inset-0 after:rounded-[inherit] after:content-['']\",\n        glowEffect && `hover:before:bg-${variantConfig.accent}/10`,\n        // Shadows\n        'shadow-[0px_3px_8px_rgba(0,0,0,0.04),0px_12px_20px_rgba(0,0,0,0.08)]',\n        'hover:shadow-[0px_5px_15px_rgba(0,0,0,0.03),0px_25px_35px_rgba(0,0,0,0.2)]',\n        'dark:shadow-[0px_3px_8px_rgba(0,0,0,0.08),0px_12px_20px_rgba(0,0,0,0.15)]',\n        'dark:hover:shadow-[0px_5px_15px_rgba(0,0,0,0.06),0px_25px_35px_rgba(0,0,0,0.4)]',\n        className,\n      )}\n      style={\n        {\n          '--card-color': variantConfig.color,\n        } as React.CSSProperties\n      }\n    >\n      {/* Moving Border */}\n      <div\n        className=\"absolute inset-0 overflow-hidden rounded-[inherit]\"\n        style={{\n          mask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',\n          maskComposite: 'exclude',\n          padding: '2px',\n        }}\n      >\n        <div\n          className=\"absolute inset-[-200%] opacity-0 transition-opacity duration-300 group-hover:opacity-100\"\n          style={{\n            background: `conic-gradient(from 0deg at 50% 50%, transparent 0deg, transparent 340deg, var(--card-color) 360deg)`,\n            animation: 'spin 4s linear infinite',\n          }}\n        />\n      </div>\n\n      {/* Icon */}\n      <IconWrapper\n        className=\"relative z-50 table rounded-xl pb-2\"\n        whileHover={interactive ? { scale: 1.1 } : undefined}\n        transition={{ duration: 0.3, ease: 'easeInOut', type: 'keyframes' }}\n      >\n        <span\n          className={cn(\n            'absolute inset-[4.5px] rounded-[inherit]',\n            'bg-linear-to-b from-black/5 to-black/10 backdrop-blur-3xl',\n            'dark:from-white/10 dark:to-white/5',\n            'transition-all duration-300',\n          )}\n        />\n        <span\n          className={cn(\n            'relative z-1 block transition-colors duration-300',\n            'text-black/60 group-hover:text-[var(--card-color)]',\n            'dark:text-zinc-400',\n            sizeConfig.iconSize,\n          )}\n        >\n          {icon}\n        </span>\n      </IconWrapper>\n\n      {/* Content */}\n      <div className=\"relative z-30 mt-2\">\n        <h3\n          className={cn(\n            'font-medium transition-colors duration-300',\n            'text-black/80 group-hover:text-[var(--card-color)]',\n            'dark:text-white/80',\n            sizeConfig.titleSize,\n          )}\n        >\n          {title}\n        </h3>\n        <p\n          className={cn(\n            'mt-1 transition-colors duration-300',\n            'text-black/60',\n            'dark:text-white/40',\n            sizeConfig.descSize,\n          )}\n        >\n          {description}\n        </p>\n      </div>\n\n      {/* Shine Effect */}\n      <div className=\"absolute inset-0 z-20 overflow-hidden rounded-[inherit] opacity-100 transition-all duration-500\">\n        <div\n          className=\"absolute bottom-[55%] left-1/2 aspect-square w-[200%] -translate-x-1/2 rounded-[50%]\"\n          style={{\n            background: `conic-gradient(from ${variantConfig.shine}, transparent 360deg)`,\n            filter: 'blur(40px)',\n          }}\n        />\n      </div>\n    </Div>\n  );\n}\n", "path": "/components/ui/pulse-card.tsx", "target": "components/ui/pulse-card.tsx"}]}