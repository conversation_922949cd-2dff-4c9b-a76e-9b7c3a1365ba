{"name": "border-beam", "type": "registry:ui", "dependencies": [], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:ui", "content": "'use client';\n\nimport { cn } from '@/lib/utils';\nimport { motion, MotionStyle, Transition } from 'motion/react';\n\ninterface BorderBeamProps {\n  /**\n   * The size of the border beam.\n   */\n  size?: number;\n  /**\n   * The duration of the border beam.\n   */\n  duration?: number;\n  /**\n   * The delay of the border beam.\n   */\n  delay?: number;\n  /**\n   * The color of the border beam from.\n   */\n  colorFrom?: string;\n  /**\n   * The color of the border beam to.\n   */\n  colorTo?: string;\n  /**\n   * The motion transition of the border beam.\n   */\n  transition?: Transition;\n  /**\n   * The class name of the border beam.\n   */\n  className?: string;\n  /**\n   * The style of the border beam.\n   */\n  style?: React.CSSProperties;\n  /**\n   * Whether to reverse the animation direction.\n   */\n  reverse?: boolean;\n  /**\n   * The initial offset position (0-100).\n   */\n  initialOffset?: number;\n}\n\nexport const BorderBeam = ({\n  className,\n  size = 50,\n  delay = 0,\n  duration = 6,\n  colorFrom = '#ffaa40',\n  colorTo = '#9c40ff',\n  transition,\n  style,\n  reverse = false,\n  initialOffset = 0,\n}: BorderBeamProps) => {\n  return (\n    <div className=\"pointer-events-none absolute inset-0 rounded-[inherit] border border-transparent [mask-image:linear-gradient(transparent,transparent),linear-gradient(#000,#000)] [mask-composite:intersect] [mask-clip:padding-box,border-box]\">\n      <motion.div\n        className={cn(\n          'absolute aspect-square',\n          'bg-gradient-to-l from-[var(--color-from)] via-[var(--color-to)] to-transparent',\n          className,\n        )}\n        style={\n          {\n            width: size,\n            offsetPath: `rect(0 auto auto 0 round ${size}px)`,\n            '--color-from': colorFrom,\n            '--color-to': colorTo,\n            ...style,\n          } as MotionStyle\n        }\n        initial={{ offsetDistance: `${initialOffset}%` }}\n        animate={{\n          offsetDistance: reverse\n            ? [`${100 - initialOffset}%`, `${-initialOffset}%`]\n            : [`${initialOffset}%`, `${100 + initialOffset}%`],\n        }}\n        transition={{\n          repeat: Infinity,\n          ease: 'linear',\n          duration,\n          delay: -delay,\n          ...transition,\n        }}\n      />\n    </div>\n  );\n};\n", "path": "/components/ui/border-beam.tsx", "target": "components/ui/border-beam.tsx"}]}