{"name": "feature-3", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "import {\n  Building2,\n  Lightbulb,\n  ScreenShare,\n  Trophy,\n  User,\n  User2,\n  LucideIcon,\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\n// Define the feature item type\ntype FeatureItem = {\n  icon: LucideIcon;\n  title: string;\n  description: string;\n  position?: 'left' | 'right';\n  cornerStyle?: string;\n};\n\n// Create feature data arrays for left and right columns\nconst leftFeatures: FeatureItem[] = [\n  {\n    icon: Building2,\n    title: 'Taught by Professionals',\n    description:\n      'Learn directly from top engineers and founders with real-world experience.',\n    position: 'left',\n    cornerStyle: 'sm:translate-x-4 sm:rounded-br-[2px]',\n  },\n  {\n    icon: User2,\n    title: 'Coding Hostels',\n    description:\n      'Join virtual hostels to study, collaborate, and vibe with fellow learners.',\n    position: 'left',\n    cornerStyle: 'sm:-translate-x-4 sm:rounded-br-[2px]',\n  },\n  {\n    icon: Trophy,\n    title: 'Bounties',\n    description:\n      'Win rewards for solving challenges, contributing to projects, and helping peers.',\n    position: 'left',\n    cornerStyle: 'sm:translate-x-4 sm:rounded-tr-[2px]',\n  },\n];\n\nconst rightFeatures: FeatureItem[] = [\n  {\n    icon: ScreenShare,\n    title: 'Revision Classes',\n    description:\n      'Stay sharp with weekly revision sessions and topic refreshers.',\n    position: 'right',\n    cornerStyle: 'sm:-translate-x-4 sm:rounded-bl-[2px]',\n  },\n  {\n    icon: User,\n    title: 'Peer Code Reviews',\n    description:\n      'Improve faster with feedback from mentors and batchmates on your actual code.',\n    position: 'right',\n    cornerStyle: 'sm:translate-x-4 sm:rounded-bl-[2px]',\n  },\n  {\n    icon: Lightbulb,\n    title: 'Leet Lab',\n    description:\n      'Ace coding interviews with daily DSA problems, contests, and tracking.',\n    position: 'right',\n    cornerStyle: 'sm:-translate-x-4 sm:rounded-tl-[2px]',\n  },\n];\n\n// Feature card component\nconst FeatureCard = ({ feature }: { feature: FeatureItem }) => {\n  const Icon = feature.icon;\n\n  return (\n    <div>\n      <div\n        className={cn(\n          'relative rounded-2xl px-4 pt-4 pb-4 text-sm',\n          'bg-secondary/50 ring-border ring',\n          feature.cornerStyle,\n        )}\n      >\n        <div className=\"text-primary mb-3 text-[2rem]\">\n          <Icon />\n        </div>\n        <h2 className=\"text-foreground mb-2.5 text-2xl\">{feature.title}</h2>\n        <p className=\"text-muted-foreground text-base text-pretty\">\n          {feature.description}\n        </p>\n        {/* Decorative elements */}\n        <span className=\"from-primary/0 via-primary to-primary/0 absolute -bottom-px left-1/2 h-px w-1/2 -translate-x-1/2 bg-gradient-to-r opacity-60\"></span>\n        <span className=\"absolute inset-0 bg-[radial-gradient(30%_5%_at_50%_100%,hsl(var(--primary)/0.15)_0%,transparent_100%)] opacity-60\"></span>\n      </div>\n    </div>\n  );\n};\n\nexport default function Feature3() {\n  return (\n    <section className=\"pt-20 pb-8\" id=\"features\">\n      <div className=\"mx-6 max-w-[1120px] pt-2 pb-16 max-[300px]:mx-4 min-[1150px]:mx-auto\">\n        <div className=\"flex flex-col-reverse gap-6 md:grid md:grid-cols-3\">\n          {/* Left column */}\n          <div className=\"flex flex-col gap-6\">\n            {leftFeatures.map((feature, index) => (\n              <FeatureCard key={`left-feature-${index}`} feature={feature} />\n            ))}\n          </div>\n\n          {/* Center column */}\n          <div className=\"order-[1] mb-6 self-center sm:order-[0] md:mb-0\">\n            <div className=\"bg-secondary text-foreground ring-border relative mx-auto mb-4.5 w-fit rounded-full rounded-bl-[2px] px-4 py-2 text-sm ring\">\n              <span className=\"relative z-1 flex items-center gap-2\">\n                Features\n              </span>\n              <span className=\"from-primary/0 via-primary to-primary/0 absolute -bottom-px left-1/2 h-px w-2/5 -translate-x-1/2 bg-gradient-to-r\"></span>\n              <span className=\"absolute inset-0 bg-[radial-gradient(30%_40%_at_50%_100%,hsl(var(--primary)/0.25)_0%,transparent_100%)]\"></span>\n            </div>\n            <h2 className=\"text-foreground mb-2 text-center text-2xl sm:mb-2.5 md:text-[2rem]\">\n              Key Benefits of Cohorts\n            </h2>\n            <p className=\"text-muted-foreground mx-auto max-w-[18rem] text-center text-pretty\">\n              Cohorts are best way to learn because you finish the course in a\n              timely manner\n            </p>\n          </div>\n\n          {/* Right column */}\n          <div className=\"flex flex-col gap-6\">\n            {rightFeatures.map((feature, index) => (\n              <FeatureCard key={`right-feature-${index}`} feature={feature} />\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/features/feature-3.tsx", "target": "components/mvpblocks/feature-3.tsx"}]}