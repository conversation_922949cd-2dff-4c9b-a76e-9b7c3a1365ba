{"name": "bento-grid-2", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/card.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport type React from 'react';\nimport { cn } from '@/lib/utils';\nimport {\n  Home,\n  MapPin,\n  Compass,\n  Building,\n  Heart,\n  HomeIcon,\n  Camera,\n} from 'lucide-react';\nimport {\n  <PERSON>,\n  CardHeader,\n  CardContent,\n  CardFooter,\n} from '@/components/ui/card';\nimport { motion } from 'framer-motion';\n\ninterface BentoItem {\n  title: string;\n  description: string;\n  icon: React.ReactNode;\n  status?: string;\n  tags?: string[];\n  meta?: string;\n  cta?: string;\n  colSpan?: number;\n  hasPersistentHover?: boolean;\n}\n\ninterface BentoGridProps {\n  items: BentoItem[];\n}\n\nconst itemsSample: BentoItem[] = [\n  {\n    title: 'Component Library',\n    meta: '100+ components',\n    description:\n      'Explore our extensive collection of ready-to-use UI components built with Next.js and Tailwind CSS. Perfect for quickly building beautiful, responsive websites.',\n    icon: <Home className=\"text-primary h-4 w-4\" />,\n    status: 'Popular',\n    tags: ['UI', 'Components', 'Tailwind'],\n    colSpan: 2,\n    hasPersistentHover: true,\n  },\n  {\n    title: 'Responsive Design',\n    meta: 'All devices',\n    description:\n      'Every component is fully responsive and works beautifully on all screen sizes, from mobile to desktop.',\n    icon: <Building className=\"text-primary h-4 w-4\" />,\n    status: 'Essential',\n    tags: ['Mobile', 'Desktop'],\n  },\n  {\n    title: 'Theme Support',\n    description:\n      'All components support both light and dark modes out of the box, with seamless transitions.',\n    icon: <MapPin className=\"text-primary h-4 w-4\" />,\n    status: 'New',\n  },\n  {\n    title: 'Performance Optimized',\n    description:\n      'Built with performance in mind, ensuring fast load times and smooth interactions.',\n    icon: <HomeIcon className=\"text-primary h-4 w-4\" />,\n    meta: 'Lighthouse 100',\n    tags: ['Speed', 'Optimization'],\n  },\n  {\n    title: 'Accessibility',\n    description:\n      'All components follow WCAG guidelines and are fully accessible to all users.',\n    icon: <Heart className=\"text-primary h-4 w-4\" />,\n    meta: 'WCAG 2.1 AA',\n    tags: ['A11y', 'Inclusive'],\n  },\n  {\n    title: 'Developer Experience',\n    meta: 'TypeScript',\n    description:\n      'Clean, well-documented code with TypeScript support for a seamless development experience.',\n    icon: <Compass className=\"text-primary h-4 w-4\" />,\n    status: 'Featured',\n    tags: ['DX', 'TypeScript'],\n  },\n  {\n    title: 'Open Source',\n    meta: 'MIT License',\n    description:\n      'MVPBlocks is completely free and open-source. Use it for personal and commercial projects without any restrictions or attribution requirements.',\n    icon: <Camera className=\"text-primary h-4 w-4\" />,\n    status: 'Free',\n    tags: ['Open Source', 'MIT'],\n    colSpan: 2,\n  },\n];\n\nexport default function BentoGrid({ items = itemsSample }: BentoGridProps) {\n  return (\n    <section className=\"relative overflow-hidden py-12\">\n      {/* Decorative elements */}\n      <div className=\"bg-primary/5 absolute top-20 -left-20 h-64 w-64 rounded-full blur-3xl\" />\n      <div className=\"bg-primary/5 absolute -right-20 bottom-20 h-64 w-64 rounded-full blur-3xl\" />\n\n      <div className=\"relative mx-auto grid max-w-6xl grid-cols-1 gap-4 p-4 md:grid-cols-3\">\n        {items.map((item, index) => (\n          <motion.a\n            href=\"#\"\n            key={`${item.title}-${item.status || item.meta}`}\n            className={cn(\n              item.colSpan || 'col-span-1',\n              item.colSpan === 2 ? 'md:col-span-2' : '',\n            )}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.4, delay: index * 0.1 }}\n          >\n            <Card\n              className={cn(\n                'group bg-card/40 relative h-full transition-all duration-300 hover:shadow-md',\n                'will-change-transform hover:-translate-y-1',\n                'border-border/60 overflow-hidden',\n                {\n                  '-translate-y-1 shadow-md': item.hasPersistentHover,\n                },\n              )}\n            >\n              <div\n                className={cn(\n                  'absolute inset-0',\n                  item.hasPersistentHover\n                    ? 'opacity-100'\n                    : 'opacity-0 group-hover:opacity-100',\n                  'transition-opacity duration-300',\n                )}\n              >\n                <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(0,0,0,0.03)_1px,transparent_1px)] bg-[length:4px_4px] dark:bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.03)_1px,transparent_1px)]\" />\n              </div>\n\n              <CardHeader className=\"relative space-y-0 p-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"bg-primary/10 flex h-8 w-8 items-center justify-center rounded-lg\">\n                    {item.icon}\n                  </div>\n                  <span className=\"bg-secondary text-secondary-foreground rounded-md px-2 py-1 text-xs font-medium\">\n                    {item.status || 'Active'}\n                  </span>\n                </div>\n              </CardHeader>\n\n              <CardContent className=\"relative space-y-2 p-4 pt-0\">\n                <h3 className=\"text-foreground text-[15px] font-medium tracking-tight\">\n                  {item.title}\n                  {item.meta && (\n                    <span className=\"text-muted-foreground ml-2 text-xs font-normal\">\n                      {item.meta}\n                    </span>\n                  )}\n                </h3>\n                <p className=\"text-muted-foreground text-sm leading-relaxed\">\n                  {item.description}\n                </p>\n              </CardContent>\n\n              <CardFooter className=\"relative p-4\">\n                <div className=\"flex w-full items-center justify-between\">\n                  <div className=\"text-muted-foreground flex flex-wrap gap-2 text-xs\">\n                    {item.tags?.map((tag) => (\n                      <span\n                        key={`${item.title}-${tag}`}\n                        className=\"bg-secondary/50 rounded-md px-2 py-1 backdrop-blur-xs transition-all duration-200\"\n                      >\n                        #{tag}\n                      </span>\n                    ))}\n                  </div>\n                  <span className=\"text-primary text-xs font-medium opacity-0 transition-opacity group-hover:opacity-100\">\n                    {item.cta || 'Explore →'}\n                  </span>\n                </div>\n              </CardFooter>\n\n              <div\n                className={cn(\n                  'via-primary/10 absolute inset-0 -z-10 rounded-xl bg-gradient-to-br from-transparent to-transparent p-px',\n                  item.hasPersistentHover\n                    ? 'opacity-100'\n                    : 'opacity-0 group-hover:opacity-100',\n                  'transition-opacity duration-300',\n                )}\n              />\n            </Card>\n          </motion.a>\n        ))}\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/grids/bento-grid-2.tsx", "target": "components/mvpblocks/bento-grid-2.tsx"}]}