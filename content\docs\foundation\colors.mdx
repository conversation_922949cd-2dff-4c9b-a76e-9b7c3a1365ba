---
title: Colors
description: Color palette that is used in Mvpblocks consistently.
icon: Blend
---

import ColorPalette from '@/components/ColorPalette';

## Base Colors

<ColorPalette
  colors={[
    {
      name: 'Background',
      usage: 'bg-background',
      value: 'hsl(var(--background))',
    },
    {
      name: 'Foreground',
      usage: 'text-foreground',
      value: 'hsl(var(--foreground))',
    },
    { name: 'Card', usage: 'bg-card', value: 'hsl(var(--card))' },
    {
      name: 'Card Foreground',
      usage: 'text-card-foreground',
      value: 'hsl(var(--card-foreground))',
    },
  ]}
/>

## Interactive Colors

<ColorPalette
  colors={[
    { name: 'Primary', usage: 'bg-primary', value: 'hsl(var(--primary))' },
    {
      name: 'Primary Foreground',
      usage: 'text-primary-foreground',
      value: 'hsl(var(--primary-foreground))',
    },
    {
      name: 'Secondary',
      usage: 'bg-secondary',
      value: 'hsl(var(--secondary))',
    },
    {
      name: 'Secondary Foreground',
      usage: 'text-secondary-foreground',
      value: 'hsl(var(--secondary-foreground))',
    },
    { name: 'Accent', usage: 'bg-accent', value: 'hsl(var(--accent))' },
    {
      name: 'Accent Foreground',
      usage: 'text-accent-foreground',
      value: 'hsl(var(--accent-foreground))',
    },
  ]}
/>

## Utility Colors

<ColorPalette
  colors={[
    { name: 'Muted', usage: 'bg-muted', value: 'hsl(var(--muted))' },
    {
      name: 'Muted Foreground',
      usage: 'text-muted-foreground',
      value: 'hsl(var(--muted-foreground))',
    },
    {
      name: 'Destructive',
      usage: 'bg-destructive',
      value: 'hsl(var(--destructive))',
    },
    {
      name: 'Destructive Foreground',
      usage: 'text-destructive-foreground',
      value: 'hsl(var(--destructive-foreground))',
    },
  ]}
/>

## Form & Layout Colors

<ColorPalette
  colors={[
    { name: 'Border', usage: 'border-border', value: 'hsl(var(--border))' },
    { name: 'Input', usage: 'input-input', value: 'hsl(var(--input))' },
    { name: 'Ring', usage: 'ring-ring', value: 'hsl(var(--ring))' },
  ]}
/>

## Other Colors

<ColorPalette
  colors={[
    { name: 'Sidebar', usage: 'bg-sidebar', value: 'hsl(var(--sidebar))' },
    {
      name: 'Sidebar Foreground',
      usage: 'text-sidebar-foreground',
      value: 'hsl(var(--sidebar-foreground))',
    },
    {
      name: 'Sidebar Primary',
      usage: 'bg-sidebar-primary',
      value: 'hsl(var(--sidebar-primary))',
    },
    {
      name: 'Sidebar Primary Foreground',
      usage: 'text-sidebar-primary-foreground',
      value: 'hsl(var(--sidebar-primary-foreground))',
    },
    {
      name: 'Sidebar Accent',
      usage: 'bg-sidebar-accent',
      value: 'hsl(var(--sidebar-accent))',
    },
    {
      name: 'Sidebar Accent Foreground',
      usage: 'text-sidebar-accent-foreground',
      value: 'hsl(var(--sidebar-accent-foreground))',
    },
    {
      name: 'Sidebar Border',
      usage: 'border-sidebar',
      value: 'hsl(var(--sidebar-border))',
    },
    {
      name: 'Sidebar Ring',
      usage: 'ring-sidebar',
      value: 'hsl(var(--sidebar-ring))',
    },
  ]}
/>
