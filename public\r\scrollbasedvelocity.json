{"name": "scrollbasedvelocity", "type": "registry:ui", "dependencies": ["framer-motion", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:ui", "content": "'use client';\n\nimport React, { useEffect, useRef, useState } from 'react';\nimport {\n  motion,\n  useAnimationFrame,\n  useMotionValue,\n  useScroll,\n  useSpring,\n  useTransform,\n  useVelocity,\n} from 'framer-motion';\nimport { cn } from '@/lib/utils';\n\ninterface VelocityScrollProps {\n  text: string;\n  default_velocity?: number;\n  className?: string;\n}\n\ninterface ParallaxProps {\n  children: string;\n  baseVelocity: number;\n  className?: string;\n}\n\nexport const wrap = (min: number, max: number, v: number) => {\n  const rangeSize = max - min;\n  return ((((v - min) % rangeSize) + rangeSize) % rangeSize) + min;\n};\n\nexport const VelocityScroll: React.FC<VelocityScrollProps> = ({\n  text,\n  default_velocity = 5,\n  className,\n}) => {\n  const ParallaxText: React.FC<ParallaxProps> = ({\n    children,\n    baseVelocity = 100,\n    className,\n  }) => {\n    const baseX = useMotionValue(0);\n    const { scrollY } = useScroll();\n    const scrollVelocity = useVelocity(scrollY);\n    const smoothVelocity = useSpring(scrollVelocity, {\n      damping: 50,\n      stiffness: 400,\n    });\n\n    const velocityFactor = useTransform(smoothVelocity, [0, 1000], [0, 5], {\n      clamp: false,\n    });\n\n    const [repetitions, setRepetitions] = useState(1);\n    const containerRef = useRef<HTMLDivElement>(null);\n    const textRef = useRef<HTMLSpanElement>(null);\n\n    useEffect(() => {\n      const calculateRepetitions = () => {\n        if (containerRef.current && textRef.current) {\n          const containerWidth = containerRef.current.offsetWidth;\n          const textWidth = textRef.current.offsetWidth;\n          const newRepetitions = Math.ceil(containerWidth / textWidth) + 2;\n          setRepetitions(newRepetitions);\n        }\n      };\n\n      calculateRepetitions();\n\n      window.addEventListener('resize', calculateRepetitions);\n      return () => window.removeEventListener('resize', calculateRepetitions);\n    }, [children]);\n\n    const x = useTransform(baseX, (v) => `${wrap(-100 / repetitions, 0, v)}%`);\n\n    const directionFactor = useRef<number>(1);\n    useAnimationFrame((t, delta) => {\n      let moveBy = directionFactor.current * baseVelocity * (delta / 1000);\n\n      if (velocityFactor.get() < 0) {\n        directionFactor.current = -1;\n      } else if (velocityFactor.get() > 0) {\n        directionFactor.current = 1;\n      }\n\n      moveBy += directionFactor.current * moveBy * velocityFactor.get();\n\n      baseX.set(baseX.get() + moveBy);\n    });\n\n    return (\n      <div\n        className=\"w-full overflow-hidden whitespace-nowrap\"\n        ref={containerRef}\n      >\n        <motion.div className={cn('inline-block', className)} style={{ x }}>\n          {Array.from({ length: repetitions }).map((_, i) => (\n            <span key={i} ref={i === 0 ? textRef : null}>\n              {children}{' '}\n            </span>\n          ))}\n        </motion.div>\n      </div>\n    );\n  };\n\n  return (\n    <section className=\"relative w-full\">\n      <ParallaxText baseVelocity={default_velocity} className={className}>\n        {text}\n      </ParallaxText>\n      <ParallaxText baseVelocity={-default_velocity} className={className}>\n        {text}\n      </ParallaxText>\n    </section>\n  );\n};\n", "path": "/components/ui/scrollbasedvelocity.tsx", "target": "components/ui/scrollbasedvelocity.tsx"}]}