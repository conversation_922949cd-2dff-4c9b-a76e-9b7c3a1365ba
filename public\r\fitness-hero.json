{"name": "fitness-hero", "type": "registry:block", "dependencies": [], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json"], "files": [{"type": "registry:block", "content": "import { Button } from '@/components/ui/button';\n\nexport default function FitnessHero() {\n  return (\n    <div className=\"relative z-10\">\n      <div className=\"pt-28 pb-20 md:pt-16 lg:pt-20\">\n        <div className=\"container\">\n          <div className=\"grid items-center gap-10 lg:grid-cols-2 lg:gap-20 xl:gap-[122px]\">\n            <div className=\"mx-auto max-w-[500px] lg:mx-0 lg:max-w-none\">\n              <div className=\"flex flex-col items-center justify-center text-center lg:items-start lg:justify-normal lg:text-left\">\n                <h1 className=\"xxl:text-[90px] mb-6 text-4xl leading-[1.11] font-extrabold -tracking-[1px] md:text-5xl lg:text-6xl xl:text-7xl\">\n                  Fitness app for your good health\n                </h1>\n                <p className=\"mb-10 xl:mb-[50px]\">\n                  Snaga is a health &amp; fitness tracker app that helps you set\n                  out realistic goals that you can accomplish without many\n                  hurdles. Sometimes, we keep bigger goals but end up and\n                  workout sessions and exercises to help you keep fit.\n                </p>\n                <div className=\"mb-8 flex flex-wrap items-center gap-[10px] lg:mb-[50px]\">\n                  <div className=\"flex\">\n                    <img\n                      src=\"https://blocks.mvp-subha.me/assets/fitness-hero/1.webp\"\n                      alt=\"hero-avatar-img-1\"\n                      width=\"58\"\n                      height=\"58\"\n                      className=\"-ml-[15px] h-[58px] w-[58px] rounded-[50%] first:-ml-0\"\n                    />\n                    <img\n                      src=\"https://blocks.mvp-subha.me/assets/fitness-hero/2.webp\"\n                      alt=\"hero-avatar-img-2\"\n                      width=\"58\"\n                      height=\"58\"\n                      className=\"-ml-[15px] h-[58px] w-[58px] rounded-[50%] first:-ml-0\"\n                    />\n                    <img\n                      src=\"https://blocks.mvp-subha.me/assets/fitness-hero/3.webp\"\n                      alt=\"hero-avatar-img-2\"\n                      width=\"58\"\n                      height=\"58\"\n                      className=\"-ml-[15px] h-[58px] w-[58px] rounded-[50%] first:-ml-0\"\n                    />\n                  </div>\n                  <ul className=\"flex gap-12\">\n                    <li className=\"relative after:absolute after:top-1/2 after:left-[calc(100%+_24px)] after:h-10 after:w-0.5 after:-translate-y-1/2 after:bg-black/20 last:after:hidden dark:after:bg-white/20\">\n                      <div className=\"text-[30px] leading-[1.4] font-bold -tracking-[1px]\">\n                        64,739\n                      </div>\n                      <div className=\"flex gap-[5px] text-base\">\n                        Happy Customers\n                      </div>\n                    </li>\n                    <li className=\"after:bg-ColorBlack/10 relative after:absolute after:top-1/2 after:left-[calc(100%+_24px)] after:h-10 after:w-0.5 after:-translate-y-1/2 last:after:hidden\">\n                      <div className=\"text-[30px] leading-[1.4] font-bold -tracking-[1px]\">\n                        4.8/5\n                      </div>\n                      <div className=\"flex gap-[5px] text-base\">\n                        <div className=\"flex gap-1\">\n                          <img\n                            src=\"https://blocks.mvp-subha.me/assets/fitness-hero/icon.svg\"\n                            alt=\"icon-green-star\"\n                            width=\"17\"\n                            height=\"17\"\n                          />\n                          <img\n                            src=\"https://blocks.mvp-subha.me/assets/fitness-hero/icon.svg\"\n                            alt=\"icon-green-star\"\n                            width=\"17\"\n                            height=\"17\"\n                          />\n                          <img\n                            src=\"https://blocks.mvp-subha.me/assets/fitness-hero/icon.svg\"\n                            alt=\"icon-green-star\"\n                            width=\"17\"\n                            height=\"17\"\n                          />\n                          <img\n                            src=\"https://blocks.mvp-subha.me/assets/fitness-hero/icon.svg\"\n                            alt=\"icon-green-star\"\n                            width=\"17\"\n                            height=\"17\"\n                          />\n                          <img\n                            src=\"https://blocks.mvp-subha.me/assets/fitness-hero/icon.svg\"\n                            alt=\"icon-green-star\"\n                            width=\"17\"\n                            height=\"17\"\n                          />\n                        </div>\n                        Rating\n                      </div>\n                    </li>\n                  </ul>\n                </div>\n\n                <div className=\"flex flex-wrap justify-center gap-5 sm:justify-normal\">\n                  <a href=\"#\" className=\"group relative z-10 inline-block\">\n                    <Button\n                      className=\"shadow-foreground/30 rounded-full shadow-md\"\n                      size=\"lg\"\n                    >\n                      Start a 10-day free trial\n                    </Button>\n                  </a>\n                </div>\n              </div>\n            </div>\n            <div>\n              <div className=\"relative z-10 mx-auto h-auto max-w-[280px] sm:max-w-[500px] lg:mr-0 lg:ml-auto\">\n                <img\n                  src=\"https://blocks.mvp-subha.me/assets/fitness-hero/image.webp\"\n                  alt=\"hero-mobille\"\n                  width=\"612\"\n                  height=\"1272\"\n                  className=\"z-10 mx-auto h-[635px] w-[306px]\"\n                />\n                <img\n                  src=\"https://blocks.mvp-subha.me/assets/fitness-hero/shape1.svg\"\n                  alt=\"hero-2-shape-1\"\n                  width=\"110\"\n                  height=\"191\"\n                  className=\"xxl:left-[6%] absolute bottom-[18%] -left-[12%] -z-10 xl:-left-[2%]\"\n                />\n                <img\n                  src=\"https://blocks.mvp-subha.me/assets/fitness-hero/shape2.svg\"\n                  alt=\"hero-2-shape-1\"\n                  width=\"292\"\n                  height=\"299\"\n                  className=\"absolute top-[15%] -right-[18%] -z-10\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/hero/fitness-hero.tsx", "target": "components/mvpblocks/fitness-hero.tsx"}]}