{"name": "pricing-4", "author": "mosespace", "type": "registry:block", "dependencies": ["lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/button.json"], "files": [{"type": "registry:block", "content": "import { Button } from '@/components/ui/button';\nimport { Check } from 'lucide-react';\nimport Link from 'next/link';\n\nconst plan = {\n  name: 'Suite Enterprise',\n  price: 300,\n  description: 'For your company of any size',\n  features: [\n    'First premium advantage',\n    'Second advantage weekly',\n    'Third advantage donate to project',\n    'Fourth, access to all components weekly',\n  ],\n  includes:\n    'Security, Unlimited Storage, Payment, Search engine, and all features',\n  companies: [\n    {\n      name: 'Nvidia',\n      logo: 'https://html.tailus.io/blocks/customers/nvidia.svg',\n      height: 20,\n    },\n    {\n      name: 'Column',\n      logo: 'https://html.tailus.io/blocks/customers/column.svg',\n      height: 16,\n    },\n    {\n      name: 'GitHub',\n      logo: 'https://html.tailus.io/blocks/customers/github.svg',\n      height: 16,\n    },\n    {\n      name: 'Nike',\n      logo: 'https://html.tailus.io/blocks/customers/nike.svg',\n      height: 20,\n    },\n  ],\n};\n\nexport default function PricingFour() {\n  return (\n    <div className=\"relative w-full bg-gradient-to-br from-zinc-50 via-white to-zinc-100 py-16 md:py-32 dark:from-zinc-900 dark:via-zinc-950 dark:to-black\">\n      <div className=\"absolute inset-0 -z-10 overflow-hidden\">\n        <div className=\"bg-primary/10 absolute -top-[10%] left-[50%] h-[40%] w-[60%] -translate-x-1/2 rounded-full blur-3xl\" />\n        <div className=\"bg-primary/5 absolute -right-[10%] -bottom-[10%] h-[40%] w-[40%] rounded-full blur-3xl\" />\n        <div className=\"bg-primary/5 absolute -bottom-[10%] -left-[10%] h-[40%] w-[40%] rounded-full blur-3xl\" />\n      </div>\n\n      <div className=\"mx-auto max-w-5xl px-6\">\n        <div className=\"mx-auto max-w-2xl text-center\">\n          <h2 className=\"text-3xl font-extrabold text-balance md:text-4xl lg:text-5xl\">\n            Transform the way you manage your company\n          </h2>\n          <p className=\"text-muted-foreground mt-4 text-lg\">\n            Empower your team with the tools they need to succeed. Flexible,\n            scalable, and built for the modern enterprise.\n          </p>\n        </div>\n        <div className=\"mt-10 md:mt-20\">\n          <div className=\"bg-card relative rounded-3xl border border-zinc-200/60 shadow-xl shadow-zinc-950/5 backdrop-blur-sm dark:border-zinc-700/50 dark:bg-zinc-900/70\">\n            <div className=\"grid items-center gap-12 divide-y divide-zinc-200 p-12 md:grid-cols-2 md:gap-x-2 md:divide-x-0 md:divide-y-0 dark:divide-zinc-700\">\n              {/* Left Side */}\n              <div className=\"pb-12 text-center md:pr-12 md:pb-0\">\n                <h3 className=\"text-2xl font-semibold\">{plan.name}</h3>\n                <p className=\"mt-2 text-lg\">{plan.description}</p>\n                <span className=\"text-primary mt-12 mb-6 inline-block text-6xl font-extrabold\">\n                  <span className=\"align-super text-4xl\">$</span>\n                  {plan.price}\n                </span>\n                <div className=\"flex justify-center\">\n                  <Button asChild size=\"lg\" className=\"shadow-md\">\n                    <Link href=\"#\">Get started</Link>\n                  </Button>\n                </div>\n                <p className=\"text-muted-foreground mt-12 text-sm\">\n                  Includes: {plan.includes}\n                </p>\n              </div>\n\n              {/* Right Side */}\n              <div className=\"relative m-3\">\n                <div className=\"text-left\">\n                  <h4 className=\"mb-4 text-lg font-medium\">What’s included:</h4>\n                  <ul role=\"list\" className=\"space-y-4\">\n                    {plan.features.map((feature, index) => (\n                      <li\n                        key={index}\n                        className=\"flex items-start gap-3 text-sm\"\n                      >\n                        <Check className=\"text-primary mt-1 size-4\" />\n                        <span>{feature}</span>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n                <p className=\"text-muted-foreground mt-6 text-sm\">\n                  Team size is flexible; add or switch members as needed.\n                  Companies using our platform include:\n                </p>\n                <div className=\"mt-8 flex flex-wrap items-center justify-start gap-6\">\n                  {plan.companies.map((company, i) => (\n                    <img\n                      key={i}\n                      className=\"h-5 w-auto dark:invert\"\n                      src={company.logo}\n                      alt={`${company.name} Logo`}\n                      height={company.height}\n                      width=\"auto\"\n                    />\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/pricing/pricing-4.tsx", "target": "components/mvpblocks/pricing-4.tsx"}]}