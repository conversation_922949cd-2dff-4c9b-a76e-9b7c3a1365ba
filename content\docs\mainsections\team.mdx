---
title: Team
description: Team sections provide information about the people behind your product or service. They can include details about team members, their roles, and their expertise.
root: mainsections
updated: true
---

import { ComponentPreview } from '@/components/preview/component-preview';
import { extractSourceCode } from '@/lib/code';

## Detailed Team

<ComponentPreview
  name="team-1"
  classNameComponentContainer="min-h-[600px]"
  code={(await extractSourceCode('team-1')).code}
  lang="tsx"
/>

## Filterable Team

<ComponentPreview
  name="team-4"
  classNameComponentContainer="min-h-[600px]"
  code={(await extractSourceCode('team-3')).code}
  lang="tsx"
  fromDocs={true}
/>

## Custom social team

<ComponentPreview
  name="team-2"
  classNameComponentContainer="min-h-[600px]"
  code={(await extractSourceCode('team-2')).code}
  lang="tsx"
  fromDocs={true}
/>

## Designer Team Section

<ComponentPreview
  name="team-8"
  classNameComponentContainer="min-h-[600px]"
  code={(await extractSourceCode('team-8')).code}
  lang="tsx"
  fromDocs={true}
/>

## Member Role Teams

<ComponentPreview
  name="team-3"
  classNameComponentContainer="min-h-[600px]"
  code={(await extractSourceCode('team-3')).code}
  lang="tsx"
  fromDocs={true}
/>

## Basic Static Team

<ComponentPreview
  name="team-5"
  classNameComponentContainer="min-h-[600px]"
  code={(await extractSourceCode('team-5')).code}
  lang="tsx"
  fromDocs={true}
/>

## Modern Member Team

<ComponentPreview
  name="team-7"
  classNameComponentContainer="min-h-[600px]"
  code={(await extractSourceCode('team-7')).code}
  lang="tsx"
  fromDocs={true}
/>

## Team Carousel With Roles

<ComponentPreview
  name="team-10"
  classNameComponentContainer="min-h-[600px]"
  code={(await extractSourceCode('team-10')).code}
  lang="tsx"
  fromDocs={true}
/>

## Professional Team Section

<ComponentPreview
  name="team-6"
  classNameComponentContainer="min-h-[600px]"
  code={(await extractSourceCode('team-6')).code}
  lang="tsx"
  fromDocs={true}
/>

## Unique Team Section With Roles

<ComponentPreview
  name="team-9"
  classNameComponentContainer="min-h-[600px]"
  code={(await extractSourceCode('team-9')).code}
  lang="tsx"
  fromDocs={true}
/>
