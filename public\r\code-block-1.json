{"name": "code-block-1", "type": "registry:block", "dependencies": ["react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "export default function CodeBlock() {\n  return (\n    <>\n      <div className=\"relative w-full max-w-2xl rounded-xl p-0.5\">\n        <div className=\"code-border-anim\" />\n        <div className=\"rounded-xl bg-[radial-gradient(at_88%_40%,#181925_0,transparent_85%),radial-gradient(at_49%_30%,#181925_0,transparent_85%),radial-gradient(at_14%_26%,#181925_0,transparent_85%),radial-gradient(at_0%_64%,#015c6e_0,transparent_85%),radial-gradient(at_41%_94%,#00b7e9_0,transparent_85%),radial-gradient(at_100%_99%,#103a42_0,transparent_85%)] p-6 shadow-[0px_-16px_24px_0px_rgba(255,255,255,0.25)_inset]\">\n          <div className=\"flex items-center justify-between pb-4\">\n            <span className=\"text-base font-semibold text-white\">app.tsx</span>\n            <button className=\"rounded-full bg-[#1fcdfc] px-3 py-1.5 text-xs font-medium text-[#181925] transition hover:bg-[#00b7e9]\">\n              Copy\n            </button>\n          </div>\n          <pre className=\"m-0 overflow-x-auto rounded-lg bg-transparent p-0 text-sm leading-relaxed whitespace-pre text-blue-100\">\n            <code>\n              <span className=\"text-[#1fcdfc]\">import</span>{' '}\n              <span className=\"text-[#e0e0e0]\">{'{'}</span>useState\n              <span className=\"text-[#e0e0e0]\">{'}'}</span>{' '}\n              <span className=\"text-[#1fcdfc]\">from</span>{' '}\n              <span className=\"text-[#f7b731]\">&apos;react&apos;</span>;<br />\n              <br />\n              <span className=\"text-[#1fcdfc]\">function</span>{' '}\n              <span className=\"text-[#ffd60a]\">Counter</span>() {'{'}\n              <br />\n              &nbsp;&nbsp;<span className=\"text-[#1fcdfc]\">const</span> [count,\n              setCount] = useState(<span className=\"text-[#f7b731]\">0</span>);\n              <br />\n              <br />\n              &nbsp;&nbsp;<span className=\"text-[#1fcdfc]\">return</span> (<br />\n              &nbsp;&nbsp;&nbsp;&nbsp;\n              <span className=\"text-[#ffd60a]\">&lt;button</span>{' '}\n              <span className=\"text-[#36ffb1]\">onClick</span>=\n              <span className=\"text-[#f7b731]\">{'{'}</span>() =&gt;\n              setCount(count + <span className=\"text-[#f7b731]\">1</span>)\n              <span className=\"text-[#f7b731]\">{'}'}</span>\n              <span className=\"text-[#ffd60a]\">&gt;</span>\n              <br />\n              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Clicked {'{'}count{'}'} times\n              <br />\n              &nbsp;&nbsp;&nbsp;&nbsp;\n              <span className=\"text-[#ffd60a]\">&lt;/button&gt;</span>\n              <br />\n              &nbsp;&nbsp;);\n              <br />\n              {'}'}\n            </code>\n          </pre>\n        </div>\n      </div>\n\n      <style>{`\n        .code-border-anim {\n          position: absolute;\n          z-index: -10;\n          top: 50%;\n          left: 50%;\n          transform: translate(-50%, -50%);\n          width: calc(100% + 2px);\n          height: calc(100% + 2px);\n          border-radius: 1rem;\n          overflow: hidden;\n          pointer-events: none;\n        }\n        .code-border-anim::before {\n          content: \"\";\n          position: absolute;\n          top: 50%;\n          left: 50%;\n          width: 200%;\n          height: 10rem;\n          background: linear-gradient(\n            0deg,\n            hsla(0,0%,100%,0) 0%,\n            hsl(189,100%,50%) 40%,\n            hsl(189,100%,50%) 60%,\n            hsla(0,0%,40%,0) 100%\n          );\n          transform: translate(-50%, -50%) rotate(0deg);\n          transform-origin: left;\n          animation: border-rotate 8s linear infinite;\n          z-index: 2;\n          pointer-events: none;\n        }\n        @keyframes border-rotate {\n          to {\n            transform: translate(-50%, -50%) rotate(360deg);\n          }\n        }\n      `}</style>\n    </>\n  );\n}\n", "path": "/components/mvpblocks/cards/code/code-block-1.tsx", "target": "components/mvpblocks/code-block-1.tsx"}]}