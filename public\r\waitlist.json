{"name": "waitlist", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "next-themes", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/particles.json", "https://blocks.mvp-subha.me/r/spotlight.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport type React from 'react';\nimport { useState, useEffect } from 'react';\nimport { ArrowR<PERSON>, Sparkles, Code, Star, ExternalLink } from 'lucide-react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Particles } from '@/components/ui/particles';\nimport { Spotlight } from '@/components/ui/spotlight';\nimport { useTheme } from 'next-themes';\nimport { Bricolage_Grotesque } from 'next/font/google';\nimport { cn } from '@/lib/utils';\n\nconst brico = Bricolage_Grotesque({\n  subsets: ['latin'],\n});\n\n// Sample users for the waitlist display\nconst users = [\n  { imgUrl: 'https://avatars.githubusercontent.com/u/111780029' },\n  { imgUrl: 'https://avatars.githubusercontent.com/u/123104247' },\n  { imgUrl: 'https://avatars.githubusercontent.com/u/115650165' },\n  { imgUrl: 'https://avatars.githubusercontent.com/u/71373838' },\n];\n\nexport default function WaitlistPage() {\n  const [email, setEmail] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitted, setSubmitted] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const { resolvedTheme } = useTheme();\n  const [color, setColor] = useState('#ffffff');\n\n  useEffect(() => {\n    setColor(resolvedTheme === 'dark' ? '#ffffff' : '#e60a64');\n  }, [resolvedTheme]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    setError(null);\n\n    // Your form submission logic here\n    // For now, let's just simulate a delay\n    await new Promise((resolve) => setTimeout(resolve, 1000));\n    setSubmitted(true);\n    setIsSubmitting(false);\n  };\n\n  return (\n    <main className=\"relative flex min-h-screen w-full items-center justify-center overflow-hidden xl:h-screen\">\n      <Spotlight />\n\n      <Particles\n        className=\"absolute inset-0 z-0\"\n        quantity={100}\n        ease={80}\n        refresh\n        color={color}\n      />\n\n      <div className=\"relative z-[100] mx-auto max-w-2xl px-4 py-16 text-center\">\n        {/* Badge */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          className=\"border-primary/10 from-primary/15 to-primary/5 mb-8 inline-flex items-center gap-2 rounded-full border bg-gradient-to-r px-4 py-2 backdrop-blur-sm\"\n        >\n          <img\n            src=\"https://i.postimg.cc/vHnf0qZF/logo.webp\"\n            alt=\"logo\"\n            className=\"spin h-6 w-6\"\n          />\n          <span className=\"text-sm font-medium\">Mvpblocks</span>\n          <motion.div\n            animate={{ x: [0, 5, 0] }}\n            transition={{ duration: 1.5, repeat: Number.POSITIVE_INFINITY }}\n          >\n            <ArrowRight className=\"h-4 w-4\" />\n          </motion.div>\n        </motion.div>\n\n        <motion.h1\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, delay: 0.2 }}\n          className={cn(\n            'from-foreground via-foreground/80 to-foreground/40 mb-4 cursor-crosshair bg-gradient-to-b bg-clip-text text-4xl font-bold text-transparent sm:text-7xl',\n            brico.className,\n          )}\n        >\n          Join the{' '}\n          <span className=\"bg-primary from-foreground to-primary via-rose-300 bg-clip-text text-transparent dark:bg-gradient-to-b\">\n            Waitlist\n          </span>\n        </motion.h1>\n\n        {/* Subtitle */}\n        <motion.p\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, delay: 0.5 }}\n          className=\"text-muted-foreground mt-2 mb-12 sm:text-lg\"\n        >\n          Be the first to access our revolutionary component library.\n          <br className=\"hidden sm:block\" /> Build your MVP faster than ever\n          before.\n        </motion.p>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.7 }}\n          className=\"mb-12 grid grid-cols-2 gap-6 sm:grid-cols-3\"\n        >\n          <div\n            className={cn(\n              'border-primary/10 flex flex-col items-center justify-center rounded-xl border bg-white/5 p-4 backdrop-blur-md',\n              resolvedTheme === 'dark' ? 'glass' : 'glass2',\n            )}\n          >\n            <Code className=\"text-primary mb-2 h-5 w-5\" />\n            <span className=\"text-xl font-bold\">100+</span>\n            <span className=\"text-muted-foreground text-xs\">Components</span>\n          </div>\n\n          <div\n            className={cn(\n              'border-primary/10 flex flex-col items-center justify-center rounded-xl border bg-white/5 p-4 backdrop-blur-md',\n              resolvedTheme === 'dark' ? 'glass' : 'glass2',\n            )}\n          >\n            <ExternalLink className=\"text-primary mb-2 h-5 w-5\" />\n            <span className=\"text-xl font-bold\">Open Source</span>\n            <span className=\"text-muted-foreground text-xs\">BSD 3-Clause</span>\n          </div>\n\n          <div\n            className={cn(\n              'border-primary/10 flex flex-col items-center justify-center rounded-xl border bg-white/5 p-4 backdrop-blur-md',\n              resolvedTheme === 'dark' ? 'glass' : 'glass2',\n            )}\n          >\n            <Star className=\"text-primary mb-2 h-5 w-5\" />\n            <span className=\"text-xl font-bold\">Premium</span>\n            <span className=\"text-muted-foreground text-xs\">Quality</span>\n          </div>\n        </motion.div>\n\n        <motion.form\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.4 }}\n          onSubmit={handleSubmit}\n          className=\"mx-auto flex flex-col gap-4 sm:flex-row\"\n        >\n          <AnimatePresence mode=\"wait\">\n            {!submitted ? (\n              <>\n                <div className=\"relative flex-1\">\n                  <motion.input\n                    key=\"email-input\"\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: -10 }}\n                    transition={{ duration: 0.3 }}\n                    type=\"email\"\n                    name=\"email\"\n                    id=\"email\"\n                    placeholder=\"Enter your email\"\n                    value={email}\n                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>\n                      setEmail(e.target.value)\n                    }\n                    required\n                    className=\"border-primary/20 text-foreground placeholder:text-muted-foreground/70 focus:border-primary/50 focus:ring-primary/30 w-full rounded-xl border bg-white/5 px-6 py-4 backdrop-blur-md transition-all focus:ring-2 focus:outline-none\"\n                  />\n                  {error && (\n                    <motion.p\n                      initial={{ opacity: 0 }}\n                      animate={{ opacity: 1 }}\n                      className=\"border-destructive/40 bg-destructive/10 text-destructive mt-2 rounded-xl border px-4 py-1 text-sm sm:absolute\"\n                    >\n                      {error}\n                    </motion.p>\n                  )}\n                </div>\n                <button\n                  type=\"submit\"\n                  disabled={isSubmitting || submitted}\n                  className=\"group text-primary-foreground focus:ring-primary/50 relative overflow-hidden rounded-xl bg-gradient-to-b from-rose-500 to-rose-700 px-8 py-4 font-semibold text-white shadow-[0px_2px_0px_0px_rgba(255,255,255,0.3)_inset] transition-all duration-300 hover:shadow-[0_0_20px_rgba(236,72,153,0.4)] focus:ring-2 focus:outline-none active:scale-95 disabled:cursor-not-allowed disabled:opacity-50\"\n                >\n                  <span className=\"relative z-10 flex items-center justify-center gap-2\">\n                    {isSubmitting ? 'Joining...' : 'Join Waitlist'}\n                    <Sparkles className=\"h-4 w-4 transition-all duration-300 group-hover:rotate-12\" />\n                  </span>\n                  <span className=\"to-primary absolute inset-0 z-0 bg-gradient-to-r from-rose-500 opacity-0 transition-opacity duration-300 group-hover:opacity-100\"></span>\n                </button>\n              </>\n            ) : (\n              <motion.div\n                key=\"thank-you-message\"\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -10 }}\n                transition={{ duration: 0.6 }}\n                className={cn(\n                  'border-primary/20 from-primary/10 to-primary/10 text-primary flex-1 cursor-pointer rounded-xl border bg-gradient-to-r via-transparent px-6 py-4 font-medium backdrop-blur-md transition-all duration-300 hover:shadow-[0_0_20px_rgba(236,72,153,0.3)] active:brightness-125',\n                  resolvedTheme === 'dark' ? 'glass' : 'glass2',\n                )}\n              >\n                <span className=\"flex items-center justify-center gap-2\">\n                  Thanks for joining!{' '}\n                  <Sparkles className=\"h-4 w-4 animate-pulse\" />\n                </span>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </motion.form>\n\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 1 }}\n          className=\"mt-10 flex items-center justify-center gap-1\"\n        >\n          <div className=\"flex -space-x-3\">\n            {users.map((user, i) => (\n              <motion.div\n                key={i}\n                initial={{ scale: 0, x: -10 }}\n                animate={{ scale: 1, x: 0 }}\n                transition={{ duration: 0.4, delay: 1 + i * 0.2 }}\n                className=\"border-background from-primary size-10 rounded-full border-2 bg-gradient-to-r to-rose-500 p-[2px]\"\n              >\n                <div className=\"overflow-hidden rounded-full\">\n                  <img\n                    src={user.imgUrl}\n                    alt=\"Avatar\"\n                    className=\"rounded-full transition-all duration-300 hover:scale-110 hover:rotate-6\"\n                    width={40}\n                    height={40}\n                  />\n                </div>\n              </motion.div>\n            ))}\n          </div>\n          <motion.span\n            initial={{ opacity: 0, x: -10 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.5, delay: 1.3 }}\n            className=\"text-muted-foreground ml-2\"\n          >\n            <span className=\"text-primary font-semibold\">100+</span> already\n            joined ✨\n          </motion.span>\n        </motion.div>\n      </div>\n\n      <style jsx global>{`\n        @keyframes float {\n          0%,\n          100% {\n            transform: translateY(0) translateX(0);\n            opacity: 0.3;\n          }\n          25% {\n            transform: translateY(-20px) translateX(10px);\n            opacity: 0.8;\n          }\n          50% {\n            transform: translateY(-40px) translateX(-10px);\n            opacity: 0.4;\n          }\n          75% {\n            transform: translateY(-20px) translateX(10px);\n            opacity: 0.6;\n          }\n        }\n      `}</style>\n    </main>\n  );\n}\n", "path": "/components/mvpblocks/pages/waitlist.tsx", "target": "components/mvpblocks/waitlist.tsx"}]}