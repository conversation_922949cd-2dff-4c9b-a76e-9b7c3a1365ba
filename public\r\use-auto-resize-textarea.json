{"name": "use-auto-resize-textarea", "type": "registry:hook", "files": [{"type": "registry:hook", "content": "'use client';\n\nimport { useEffect, useRef, useCallback } from 'react';\n\ninterface UseAutoResizeTextareaProps {\n  minHeight: number;\n  maxHeight?: number;\n}\n\nexport function useAutoResizeTextarea({\n  minHeight,\n  maxHeight,\n}: UseAutoResizeTextareaProps) {\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\n\n  const adjustHeight = useCallback(\n    (reset?: boolean) => {\n      const textarea = textareaRef.current;\n      if (!textarea) return;\n\n      if (reset) {\n        textarea.style.height = `${minHeight}px`;\n        return;\n      }\n\n      // Temporarily shrink to get the right scrollHeight\n      textarea.style.height = `${minHeight}px`;\n\n      // Calculate new height\n      const newHeight = Math.max(\n        minHeight,\n        Math.min(textarea.scrollHeight, maxHeight ?? Number.POSITIVE_INFINITY),\n      );\n\n      textarea.style.height = `${newHeight}px`;\n    },\n    [minHeight, maxHeight],\n  );\n\n  useEffect(() => {\n    // Set initial height\n    const textarea = textareaRef.current;\n    if (textarea) {\n      textarea.style.height = `${minHeight}px`;\n    }\n  }, [minHeight]);\n\n  // Adjust height on window resize\n  useEffect(() => {\n    const handleResize = () => adjustHeight();\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, [adjustHeight]);\n\n  return { textareaRef, adjustHeight };\n}\n", "path": "/hooks/use-auto-resize-textarea.ts", "target": "hooks/use-auto-resize-textarea.ts"}]}