{"name": "team-3", "author": "mosespace", "type": "registry:block", "dependencies": ["lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "import Link from 'next/link';\nimport { cn } from '@/lib/utils';\nimport { Facebook, Linkedin, Twitter } from 'lucide-react';\n\ntype TeamMember = {\n  id: number;\n  name: string;\n  role: string;\n  image: string;\n  troubleMaker?: boolean;\n  socialMedia?: {\n    facebook?: string;\n    twitter?: string;\n    linkedin?: string;\n  };\n};\n\nconst defaultTeamMembers: TeamMember[] = [\n  {\n    id: 1,\n    name: '<PERSON><PERSON>',\n    role: 'CEO & Founder',\n    troubleMaker: false,\n    image:\n      'https://img.freepik.com/free-psd/3d-rendering-avatar_23-2150833554.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n    socialMedia: {\n      facebook: '#',\n      twitter: '#',\n      linkedin: '#',\n    },\n  },\n  {\n    id: 2,\n    name: '<PERSON>',\n    role: 'CTO',\n    troubleMaker: true,\n\n    image:\n      'https://img.freepik.com/premium-photo/png-headset-headphones-portrait-cartoon_53876-762197.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n    socialMedia: {\n      facebook: '#',\n      twitter: '#',\n      linkedin: '#',\n    },\n  },\n  {\n    id: 3,\n    name: 'David Locklear',\n    role: 'Lead Designer',\n    troubleMaker: false,\n\n    image:\n      'https://img.freepik.com/premium-photo/png-cartoon-portrait-glasses-white-background_53876-905385.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n    socialMedia: {\n      facebook: '#',\n      twitter: '#',\n      linkedin: '#',\n    },\n  },\n  {\n    id: 4,\n    name: 'Michael Brown',\n    role: 'Marketing Director',\n    troubleMaker: false,\n\n    image:\n      'https://img.freepik.com/premium-psd/3d-avatar-character_975163-690.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n    socialMedia: {\n      facebook: '#',\n      twitter: '#',\n      linkedin: '#',\n    },\n  },\n];\n\nexport default function Team3({\n  teamMembers = defaultTeamMembers,\n  backgroundColor = 'bg-indigo-950',\n  title = 'top people at each industry',\n  headline = 'Partnered with most of the',\n}: {\n  title?: string;\n  headline?: string;\n  backgroundColor?: string;\n  teamMembers: TeamMember[];\n}) {\n  return (\n    <section className={cn(`${backgroundColor} w-full py-16 text-white`)}>\n      <div className=\"container mx-auto px-4\">\n        <div className=\"mb-12 text-center\">\n          <p className=\"mb-2 text-indigo-400\">{headline}</p>\n          <h2 className=\"mb-6 text-3xl font-light\">\n            <span className=\"italic\">{title}</span>\n          </h2>\n        </div>\n\n        <div className=\"grid grid-cols-1 gap-6 md:grid-cols-4\">\n          {teamMembers.map((member, index) => (\n            <div key={member.id} className=\"group relative\">\n              <div className=\"relative h-80 w-full overflow-hidden rounded-lg\">\n                <div className=\"absolute inset-0 z-10 opacity-20 transition-opacity group-hover:opacity-0\"></div>\n                <img\n                  src={member.image || '/placeholder.svg'}\n                  alt={member.name}\n                  className=\"object-cover\"\n                />\n              </div>\n              <div className=\"absolute right-0 bottom-0 left-0 bg-gradient-to-t from-indigo-900 to-transparent p-6\">\n                <h3 className=\"text-xl font-bold\">{member.name}</h3>\n                <p className=\"mb-3 text-indigo-300\">{member.role}</p>\n                <div className=\"flex space-x-3\">\n                  {member.socialMedia?.facebook && (\n                    <Link\n                      href={member.socialMedia.facebook}\n                      className=\"text-white hover:text-indigo-300\"\n                    >\n                      <Facebook size={18} />\n                    </Link>\n                  )}\n                  {member.socialMedia?.twitter && (\n                    <Link\n                      href={member.socialMedia.twitter}\n                      className=\"text-white hover:text-indigo-300\"\n                    >\n                      <Twitter size={18} />\n                    </Link>\n                  )}\n                  {member.socialMedia?.linkedin && (\n                    <Link\n                      href={member.socialMedia.linkedin}\n                      className=\"text-white hover:text-indigo-300\"\n                    >\n                      <Linkedin size={18} />\n                    </Link>\n                  )}\n                </div>\n              </div>\n              {member.troubleMaker && (\n                <div className=\"bg-opacity-80 absolute top-0 right-0 left-0 bg-indigo-600 p-6\">\n                  <p className=\"text-sm font-medium\">Trouble Maker</p>\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/team/team-3.tsx", "target": "components/mvpblocks/team-3.tsx"}]}