---
title: Skeletons
description: Skeletons are placeholder elements that can be used to indicate loading states in your application. They provide a visual cue to users that content is being loaded.
new: true
---

import { ComponentPreview } from '@/components/preview/component-preview';
import { extractSourceCode } from '@/lib/code';

## Shimmer Card

<ComponentPreview
  name="skeleton-card-one"
  classNameComponentContainer="min-h-[500px]"
  code={(await extractSourceCode('skeleton-card-one')).code}
  lang="tsx"
/>

## Shimmer Table Type One

<ComponentPreview
  name="skeleton-table-one"
  classNameComponentContainer="min-h-[500px]"
  code={(await extractSourceCode("skeleton-table-one")).code}
  lang="tsx"
/>

## Shimmer Table Type Two

<ComponentPreview
  name="skeleton-table-two"
  classNameComponentContainer="min-h-[500px]"
  code={(await extractSourceCode("skeleton-table-two")).code}
  lang="tsx"
/>