{"name": "congusted-pricing", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react", "@number-flow/react", "canvas-confetti"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/label.json", "https://blocks.mvp-subha.me/r/switch.json", "https://blocks.mvp-subha.me/r/use-media-query.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { buttonVariants } from '@/components/ui/button';\nimport { Label } from '@/components/ui/label';\nimport { Switch } from '@/components/ui/switch';\nimport { useMediaQuery } from '@/hooks/use-media-query';\nimport { cn } from '@/lib/utils';\nimport { motion } from 'framer-motion';\nimport { Check, Star } from 'lucide-react';\nimport Link from 'next/link';\nimport { useState, useRef } from 'react';\nimport confetti from 'canvas-confetti';\nimport NumberFlow from '@number-flow/react';\n\n// Define your plans\nconst plans = [\n  {\n    name: 'STARTER',\n    price: '50',\n    yearlyPrice: '40',\n    period: 'per month',\n    features: [\n      'Up to 10 projects',\n      'Basic analytics',\n      '48-hour support response time',\n      'Limited API access',\n      'Community support',\n    ],\n    description: 'Perfect for individuals and small projects',\n    buttonText: 'Start Free Trial',\n    href: '/sign-up',\n    isPopular: false,\n  },\n  {\n    name: 'PROFESSIONAL',\n    price: '99',\n    yearlyPrice: '79',\n    period: 'per month',\n    features: [\n      'Unlimited projects',\n      'Advanced analytics',\n      '24-hour support response time',\n      'Full API access',\n      'Priority support',\n      'Team collaboration',\n      'Custom integrations',\n    ],\n    description: 'Ideal for growing teams and businesses',\n    buttonText: 'Get Started',\n    href: '/sign-up',\n    isPopular: true,\n  },\n  {\n    name: 'ENTERPRISE',\n    price: '299',\n    yearlyPrice: '239',\n    period: 'per month',\n    features: [\n      'Everything in Professional',\n      'Custom solutions',\n      'Dedicated account manager',\n      '1-hour support response time',\n      'SSO Authentication',\n      'Advanced security',\n      'Custom contracts',\n      'SLA agreement',\n    ],\n    description: 'For large organizations with specific needs',\n    buttonText: 'Contact Sales',\n    href: '/contact',\n    isPopular: false,\n  },\n];\n\ninterface PricingPlan {\n  name: string;\n  price: string;\n  yearlyPrice: string;\n  period: string;\n  features: string[];\n  description: string;\n  buttonText: string;\n  href: string;\n  isPopular: boolean;\n}\n\ninterface PricingProps {\n  plans: PricingPlan[];\n  title?: string;\n  description?: string;\n}\n\nexport default function CongestedPricing() {\n  const [isMonthly, setIsMonthly] = useState(true);\n  const isDesktop = useMediaQuery('(min-width: 768px)');\n  const switchRef = useRef<HTMLButtonElement>(null);\n\n  const handleToggle = (checked: boolean) => {\n    setIsMonthly(!checked);\n    if (checked && switchRef.current) {\n      const rect = switchRef.current.getBoundingClientRect();\n      const x = rect.left + rect.width / 2;\n      const y = rect.top + rect.height / 2;\n\n      confetti({\n        particleCount: 50,\n        spread: 60,\n        origin: {\n          x: x / window.innerWidth,\n          y: y / window.innerHeight,\n        },\n        colors: [\n          'hsl(var(--primary))',\n          'hsl(var(--accent))',\n          'hsl(var(--secondary))',\n          'hsl(var(--muted))',\n        ],\n        ticks: 200,\n        gravity: 1.2,\n        decay: 0.94,\n        startVelocity: 30,\n        shapes: ['circle'],\n      });\n    }\n  };\n\n  return (\n    <div className=\"container py-20\">\n      <div className=\"mb-12 space-y-4 text-center\">\n        <h2 className=\"text-4xl font-bold tracking-tight sm:text-5xl\">\n          Simple, transparent pricing for all.\n        </h2>\n        <p className=\"text-muted-foreground text-lg whitespace-pre-line\">\n          Choose the plan that works for you\\nAll plans include access to our\n          platform, lead generation tools, and dedicated support.\n        </p>\n      </div>\n\n      <div className=\"mb-10 flex justify-center\">\n        <label className=\"relative inline-flex cursor-pointer items-center\">\n          <Label>\n            <Switch\n              ref={switchRef as any}\n              checked={!isMonthly}\n              onCheckedChange={handleToggle}\n              className=\"relative\"\n            />\n          </Label>\n        </label>\n        <span className=\"ml-2 font-semibold\">\n          Annual billing <span className=\"text-primary\">(Save 20%)</span>\n        </span>\n      </div>\n\n      <div className=\"sm:2 grid grid-cols-1 gap-4 md:grid-cols-3\">\n        {plans.map((plan, index) => (\n          <motion.div\n            key={index}\n            initial={{ y: 50, opacity: 1 }}\n            whileInView={\n              isDesktop\n                ? {\n                    y: plan.isPopular ? -20 : 0,\n                    opacity: 1,\n                    x: index === 2 ? -30 : index === 0 ? 30 : 0,\n                    scale: index === 0 || index === 2 ? 0.94 : 1.0,\n                  }\n                : {}\n            }\n            viewport={{ once: true }}\n            transition={{\n              duration: 1.6,\n              type: 'spring',\n              stiffness: 100,\n              damping: 30,\n              delay: 0.4,\n              opacity: { duration: 0.5 },\n            }}\n            className={cn(\n              `bg-background relative rounded-2xl border-[1px] p-6 text-center lg:flex lg:flex-col lg:justify-center`,\n              plan.isPopular ? 'border-primary border-2' : 'border-border',\n              'flex flex-col',\n              !plan.isPopular && 'mt-5',\n              index === 0 || index === 2\n                ? 'z-0 translate-x-0 translate-y-0 -translate-z-[50px] rotate-y-[10deg] transform'\n                : 'z-10',\n              index === 0 && 'origin-right',\n              index === 2 && 'origin-left',\n            )}\n          >\n            {plan.isPopular && (\n              <div className=\"bg-primary absolute top-0 right-0 flex items-center rounded-tr-xl rounded-bl-xl px-2 py-0.5\">\n                <Star className=\"text-primary-foreground h-4 w-4 fill-current\" />\n                <span className=\"text-primary-foreground ml-1 font-sans font-semibold\">\n                  Popular\n                </span>\n              </div>\n            )}\n            <div className=\"flex flex-1 flex-col\">\n              <p className=\"text-muted-foreground text-base font-semibold\">\n                {plan.name}\n              </p>\n              <div className=\"mt-6 flex items-center justify-center gap-x-2\">\n                <span className=\"text-foreground text-5xl font-bold tracking-tight\">\n                  <NumberFlow\n                    value={\n                      isMonthly ? Number(plan.price) : Number(plan.yearlyPrice)\n                    }\n                    format={{\n                      style: 'currency',\n                      currency: 'USD',\n                      minimumFractionDigits: 0,\n                      maximumFractionDigits: 0,\n                    }}\n                    transformTiming={{\n                      duration: 500,\n                      easing: 'ease-out',\n                    }}\n                    willChange\n                    className=\"font-variant-numeric: tabular-nums\"\n                  />\n                </span>\n                {plan.period !== 'Next 3 months' && (\n                  <span className=\"text-muted-foreground text-sm leading-6 font-semibold tracking-wide\">\n                    / {plan.period}\n                  </span>\n                )}\n              </div>\n\n              <p className=\"text-muted-foreground text-xs leading-5\">\n                {isMonthly ? 'billed monthly' : 'billed annually'}\n              </p>\n\n              <ul className=\"mt-5 flex flex-col gap-2\">\n                {plan.features.map((feature, idx) => (\n                  <li key={idx} className=\"flex items-start gap-2\">\n                    <Check className=\"text-primary mt-1 h-4 w-4 flex-shrink-0\" />\n                    <span className=\"text-left\">{feature}</span>\n                  </li>\n                ))}\n              </ul>\n\n              <hr className=\"my-4 w-full\" />\n\n              <Link\n                href={plan.href}\n                className={cn(\n                  buttonVariants({\n                    variant: 'outline',\n                  }),\n                  'group relative w-full gap-2 overflow-hidden text-lg font-semibold tracking-tighter',\n                  'hover:bg-primary hover:text-primary-foreground hover:ring-primary transform-gpu ring-offset-current transition-all duration-300 ease-out hover:ring-2 hover:ring-offset-1',\n                  plan.isPopular\n                    ? 'bg-primary text-primary-foreground'\n                    : 'bg-background text-foreground',\n                )}\n              >\n                {plan.buttonText}\n              </Link>\n              <p className=\"text-muted-foreground mt-6 text-xs leading-5\">\n                {plan.description}\n              </p>\n            </div>\n          </motion.div>\n        ))}\n      </div>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/pricing/congusted-pricing.tsx", "target": "components/mvpblocks/congusted-pricing.tsx"}]}