{"name": "users-table", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { memo } from 'react';\nimport { motion } from 'framer-motion';\nimport { Button } from '@/components/ui/button';\nimport {\n  TrendingUp,\n  Plus,\n  Calendar,\n  Mail,\n  MapPin,\n  MoreHorizontal,\n} from 'lucide-react';\n\nconst users = [\n  {\n    id: 1,\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    avatar: '/assets/avatars/avatar-1.webp',\n    role: 'Admin',\n    status: 'active',\n    joinDate: '2024-01-15',\n    location: 'New York, US',\n  },\n  {\n    id: 2,\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    avatar: '/assets/avatars/avatar-2.webp',\n    role: 'User',\n    status: 'active',\n    joinDate: '2024-02-20',\n    location: 'San Francisco, US',\n  },\n  {\n    id: 3,\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    avatar: '/assets/avatars/avatar-3.webp',\n    role: 'Moderator',\n    status: 'inactive',\n    joinDate: '2024-01-08',\n    location: 'London, UK',\n  },\n];\n\ninterface UsersTableProps {\n  onAddUser: () => void;\n}\n\nexport const UsersTable = memo(({ onAddUser }: UsersTableProps) => {\n  return (\n    <div className=\"border-border bg-card/40 rounded-xl border p-3 sm:p-6\">\n      <div className=\"mb-6 flex flex-col justify-between gap-4 sm:flex-row sm:items-center\">\n        <div>\n          <h3 className=\"text-lg font-semibold sm:text-xl\">Recent Users</h3>\n          <p className=\"text-muted-foreground text-sm\">\n            Latest user registrations and activity\n          </p>\n        </div>\n        <div className=\"flex items-center gap-2\">\n          <div className=\"flex items-center gap-1 text-sm text-green-500\">\n            <TrendingUp className=\"h-4 w-4\" />\n            <span>+12%</span>\n          </div>\n          <Button variant=\"outline\" size=\"sm\" onClick={onAddUser}>\n            <Plus className=\"mr-2 h-4 w-4\" />\n            <span className=\"hidden sm:inline\">Add User</span>\n            <span className=\"sm:hidden\">Add</span>\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"space-y-2\">\n        {users.map((user, index) => (\n          <motion.div\n            key={user.id}\n            initial={{ opacity: 0, y: 10 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.05 }}\n            className=\"group hover:bg-accent/50 flex flex-col items-start gap-4 rounded-lg p-4 transition-colors sm:flex-row sm:items-center\"\n          >\n            <div className=\"flex w-full items-center gap-4 sm:w-auto\">\n              <div className=\"relative\">\n                <img\n                  src={user.avatar}\n                  alt={user.name}\n                  width={40}\n                  height={40}\n                  className=\"rounded-full\"\n                />\n                <div\n                  className={`border-background absolute -right-1 -bottom-1 h-3 w-3 rounded-full border-2 ${\n                    user.status === 'active' ? 'bg-green-500' : 'bg-red-500'\n                  }`}\n                />\n              </div>\n\n              <div className=\"min-w-0 flex-1\">\n                <div className=\"flex flex-wrap items-center gap-2\">\n                  <h4 className=\"truncate text-sm font-medium\">{user.name}</h4>\n                  <span\n                    className={`rounded-full px-2 py-1 text-xs font-medium ${\n                      user.role === 'Admin'\n                        ? 'bg-purple-500/10 text-purple-500'\n                        : user.role === 'Moderator'\n                          ? 'bg-blue-500/10 text-blue-500'\n                          : 'bg-gray-500/10 text-gray-500'\n                    }`}\n                  >\n                    {user.role}\n                  </span>\n                </div>\n                <div className=\"text-muted-foreground mt-1 flex flex-col gap-2 text-xs sm:flex-row sm:items-center sm:gap-4\">\n                  <div className=\"flex items-center gap-1\">\n                    <Mail className=\"h-3 w-3\" />\n                    <span className=\"truncate\">{user.email}</span>\n                  </div>\n                  <div className=\"flex items-center gap-1\">\n                    <MapPin className=\"h-3 w-3\" />\n                    <span>{user.location}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"ml-auto flex items-center gap-3\">\n              <div className=\"text-muted-foreground flex items-center gap-1 text-xs\">\n                <Calendar className=\"h-3 w-3\" />\n                <span>{new Date(user.joinDate).toLocaleDateString()}</span>\n              </div>\n\n              <Button variant=\"ghost\" size=\"sm\" className=\"ml-auto\">\n                <MoreHorizontal className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </motion.div>\n        ))}\n      </div>\n    </div>\n  );\n});\n\nUsersTable.displayName = 'UsersTable';\n", "path": "/components/mvpblocks/dashboards/admin-dashboard-1/ui/users-table.tsx", "target": "components/mvpblocks/ui/users-table.tsx"}]}