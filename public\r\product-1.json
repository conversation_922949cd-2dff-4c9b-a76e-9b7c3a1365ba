{"name": "product-1", "type": "registry:block", "dependencies": ["react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\n\nimport React, { useEffect } from 'react';\n\nconst LearnAndGrowCard: React.FC = () => {\n  useEffect(() => {\n    const phoneGradient = document.getElementById('glowingGradient');\n    let angle = 0;\n    let animationFrameId: number;\n\n    const animateGradients = () => {\n      angle = (angle + 1) % 360;\n      const x1 = 50 + 45 * Math.cos((angle * Math.PI) / 180);\n      const y1 = 50 + 45 * Math.sin((angle * Math.PI) / 180);\n      const x2 = 50 + 45 * Math.cos(((angle + 180) * Math.PI) / 180);\n      const y2 = 50 + 45 * Math.sin(((angle + 180) * Math.PI) / 180);\n\n      if (phoneGradient) {\n        phoneGradient.setAttribute('x1', `${x1}%`);\n        phoneGradient.setAttribute('y1', `${y1}%`);\n        phoneGradient.setAttribute('x2', `${x2}%`);\n        phoneGradient.setAttribute('y2', `${y2}%`);\n      }\n\n      animationFrameId = requestAnimationFrame(animateGradients);\n    };\n\n    animateGradients();\n\n    return () => {\n      cancelAnimationFrame(animationFrameId);\n    };\n  }, []);\n\n  return (\n    <>\n      <div className=\"card-container\">\n        <div className=\"flex w-[296px] max-w-[320px] min-w-[230px] flex-col overflow-hidden rounded-[20px] border border-white/10 bg-[#1a1b22] shadow-xl shadow-black/30\">\n          <div className=\"relative flex h-[180px] w-full items-center justify-center overflow-hidden bg-[#1E2130]\">\n            <div className=\"relative w-[180px]\">\n              {/* Phone body */}\n              <div className=\"phone-body absolute top-[-70px] left-0 h-[320px] w-[180px] rounded-[28px] border border-white/20 bg-gradient-to-br from-white/15 to-white/5\"></div>\n\n              {/* Screen shape */}\n              <div className=\"screen-shape absolute top-[-63px] left-[7px] h-[306px] w-[166px] rounded-[20px] border border-white/10 bg-gradient-to-b from-[#2A2D3A] to-[#1a1c20]\">\n                {/* Screen content */}\n                <div className=\"screen-content h-full w-full overflow-hidden rounded-[18px] bg-gradient-to-br from-[#3B4058] to-[#1E2130]\">\n                  {/* Status bar */}\n                  <div className=\"relative flex h-[26px] w-full items-center justify-between rounded-t-[18px] bg-black px-3\">\n                    <div className=\"absolute top-0 left-1/2 flex h-[18px] w-[40%] -translate-x-1/2 items-center justify-center space-x-1 rounded-b-[12px] bg-black\">\n                      <div className=\"h-[4px] w-[4px] rounded-full bg-gray-500\"></div>\n                      <div className=\"h-[4px] w-[4px] rounded-full bg-gray-300\"></div>\n                    </div>\n                    <div className=\"z-10 ml-3 text-[10px] font-medium text-white\">\n                      9:41\n                    </div>\n                    <div className=\"z-10 mr-3 flex space-x-2\">\n                      <svg\n                        className=\"h-3.5 w-3.5\"\n                        viewBox=\"0 0 24 24\"\n                        fill=\"none\"\n                      >\n                        <path\n                          d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z\"\n                          fill=\"white\"\n                          fillOpacity=\"0.8\"\n                        />\n                      </svg>\n                      <svg\n                        className=\"h-3.5 w-3.5\"\n                        viewBox=\"0 0 24 24\"\n                        fill=\"none\"\n                      >\n                        <path\n                          d=\"M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 14.49.01.01.01-.01z\"\n                          fill=\"white\"\n                          fillOpacity=\"0.8\"\n                        />\n                      </svg>\n                      <svg\n                        className=\"h-3.5 w-3.5\"\n                        viewBox=\"0 0 24 24\"\n                        fill=\"none\"\n                      >\n                        <path\n                          d=\"M7 17h10V7H7v10zm2-8h6v6H9V9z\"\n                          fill=\"white\"\n                          fillOpacity=\"0.8\"\n                        />\n                      </svg>\n                    </div>\n                  </div>\n\n                  <div className=\"relative px-3 pt-2\">\n                    <div className=\"mb-3 flex items-center justify-between\">\n                      <div className=\"text-[10px] font-semibold text-white\">\n                        Design Courses\n                      </div>\n                      <div className=\"flex h-[18px] w-[18px] items-center justify-center rounded-full bg-white/10\">\n                        <svg\n                          className=\"h-2.5 w-2.5\"\n                          viewBox=\"0 0 24 24\"\n                          fill=\"none\"\n                        >\n                          <path\n                            d=\"M12 8l-6 6 1.41 1.41L12 10.83l4.59 4.58L18 14z\"\n                            fill=\"white\"\n                          />\n                        </svg>\n                      </div>\n                    </div>\n\n                    <div className=\"relative grid grid-cols-2 gap-2\">\n                      <div className=\"h-[40px] rounded-md bg-white/10\"></div>\n                      <div className=\"h-[40px] rounded-md bg-white/10\"></div>\n                      <div className=\"h-[40px] rounded-md bg-white/10\"></div>\n                      <div className=\"h-[40px] rounded-md bg-white/10\"></div>\n\n                      {/* Play button positioned in the middle of the 4 rectangles with animated blobs */}\n                      <div\n                        className=\"play-button absolute z-20 flex h-[48px] w-[48px] items-center justify-center overflow-hidden rounded-[14px] border border-white/15 bg-[rgba(26,28,32,0.7)] shadow-lg backdrop-blur-md\"\n                        style={{\n                          left: 'calc(50% - 24px)',\n                          top: 'calc(50% - 24px)',\n                        }}\n                      >\n                        {/* Animated blobs */}\n                        <div className=\"blob blob-1\"></div>\n                        <div className=\"blob blob-2\"></div>\n                        <div className=\"blob blob-3\"></div>\n\n                        {/* Play icon */}\n                        <div className=\"relative z-10 flex h-6 w-6 items-center justify-center\">\n                          <svg\n                            className=\"h-4 w-4\"\n                            viewBox=\"0 0 24 24\"\n                            fill=\"none\"\n                          >\n                            <path d=\"M8 5v14l11-7L8 5z\" fill=\"white\" />\n                          </svg>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Phone outline with animated stroke */}\n              <svg\n                className=\"absolute top-[-70px] left-0 z-30 h-[320px] w-[180px]\"\n                viewBox=\"0 0 180 320\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n              >\n                <defs>\n                  <linearGradient\n                    id=\"glowingGradient\"\n                    x1=\"0%\"\n                    y1=\"0%\"\n                    x2=\"100%\"\n                    y2=\"0%\"\n                  >\n                    <stop offset=\"0%\" stopColor=\"rgba(77, 124, 254, 0)\" />\n                    <stop offset=\"30%\" stopColor=\"rgba(77, 124, 254, 0.2)\" />\n                    <stop offset=\"45%\" stopColor=\"rgba(77, 124, 254, 0.8)\" />\n                    <stop offset=\"50%\" stopColor=\"rgba(77, 124, 254, 1)\" />\n                    <stop offset=\"55%\" stopColor=\"rgba(77, 124, 254, 0.8)\" />\n                    <stop offset=\"70%\" stopColor=\"rgba(77, 124, 254, 0.2)\" />\n                    <stop offset=\"100%\" stopColor=\"rgba(77, 124, 254, 0)\" />\n                  </linearGradient>\n                </defs>\n                <path\n                  className=\"phone-path\"\n                  fill=\"none\"\n                  stroke=\"url(#glowingGradient)\"\n                  strokeWidth=\"2.5\"\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  filter=\"drop-shadow(0 0 3px rgba(77, 124, 254, 0.6))\"\n                  d=\"M 28,1 H 152 C 166.9,1 179,13.1 179,28 V 292 C 179,306.9 166.9,319 152,319 H 28 C 13.1,319 1,306.9 1,292 V 28 C 1,13.1 13.1,1 28,1 Z\"\n                />\n              </svg>\n            </div>\n\n            <div className=\"absolute right-0 bottom-0 left-0 h-[60px] bg-gradient-to-t from-[#1a1b22] to-transparent\"></div>\n          </div>\n\n          <div className=\"flex flex-col items-center space-y-5 border-t border-white/5 bg-[#1a1b22] px-5 pt-5 pb-6\">\n            <div className=\"card-text flex flex-col items-center space-y-2\">\n              <h3 className=\"text-center text-lg font-semibold text-white\">\n                Learn and Grow\n              </h3>\n              <p className=\"text-center text-sm leading-snug text-white opacity-70\">\n                Access guides and resources to enhance your design skills and\n                advance your career\n              </p>\n            </div>\n\n            <div className=\"card-button w-full\">\n              <button className=\"flex w-full items-center justify-center rounded-[14px] bg-gradient-to-r from-[#4D7CFE] to-[#3361D8] py-3 shadow-lg shadow-blue-900/20 transition-all duration-300 hover:from-[#5A89FF] hover:to-[#3D6BE0]\">\n                <span className=\"text-sm font-medium text-white\">\n                  Explore Resources\n                </span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <style jsx global>{`\n        .card-container {\n          opacity: 0;\n          transform: scale(0.95);\n          animation: fadeInScale 0.8s ease-out forwards;\n        }\n        @keyframes fadeInScale {\n          0% {\n            opacity: 0;\n            transform: scale(0.95);\n          }\n          100% {\n            opacity: 1;\n            transform: scale(1);\n          }\n        }\n        @keyframes slideUp {\n          from {\n            transform: translateY(100%);\n            opacity: 0;\n          }\n          to {\n            transform: translateY(0);\n            opacity: 1;\n          }\n        }\n        @keyframes tracePath {\n          0% {\n            stroke-dashoffset: 1000;\n          }\n          100% {\n            stroke-dashoffset: 0;\n          }\n        }\n        .phone-body {\n          animation: slideUp 0.8s ease-out 0.3s forwards;\n          opacity: 0;\n          transform: translateY(100%);\n        }\n        .screen-shape {\n          animation: slideUp 0.8s ease-out 0.5s forwards;\n          opacity: 0;\n          transform: translateY(100%);\n        }\n        .screen-content {\n          animation: slideUp 0.8s ease-out 0.7s forwards;\n          opacity: 0;\n          transform: translateY(100%);\n        }\n        .play-button {\n          animation: slideUp 0.8s ease-out 0.9s forwards;\n          opacity: 0;\n          transform: translateY(100%);\n        }\n        .card-text {\n          animation: slideUp 0.6s ease-out 1s forwards;\n          opacity: 0;\n          transform: translateY(20px);\n        }\n        .card-button {\n          animation: slideUp 0.6s ease-out 1.2s forwards;\n          opacity: 0;\n          transform: translateY(20px);\n        }\n        .phone-path {\n          stroke-dasharray: 1000;\n          stroke-dashoffset: 1000;\n          animation: tracePath 4s linear 1.1s infinite;\n        }\n        .blob {\n          position: absolute;\n          border-radius: 50%;\n          filter: blur(10px);\n          opacity: 0.6;\n          mix-blend-mode: screen;\n        }\n        .blob-1 {\n          width: 60%;\n          height: 60%;\n          background: #7b5ee0;\n          animation: blob1 15s ease-in-out infinite alternate;\n        }\n        .blob-2 {\n          width: 45%;\n          height: 45%;\n          background: #4d7cfe;\n          animation: blob2 18s ease-in-out infinite alternate;\n        }\n        .blob-3 {\n          width: 40%;\n          height: 40%;\n          background: #9364f7;\n          animation: blob3 20s ease-in-out infinite alternate;\n        }\n        @keyframes blob1 {\n          0%,\n          100% {\n            transform: translate(-20%, -20%);\n          }\n          25% {\n            transform: translate(10%, 15%);\n          }\n          50% {\n            transform: translate(20%, -10%);\n          }\n          75% {\n            transform: translate(-10%, 20%);\n          }\n        }\n        @keyframes blob2 {\n          0%,\n          100% {\n            transform: translate(20%, 20%);\n          }\n          25% {\n            transform: translate(-15%, 10%);\n          }\n          50% {\n            transform: translate(-20%, -15%);\n          }\n          75% {\n            transform: translate(15%, -20%);\n          }\n        }\n        @keyframes blob3 {\n          0%,\n          100% {\n            transform: translate(0%, 0%);\n          }\n          25% {\n            transform: translate(-20%, -15%);\n          }\n          50% {\n            transform: translate(15%, 20%);\n          }\n          75% {\n            transform: translate(20%, -20%);\n          }\n        }\n      `}</style>\n    </>\n  );\n};\n\nexport default LearnAndGrowCard;\n", "path": "/components/mvpblocks/cards/product/product-1.tsx", "target": "components/mvpblocks/product-1.tsx"}]}