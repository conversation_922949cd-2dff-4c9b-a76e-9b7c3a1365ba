{"name": "team-9", "author": "mosespace", "type": "registry:block", "dependencies": ["lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { cn } from '@/lib/utils';\nimport { useState } from 'react';\n\ntype TeamMember = {\n  id: number;\n  name: string;\n  role: string;\n  bio?: string;\n  image: string;\n  color?: string; // For the card background color\n};\n\ntype PremiumTeamProps = {\n  title?: string;\n  subtitle?: string;\n  teamMembers: TeamMember[];\n  backgroundColor?: string;\n  textColor?: string;\n  className?: string;\n};\n\nconst dami_data: TeamMember[] = [\n  {\n    id: 1,\n    name: '<PERSON><PERSON>',\n    role: 'Chief Executive Officer',\n    bio: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy.',\n    image:\n      'https://img.freepik.com/free-psd/3d-rendering-avatar_23-2150833554.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 2,\n    name: '<PERSON>',\n    role: 'Chief Technology Officer',\n    bio: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy.',\n    image:\n      'https://img.freepik.com/premium-photo/png-headset-headphones-portrait-cartoon_53876-762197.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 3,\n    name: 'Zainab Rahman',\n    role: 'Chief Operations Officer',\n    bio: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy.',\n    image:\n      'https://img.freepik.com/premium-photo/png-cartoon-portrait-glasses-white-background_53876-905385.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 4,\n    name: 'Aiden Davis',\n    role: 'Chief Marketing Officer',\n    bio: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy.',\n    image:\n      'https://img.freepik.com/premium-psd/3d-avatar-character_975163-690.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 5,\n    name: 'Aysha Hussain',\n    role: 'UX Designer',\n    bio: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy.',\n    image:\n      'https://img.freepik.com/free-photo/fun-3d-illustration-american-referee_183364-81231.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 6,\n    name: 'Samira Shah',\n    role: 'Product Manager',\n    bio: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy.',\n    image:\n      'https://img.freepik.com/premium-psd/lego-character-with-blue-button-his-chest_1217673-223400.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 7,\n    name: 'Ethan Williams',\n    role: 'Backend Developer',\n    bio: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy.',\n    image:\n      'https://img.freepik.com/premium-photo/there-is-black-girl-with-headphones-yellow-jacket_1034474-106535.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 8,\n    name: 'Amina Khan',\n    role: 'Frontend Developer',\n    bio: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy.',\n    image:\n      'https://img.freepik.com/free-photo/portrait-young-student-with-book-education-day_23-2150980030.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n];\n\nexport default function Team9({\n  title = 'Partnered with most of the',\n  subtitle = 'top people at each industry',\n  teamMembers = dami_data,\n  backgroundColor = '',\n  textColor = '#ffff',\n  className,\n}: PremiumTeamProps) {\n  const [activeIndex, setActiveIndex] = useState<number | null>(1); // Default second card active\n\n  // Function to handle mouse enter\n  const handleMouseEnter = (index: number) => {\n    setActiveIndex(index);\n  };\n\n  return (\n    <section\n      className={cn('w-full py-16', className)}\n      style={{ backgroundColor, color: textColor }}\n    >\n      <div className=\"container mx-auto px-4\">\n        <div className=\"mb-16 text-center\">\n          <h2 className=\"mb-1 text-2xl font-medium\">{title}</h2>\n          <p className=\"text-xl font-light italic opacity-80\">{subtitle}</p>\n        </div>\n\n        <div className=\"relative flex justify-center gap-4\">\n          {teamMembers.slice(0, 4).map((member, index) => {\n            const isActive = activeIndex === index;\n            return (\n              <div\n                key={member.id}\n                className=\"w-[16rem] cursor-pointer overflow-hidden rounded-xl text-white transition-all duration-500 ease-in-out\"\n                style={{\n                  backgroundColor: isActive\n                    ? member.color || '#3F72AF'\n                    : '#112D4E',\n                  height: '360px',\n                }}\n                onMouseEnter={() => handleMouseEnter(index)}\n              >\n                <div className=\"flex h-full w-full flex-col\">\n                  {/* Person image */}\n                  <div\n                    className={cn(\n                      'relative transition-all duration-500 ease-in-out',\n                      isActive ? 'h-3/5' : 'h-4/5',\n                    )}\n                  >\n                    <img\n                      src={member.image || '/placeholder.svg'}\n                      alt={member.name}\n                      className=\"aspect-square object-cover object-top\"\n                    />\n                  </div>\n\n                  {/* Text content */}\n                  <div\n                    className={cn(\n                      'flex flex-col p-4 transition-all duration-500 ease-in-out',\n                      isActive ? 'h-2/5' : 'h-1/5',\n                    )}\n                  >\n                    {isActive && member.bio && (\n                      <div className=\"mb-2 line-clamp-3 overflow-hidden text-sm opacity-80 transition-opacity duration-500 ease-in-out\">\n                        {member.bio}\n                      </div>\n                    )}\n                    <div className=\"mt-auto\">\n                      <h3 className=\"text-lg font-medium\">{member.name}</h3>\n                      <p className=\"text-sm opacity-70\">{member.role}</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/team/team-9.tsx", "target": "components/mvpblocks/team-9.tsx"}]}