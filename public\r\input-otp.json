{"name": "input-otp", "type": "registry:ui", "dependencies": ["input-otp"], "files": [{"type": "registry:ui", "content": "'use client';\n\nimport * as React from 'react';\nimport { OTPInput, OTPInputContext } from 'input-otp';\nimport { Dot } from 'lucide-react';\n\nimport { cn } from '@/lib/utils';\n\nconst InputOTP = React.forwardRef<\n  React.ElementRef<typeof OTPInput>,\n  React.ComponentPropsWithoutRef<typeof OTPInput>\n>(({ className, containerClassName, ...props }, ref) => (\n  <OTPInput\n    ref={ref}\n    containerClassName={cn(\n      'flex items-center gap-2 has-[:disabled]:opacity-50',\n      containerClassName,\n    )}\n    className={cn('disabled:cursor-not-allowed', className)}\n    {...props}\n  />\n));\nInputOTP.displayName = 'InputOTP';\n\nconst InputOTPGroup = React.forwardRef<\n  React.ElementRef<'div'>,\n  React.ComponentPropsWithoutRef<'div'>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('flex items-center', className)} {...props} />\n));\nInputOTPGroup.displayName = 'InputOTPGroup';\n\nconst InputOTPSlot = React.forwardRef<\n  React.ElementRef<'div'>,\n  React.ComponentPropsWithoutRef<'div'> & { index: number }\n>(({ index, className, ...props }, ref) => {\n  const inputOTPContext = React.useContext(OTPInputContext);\n  const { char, hasFakeCaret, isActive } = inputOTPContext.slots[index];\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        'border-input relative flex h-10 w-10 items-center justify-center border-y border-r text-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md',\n        isActive && 'ring-ring ring-offset-background z-10 ring-2',\n        className,\n      )}\n      {...props}\n    >\n      {char}\n      {hasFakeCaret && (\n        <div className=\"pointer-events-none absolute inset-0 flex items-center justify-center\">\n          <div className=\"animate-caret-blink bg-foreground h-4 w-px duration-1000\" />\n        </div>\n      )}\n    </div>\n  );\n});\nInputOTPSlot.displayName = 'InputOTPSlot';\n\nconst InputOTPSeparator = React.forwardRef<\n  React.ElementRef<'div'>,\n  React.ComponentPropsWithoutRef<'div'>\n>(({ ...props }, ref) => (\n  <div ref={ref} role=\"separator\" {...props}>\n    <Dot />\n  </div>\n));\nInputOTPSeparator.displayName = 'InputOTPSeparator';\n\nexport { InputOTP, InputOTPGroup, InputOTPSlot, InputOTPSeparator };\n", "path": "/components/ui/input-otp.tsx", "target": "components/ui/input-otp.tsx"}]}