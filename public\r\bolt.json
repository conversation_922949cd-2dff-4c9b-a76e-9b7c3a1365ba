{"name": "bolt", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json"], "files": [{"type": "registry:block", "content": "import {\n  Paperclip,\n  Terminal,\n  Figma,\n  FileUp,\n  MonitorIcon,\n  Sparkles,\n} from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nconst EXAMPLE_ACTIONS = [\n  { icon: <Figma className=\"h-4 w-4\" />, text: 'Import from Figma' },\n  {\n    icon: <FileUp className=\"h-4 w-4\" />,\n    text: 'Build a mobile app with Expo',\n  },\n  {\n    icon: <MonitorIcon className=\"h-4 w-4\" />,\n    text: 'Start a blog with Astro',\n  },\n  {\n    icon: <Terminal className=\"h-4 w-4\" />,\n    text: 'Create a docs site with Vitepress',\n  },\n  { icon: <FileUp className=\"h-4 w-4\" />, text: 'Scaffold UI with shadcn' },\n];\n\nexport default function BoltChat() {\n  return (\n    <div className=\"selection-accent flex flex-grow flex-col py-20\">\n      <div className=\"mx-4 flex flex-col\">\n        <div className=\"mb-8 text-center\">\n          <h1 className=\"mb-4 text-5xl font-bold\">\n            What do you want to build?\n          </h1>\n          <p className=\"text-lg text-gray-400\">\n            Prompt, run, edit, and deploy full-stack{' '}\n            <span className=\"text-foreground font-medium\">web</span> and{' '}\n            <span className=\"text-foreground font-medium\">mobile</span> apps.\n          </p>\n        </div>\n\n        <div className=\"mx-auto mb-6 w-full max-w-lg\">\n          <div className=\"relative h-[26px]\">\n            <div className=\"bg-secondary/50 absolute top-0 left-2 flex w-[calc(100%-1rem)] flex-wrap justify-between truncate rounded-t-lg border px-2 py-1 text-xs opacity-100 backdrop-blur transition-opacity duration-350\">\n              <span>150K daily tokens remaining.</span>\n              <button className=\"text-primary mr-4 inline-block bg-transparent font-semibold hover:underline\">\n                Subscribe to Pro for 66x more usage\n              </button>\n            </div>\n          </div>\n          <div className=\"relative rounded-lg shadow-xs backdrop-blur\">\n            <div className=\"bg-secondary/50 flex flex-col rounded-lg border p-4\">\n              <textarea\n                placeholder=\"How can Bolt help you today?\"\n                className=\"mb-4 h-24 w-full resize-none bg-transparent outline-none\"\n              />\n\n              <div className=\"mt-auto flex gap-4\">\n                <button className=\"text-zinc-400\">\n                  <Paperclip size={20} />\n                </button>\n                <button className=\"text-zinc-400\">\n                  <Sparkles size={20} />\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"mx-auto mt-8 flex w-full max-w-3xl flex-wrap justify-center gap-2\">\n          {EXAMPLE_ACTIONS.map((action, index) => (\n            <Button\n              key={index}\n              size=\"sm\"\n              variant=\"outline\"\n              className=\"rounded-full px-4 py-0.5 text-xs\"\n            >\n              {action.icon}\n              <span>{action.text}</span>\n            </Button>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/chatbot-ui/bolt.tsx", "target": "components/mvpblocks/bolt.tsx"}]}