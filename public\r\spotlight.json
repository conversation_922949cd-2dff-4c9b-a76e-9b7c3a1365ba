{"name": "spotlight", "type": "registry:ui", "dependencies": ["react"], "registryDependencies": [], "files": [{"type": "registry:ui", "content": "'use client';\nimport React from 'react';\nimport { motion } from 'motion/react';\n\ntype SpotlightProps = {\n  gradientFirst?: string;\n  gradientSecond?: string;\n  gradientThird?: string;\n  translateY?: number;\n  width?: number;\n  height?: number;\n  smallWidth?: number;\n  duration?: number;\n  xOffset?: number;\n};\n\nexport const Spotlight = ({\n  gradientFirst = 'radial-gradient(68.54% 68.72% at 55.02% 31.46%, hsla(336, 100%, 50%, 0.1) 0, hsla(341, 100%, 55%, 0.02) 50%, hsla(336, 100%, 45%, 0) 80%)',\n  gradientSecond = 'radial-gradient(50% 50% at 50% 50%, hsla(333, 100%, 85%, 0.06) 0, hsla(335, 100%, 55%, 0.02) 80%, transparent 100%)',\n  gradientThird = 'radial-gradient(50% 50% at 50% 50%, hsla(332, 100%, 85%, 0.04) 0, hsla(327, 100%, 85%, 0.04) 80%, transparent 100%)',\n  translateY = -350,\n  width = 560,\n  height = 1380,\n  smallWidth = 240,\n  duration = 7,\n  xOffset = 100,\n}: SpotlightProps = {}) => {\n  return (\n    <motion.div\n      initial={{\n        opacity: 0,\n      }}\n      animate={{\n        opacity: 1,\n      }}\n      transition={{\n        duration: 1.5,\n      }}\n      className=\"pointer-events-none absolute inset-0 z-10 h-full w-full\"\n    >\n      <motion.div\n        animate={{\n          x: [0, xOffset, 0],\n        }}\n        transition={{\n          duration,\n          repeat: Infinity,\n          repeatType: 'reverse',\n          ease: 'easeInOut',\n        }}\n        className=\"pointer-events-none absolute top-0 left-0 z-0 h-screen w-screen\"\n      >\n        <div\n          style={{\n            transform: `translateY(${translateY}px) rotate(-45deg)`,\n            background: gradientFirst,\n            width: `${width}px`,\n            height: `${height}px`,\n          }}\n          className={`absolute top-0 left-0`}\n        />\n\n        <div\n          style={{\n            transform: 'rotate(-45deg) translate(5%, -50%)',\n            background: gradientSecond,\n            width: `${smallWidth}px`,\n            height: `${height}px`,\n          }}\n          className={`absolute top-0 left-0 origin-top-left`}\n        />\n\n        <div\n          style={{\n            transform: 'rotate(-45deg) translate(-180%, -70%)',\n            background: gradientThird,\n            width: `${smallWidth}px`,\n            height: `${height}px`,\n          }}\n          className={`absolute top-0 left-0 origin-top-left`}\n        />\n      </motion.div>\n\n      <motion.div\n        animate={{\n          x: [0, -xOffset, 0],\n        }}\n        transition={{\n          duration,\n          repeat: Infinity,\n          repeatType: 'reverse',\n          ease: 'easeInOut',\n        }}\n        className=\"pointer-events-none absolute top-0 right-0 z-40 h-screen w-screen\"\n      >\n        <div\n          style={{\n            transform: `translateY(${translateY}px) rotate(45deg)`,\n            background: gradientFirst,\n            width: `${width}px`,\n            height: `${height}px`,\n          }}\n          className={`absolute top-0 right-0`}\n        />\n\n        <div\n          style={{\n            transform: 'rotate(45deg) translate(-5%, -50%)',\n            background: gradientSecond,\n            width: `${smallWidth}px`,\n            height: `${height}px`,\n          }}\n          className={`absolute top-0 right-0 origin-top-right`}\n        />\n\n        <div\n          style={{\n            transform: 'rotate(45deg) translate(180%, -70%)',\n            background: gradientThird,\n            width: `${smallWidth}px`,\n            height: `${height}px`,\n          }}\n          className={`absolute top-0 right-0 origin-top-right`}\n        />\n      </motion.div>\n    </motion.div>\n  );\n};\n", "path": "/components/ui/spotlight.tsx", "target": "components/ui/spotlight.tsx"}]}