{"name": "payment-modal", "type": "registry:ui", "dependencies": ["react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/dialog.json", "https://blocks.mvp-subha.me/r/label.json", "https://blocks.mvp-subha.me/r/radio-group.json", "https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:ui", "content": "'use client';\n\nimport { useState } from 'react';\nimport {\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>ontent,\n  <PERSON>alogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog';\nimport { Button } from '@/components/ui/button';\nimport { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';\nimport { Label } from '@/components/ui/label';\nimport { cn } from '@/lib/utils';\n\ninterface PaymentMethod {\n  id: string;\n  name: string;\n  logo: string;\n  description: string;\n}\n\nconst paymentMethods: PaymentMethod[] = [\n  {\n    id: 'paypal',\n    name: 'PayPal',\n    logo: 'https://upload.wikimedia.org/wikipedia/commons/a/a4/Paypal_2014_logo.png',\n    description: 'Pay with your PayPal account',\n  },\n  {\n    id: 'stripe',\n    name: 'Stripe',\n    logo: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQQGluJhW7I1NYU7jF77E-9K9I46_ib_DUNHw&s',\n    description: 'Pay with credit card',\n  },\n  {\n    id: 'razorpay',\n    name: '<PERSON><PERSON><PERSON><PERSON>',\n    logo: 'https://cdn.prod.website-files.com/62979cdcff90ad6bae40b3ef/62d855876f4add6e152a5567_unnamed.png',\n    description: 'Pay with Indian payment methods',\n  },\n];\n\ninterface PaymentModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  plan: {\n    name: string;\n    price: number;\n    period: string;\n  };\n}\n\nexport function PaymentModal({ isOpen, onClose, plan }: PaymentModalProps) {\n  const [selectedMethod, setSelectedMethod] = useState<string>('stripe');\n  const [isLoading, setIsLoading] = useState(false);\n\n  const handleSubmit = async () => {\n    setIsLoading(true);\n    // Simulated payment processing\n    await new Promise((resolve) => setTimeout(resolve, 1500));\n    setIsLoading(false);\n    onClose();\n  };\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"p-0 sm:max-w-[500px]\">\n        <DialogHeader className=\"border-b p-6\">\n          <DialogTitle className=\"text-xl font-medium\">\n            Choose payment method\n          </DialogTitle>\n        </DialogHeader>\n        <div className=\"space-y-6 p-6\">\n          <div className=\"space-y-4\">\n            <div className=\"flex items-baseline justify-between\">\n              <h3 className=\"text-sm font-medium text-zinc-400\">\n                Selected plan\n              </h3>\n              <div className=\"text-right\">\n                <div className=\"text-sm font-medium\">{plan.name}</div>\n                <div className=\"text-2xl font-bold\">\n                  €{plan.price}\n                  <span className=\"text-sm text-zinc-400\">/{plan.period}</span>\n                </div>\n              </div>\n            </div>\n            <RadioGroup\n              value={selectedMethod}\n              onValueChange={setSelectedMethod}\n              className=\"grid gap-4\"\n            >\n              {paymentMethods.map((method) => (\n                <Label\n                  key={method.id}\n                  className={cn(\n                    'flex cursor-pointer items-center justify-between rounded-lg border px-4 py-3 transition-colors',\n                    selectedMethod === method.id ? 'border-primary' : 'border',\n                  )}\n                >\n                  <div className=\"flex items-center gap-4\">\n                    <RadioGroupItem\n                      value={method.id}\n                      className=\"border-zinc-700\"\n                    />\n                    <div className=\"space-y-1\">\n                      <div className=\"font-medium\">{method.name}</div>\n                      <div className=\"text-sm text-zinc-400\">\n                        {method.description}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"relative h-8 w-20\">\n                    <img\n                      src={method.logo || '/placeholder.svg'}\n                      alt={method.name}\n                      className=\"object-contain\"\n                    />\n                  </div>\n                </Label>\n              ))}\n            </RadioGroup>\n          </div>\n          <Button\n            className=\"w-full\"\n            onClick={handleSubmit}\n            disabled={isLoading}\n          >\n            {isLoading ? 'Processing...' : 'Continue to payment'}\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}\n", "path": "/components/ui/payment-modal.tsx", "target": "components/ui/payment-modal.tsx"}]}