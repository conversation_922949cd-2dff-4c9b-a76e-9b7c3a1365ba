{"name": "working-chatbot", "type": "registry:block", "dependencies": ["lucide-react", "react", "react-markdown", "sonner"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/textarea.json", "https://blocks.mvp-subha.me/r/use-auto-resize-textarea.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>U<PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react';\nimport { useCallback, useRef, useState } from 'react';\nimport { Textarea } from '@/components/ui/textarea';\nimport { cn } from '@/lib/utils';\nimport { useAutoResizeTextarea } from '@/hooks/use-auto-resize-textarea';\nimport { useChat } from 'ai/react';\nimport Markdown from 'react-markdown';\nimport { toast } from 'sonner';\n\nfunction AiInput({\n  value,\n  onChange,\n  onSubmit,\n  onKeyDown,\n}: {\n  value: string;\n  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;\n  onSubmit: () => void;\n  onKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;\n}) {\n  const { textareaRef, adjustHeight } = useAutoResizeTextarea({\n    minHeight: 50,\n    maxHeight: 200,\n  });\n\n  return (\n    <div className=\"w-full\">\n      <div className=\"relative mx-auto flex w-full max-w-4xl flex-col items-start gap-2\">\n        <div className=\"relative mx-auto w-full max-w-4xl\">\n          <Textarea\n            ref={textareaRef}\n            id=\"ai-input-06\"\n            placeholder=\"Ask me anything!\"\n            className={cn(\n              'bg-muted/50 text-foreground ring-primary/20 placeholder:text-muted-foreground/70 w-full max-w-4xl resize-none rounded-3xl border-none py-4 pr-12 pl-6 leading-[1.2] text-wrap',\n              'focus:ring-primary/30 min-h-[56px] transition-all duration-200 focus:ring-2',\n            )}\n            value={value}\n            onKeyDown={onKeyDown}\n            onChange={(e) => {\n              onChange(e);\n              adjustHeight();\n            }}\n          />\n          <button\n            onClick={onSubmit}\n            className={cn(\n              'bg-primary/10 hover:bg-primary/20 absolute top-1/2 right-3 -translate-y-1/2 rounded-xl p-2 transition-all duration-200',\n              value.trim() ? 'opacity-100' : 'cursor-not-allowed opacity-50',\n            )}\n            type=\"button\"\n            disabled={!value.trim()}\n          >\n            <CornerRightUp\n              className={cn(\n                'text-primary h-4 w-4 transition-opacity',\n                value ? 'opacity-100' : 'opacity-50',\n              )}\n            />\n          </button>\n        </div>\n        <p className=\"text-muted-foreground ml-4 text-xs\">\n          {value.length}/2000 characters\n        </p>\n      </div>\n    </div>\n  );\n}\n\nexport default function WorkingChatbot() {\n  const [responseTimes, setResponseTimes] = useState<Record<string, number>>(\n    {},\n  );\n  const startTimeRef = useRef<number>(0);\n  // Using theme for styling is handled by Tailwind's dark mode classes\n\n  const {\n    messages,\n    input,\n    handleInputChange,\n    handleSubmit: originalHandleSubmit,\n    status,\n    error,\n  } = useChat({\n    api: '/api/demo-chat',\n    onFinish: (message) => {\n      const endTime = Date.now();\n      const duration = (endTime - startTimeRef.current) / 1000;\n      setResponseTimes((prev) => ({\n        ...prev,\n        [message.id]: duration,\n      }));\n    },\n  });\n\n  // Check if the AI is currently generating a response\n  const isLoading = status === 'submitted' || status === 'streaming';\n\n  const handleSubmit = useCallback(\n    (e?: React.FormEvent) => {\n      if (!input.trim()) return;\n      startTimeRef.current = Date.now();\n      originalHandleSubmit(e);\n    },\n    [originalHandleSubmit, input],\n  );\n\n  const handleKeyDown = useCallback(\n    (event: React.KeyboardEvent<HTMLTextAreaElement>) => {\n      if (event.key === 'Enter' && !event.shiftKey) {\n        event.preventDefault();\n        handleSubmit();\n      }\n    },\n    [handleSubmit],\n  );\n\n  return (\n    <div className=\"mx-auto flex h-svh w-full max-w-4xl flex-col pb-0.5\">\n      <div className=\"border-primary/20 bg-card/40 text-card-foreground h-full flex-1 overflow-y-auto rounded-xl border p-4 text-sm leading-6 shadow-md sm:text-base sm:leading-7\">\n        {messages.length > 0 ? (\n          messages.map((m) => {\n            return (\n              <div key={m.id} className=\"mb-4 whitespace-pre-wrap\">\n                {m.role === 'user' ? (\n                  <div className=\"flex flex-row px-2 py-4 sm:px-4\">\n                    <img\n                      alt=\"user\"\n                      className=\"mr-2 flex size-6 rounded-full sm:mr-4 md:size-8\"\n                      src=\"/logo.webp\"\n                      width={32}\n                      height={32}\n                    />\n                    <div className=\"flex max-w-3xl items-center\">\n                      <p>{m.content}</p>\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"relative mb-4 flex rounded-xl bg-neutral-50 px-2 py-6 sm:px-4 dark:bg-neutral-900\">\n                    <Bot className=\"bg-secondary text-primary mr-2 flex size-8 rounded-full p-1 sm:mr-4\" />{' '}\n                    <div className=\"markdown-body w-full max-w-3xl overflow-x-auto rounded-xl\">\n                      <Markdown>{m.content}</Markdown>\n                      {responseTimes[m.id] && (\n                        <div className=\"mt-2 text-xs text-neutral-500\">\n                          Response time: {responseTimes[m.id].toFixed(3)}s\n                        </div>\n                      )}\n                    </div>\n                    <button\n                      type=\"button\"\n                      title=\"copy\"\n                      className=\"absolute top-2 right-2 rounded-full bg-rose-500 p-1 opacity-50 transition-all hover:opacity-75 active:scale-95 dark:bg-neutral-800\"\n                      onClick={() => {\n                        navigator.clipboard.writeText(m.content);\n                        toast.success('Copied to clipboard');\n                      }}\n                    >\n                      <Copy className=\"h-4 w-4 text-white\" />\n                    </button>\n                  </div>\n                )}\n              </div>\n            );\n          })\n        ) : (\n          <div className=\"flex h-full flex-col items-center justify-center\">\n            <p className=\"text-muted-foreground mx-auto px-2 text-center text-xl font-semibold tracking-wide md:text-2xl\">\n              Start Chatting with\n              <br />\n              <span className=\"text-primary text-2xl font-bold md:text-4xl\">\n                MVPBlocks\n              </span>\n              <span className=\"text-primary\">.AI</span>\n            </p>\n            <div className=\"group relative mt-6\">\n              <div className=\"from-primary/30 to-primary/10 absolute -inset-1 rounded-full bg-gradient-to-r opacity-75 blur-md transition-opacity duration-500 group-hover:opacity-100\"></div>\n              <img\n                src=\"/assets/robo.svg\"\n                alt=\"AI Assistant\"\n                width={250}\n                height={250}\n                className=\"relative transition-all duration-500 hover:scale-105 active:scale-95\"\n              />\n            </div>\n          </div>\n        )}\n        {isLoading && (\n          <div className=\"bg-primary/5 mx-auto flex w-fit items-center gap-2 rounded-full px-4 py-2\">\n            <Sparkles className=\"text-primary h-4 w-4 animate-pulse\" />\n            <span className=\"from-primary/80 to-primary animate-pulse bg-gradient-to-r bg-clip-text text-sm font-medium text-transparent\">\n              Generating response...\n            </span>\n          </div>\n        )}\n        {error && (\n          <div className=\"border-destructive/20 bg-destructive/10 text-destructive mx-auto w-fit rounded-lg border p-3\">\n            Something went wrong! Please try again.\n          </div>\n        )}\n      </div>\n\n      <form className=\"mt-2\" onSubmit={handleSubmit}>\n        <div className=\"relative\">\n          <AiInput\n            value={input}\n            onChange={handleInputChange}\n            onSubmit={handleSubmit}\n            onKeyDown={handleKeyDown}\n          />\n        </div>\n      </form>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/chatbot-ui/working-chatbot.tsx", "target": "components/mvpblocks/working-chatbot.tsx"}]}