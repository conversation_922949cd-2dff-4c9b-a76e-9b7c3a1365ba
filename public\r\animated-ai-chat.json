{"name": "animated-ai-chat", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { useEffect, useRef, useCallback, useTransition } from 'react';\nimport { useState } from 'react';\nimport { cn } from '@/lib/utils';\nimport {\n  ImageIcon,\n  Figma,\n  MonitorIcon,\n  Paperclip,\n  SendIcon,\n  XIcon,\n  LoaderIcon,\n  Sparkles,\n  Command,\n} from 'lucide-react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport * as React from 'react';\n\ninterface UseAutoResizeTextareaProps {\n  minHeight: number;\n  maxHeight?: number;\n}\n\nfunction useAutoResizeTextarea({\n  minHeight,\n  maxHeight,\n}: UseAutoResizeTextareaProps) {\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\n\n  const adjustHeight = useCallback(\n    (reset?: boolean) => {\n      const textarea = textareaRef.current;\n      if (!textarea) return;\n\n      if (reset) {\n        textarea.style.height = `${minHeight}px`;\n        return;\n      }\n\n      textarea.style.height = `${minHeight}px`;\n      const newHeight = Math.max(\n        minHeight,\n        Math.min(textarea.scrollHeight, maxHeight ?? Number.POSITIVE_INFINITY),\n      );\n\n      textarea.style.height = `${newHeight}px`;\n    },\n    [minHeight, maxHeight],\n  );\n\n  useEffect(() => {\n    const textarea = textareaRef.current;\n    if (textarea) {\n      textarea.style.height = `${minHeight}px`;\n    }\n  }, [minHeight]);\n\n  useEffect(() => {\n    const handleResize = () => adjustHeight();\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, [adjustHeight]);\n\n  return { textareaRef, adjustHeight };\n}\n\ninterface CommandSuggestion {\n  icon: React.ReactNode;\n  label: string;\n  description: string;\n  prefix: string;\n}\n\ninterface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {\n  containerClassName?: string;\n  showRing?: boolean;\n}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, containerClassName, showRing = true, ...props }, ref) => {\n    const [isFocused, setIsFocused] = React.useState(false);\n\n    return (\n      <div className={cn('relative', containerClassName)}>\n        <textarea\n          className={cn(\n            'border-input bg-background flex min-h-[80px] w-full rounded-md border px-3 py-2 text-sm',\n            'transition-all duration-200 ease-in-out',\n            'placeholder:text-muted-foreground',\n            'disabled:cursor-not-allowed disabled:opacity-50',\n            showRing\n              ? 'focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none'\n              : '',\n            className,\n          )}\n          ref={ref}\n          onFocus={() => setIsFocused(true)}\n          onBlur={() => setIsFocused(false)}\n          {...props}\n        />\n\n        {showRing && isFocused && (\n          <motion.span\n            className=\"ring-primary/30 pointer-events-none absolute inset-0 rounded-md ring-2 ring-offset-0\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 0.2 }}\n          />\n        )}\n\n        {props.onChange && (\n          <div\n            className=\"bg-primary absolute right-2 bottom-2 h-2 w-2 rounded-full opacity-0\"\n            style={{\n              animation: 'none',\n            }}\n            id=\"textarea-ripple\"\n          />\n        )}\n      </div>\n    );\n  },\n);\nTextarea.displayName = 'Textarea';\n\nexport default function AnimatedAIChat() {\n  const [value, setValue] = useState('');\n  const [attachments, setAttachments] = useState<string[]>([]);\n  const [isTyping, setIsTyping] = useState(false);\n  const [isPending, startTransition] = useTransition();\n  const [activeSuggestion, setActiveSuggestion] = useState<number>(-1);\n  const [showCommandPalette, setShowCommandPalette] = useState(false);\n  const [recentCommand, setRecentCommand] = useState<string | null>(null);\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  const { textareaRef, adjustHeight } = useAutoResizeTextarea({\n    minHeight: 60,\n    maxHeight: 200,\n  });\n  const [inputFocused, setInputFocused] = useState(false);\n  const commandPaletteRef = useRef<HTMLDivElement>(null);\n\n  const commandSuggestions: CommandSuggestion[] = [\n    {\n      icon: <ImageIcon className=\"h-4 w-4\" />,\n      label: 'Clone UI',\n      description: 'Generate a UI from a screenshot',\n      prefix: '/clone',\n    },\n    {\n      icon: <Figma className=\"h-4 w-4\" />,\n      label: 'Import Figma',\n      description: 'Import a design from Figma',\n      prefix: '/figma',\n    },\n    {\n      icon: <MonitorIcon className=\"h-4 w-4\" />,\n      label: 'Create Page',\n      description: 'Generate a new web page',\n      prefix: '/page',\n    },\n    {\n      icon: <Sparkles className=\"h-4 w-4\" />,\n      label: 'Improve',\n      description: 'Improve existing UI design',\n      prefix: '/improve',\n    },\n  ];\n\n  useEffect(() => {\n    if (value.startsWith('/') && !value.includes(' ')) {\n      setShowCommandPalette(true);\n\n      const matchingSuggestionIndex = commandSuggestions.findIndex((cmd) =>\n        cmd.prefix.startsWith(value),\n      );\n\n      if (matchingSuggestionIndex >= 0) {\n        setActiveSuggestion(matchingSuggestionIndex);\n      } else {\n        setActiveSuggestion(-1);\n      }\n    } else {\n      setShowCommandPalette(false);\n    }\n  }, [value]);\n\n  useEffect(() => {\n    const handleMouseMove = (e: MouseEvent) => {\n      setMousePosition({ x: e.clientX, y: e.clientY });\n    };\n\n    window.addEventListener('mousemove', handleMouseMove);\n    return () => {\n      window.removeEventListener('mousemove', handleMouseMove);\n    };\n  }, []);\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      const target = event.target as Node;\n      const commandButton = document.querySelector('[data-command-button]');\n\n      if (\n        commandPaletteRef.current &&\n        !commandPaletteRef.current.contains(target) &&\n        !commandButton?.contains(target)\n      ) {\n        setShowCommandPalette(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {\n    if (showCommandPalette) {\n      if (e.key === 'ArrowDown') {\n        e.preventDefault();\n        setActiveSuggestion((prev) =>\n          prev < commandSuggestions.length - 1 ? prev + 1 : 0,\n        );\n      } else if (e.key === 'ArrowUp') {\n        e.preventDefault();\n        setActiveSuggestion((prev) =>\n          prev > 0 ? prev - 1 : commandSuggestions.length - 1,\n        );\n      } else if (e.key === 'Tab' || e.key === 'Enter') {\n        e.preventDefault();\n        if (activeSuggestion >= 0) {\n          const selectedCommand = commandSuggestions[activeSuggestion];\n          setValue(selectedCommand.prefix + ' ');\n          setShowCommandPalette(false);\n\n          setRecentCommand(selectedCommand.label);\n          setTimeout(() => setRecentCommand(null), 3500);\n        }\n      } else if (e.key === 'Escape') {\n        e.preventDefault();\n        setShowCommandPalette(false);\n      }\n    } else if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      if (value.trim()) {\n        handleSendMessage();\n      }\n    }\n  };\n\n  const handleSendMessage = () => {\n    if (value.trim()) {\n      startTransition(() => {\n        setIsTyping(true);\n        setTimeout(() => {\n          setIsTyping(false);\n          setValue('');\n          adjustHeight(true);\n        }, 3000);\n      });\n    }\n  };\n\n  const handleAttachFile = () => {\n    const mockFileName = `file-${Math.floor(Math.random() * 1000)}.pdf`;\n    setAttachments((prev) => [...prev, mockFileName]);\n  };\n\n  const removeAttachment = (index: number) => {\n    setAttachments((prev) => prev.filter((_, i) => i !== index));\n  };\n\n  const selectCommandSuggestion = (index: number) => {\n    const selectedCommand = commandSuggestions[index];\n    setValue(selectedCommand.prefix + ' ');\n    setShowCommandPalette(false);\n\n    setRecentCommand(selectedCommand.label);\n    setTimeout(() => setRecentCommand(null), 2000);\n  };\n\n  return (\n    <div className=\"flex w-screen overflow-x-hidden\">\n      <div className=\"text-foreground relative flex min-h-screen w-full flex-col items-center justify-center overflow-hidden bg-transparent p-6\">\n        <div className=\"absolute inset-0 h-full w-full overflow-hidden\">\n          <div className=\"bg-primary/10 absolute top-0 left-1/4 h-96 w-96 animate-pulse rounded-full mix-blend-normal blur-[128px] filter\" />\n          <div className=\"bg-secondary/10 absolute right-1/4 bottom-0 h-96 w-96 animate-pulse rounded-full mix-blend-normal blur-[128px] filter delay-700\" />\n          <div className=\"bg-primary/10 absolute top-1/4 right-1/3 h-64 w-64 animate-pulse rounded-full mix-blend-normal blur-[96px] filter delay-1000\" />\n        </div>\n        <div className=\"relative mx-auto w-full max-w-2xl\">\n          <motion.div\n            className=\"relative z-10 space-y-12\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, ease: 'easeOut' }}\n          >\n            <div className=\"space-y-3 text-center\">\n              <motion.div\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.2, duration: 0.5 }}\n                className=\"inline-block\"\n              >\n                <h1 className=\"pb-1 text-3xl font-medium tracking-tight\">\n                  How can I help today?\n                </h1>\n                <motion.div\n                  className=\"via-primary/50 h-px bg-gradient-to-r from-transparent to-transparent\"\n                  initial={{ width: 0, opacity: 0 }}\n                  animate={{ width: '100%', opacity: 1 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                />\n              </motion.div>\n              <motion.p\n                className=\"text-muted-foreground text-sm\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.3 }}\n              >\n                Type a command or ask a question\n              </motion.p>\n            </div>\n\n            <motion.div\n              className=\"border-border bg-card/80 relative rounded-2xl border shadow-2xl backdrop-blur-2xl\"\n              initial={{ scale: 0.98 }}\n              animate={{ scale: 1 }}\n              transition={{ delay: 0.1 }}\n            >\n              <AnimatePresence>\n                {showCommandPalette && (\n                  <motion.div\n                    ref={commandPaletteRef}\n                    className=\"border-border bg-background/90 absolute right-4 bottom-full left-4 z-50 mb-2 overflow-hidden rounded-lg border shadow-lg backdrop-blur-xl\"\n                    initial={{ opacity: 0, y: 5 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: 5 }}\n                    transition={{ duration: 0.15 }}\n                  >\n                    <div className=\"bg-background py-1\">\n                      {commandSuggestions.map((suggestion, index) => (\n                        <motion.div\n                          key={suggestion.prefix}\n                          className={cn(\n                            'flex cursor-pointer items-center gap-2 px-3 py-2 text-xs transition-colors',\n                            activeSuggestion === index\n                              ? 'bg-primary/20 text-foreground'\n                              : 'text-muted-foreground hover:bg-primary/10',\n                          )}\n                          onClick={() => selectCommandSuggestion(index)}\n                          initial={{ opacity: 0 }}\n                          animate={{ opacity: 1 }}\n                          transition={{ delay: index * 0.03 }}\n                        >\n                          <div className=\"text-primary flex h-5 w-5 items-center justify-center\">\n                            {suggestion.icon}\n                          </div>\n                          <div className=\"font-medium\">{suggestion.label}</div>\n                          <div className=\"text-muted-foreground ml-1 text-xs\">\n                            {suggestion.prefix}\n                          </div>\n                        </motion.div>\n                      ))}\n                    </div>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n\n              <div className=\"p-4\">\n                <Textarea\n                  ref={textareaRef}\n                  value={value}\n                  onChange={(e) => {\n                    setValue(e.target.value);\n                    adjustHeight();\n                  }}\n                  onKeyDown={handleKeyDown}\n                  onFocus={() => setInputFocused(true)}\n                  onBlur={() => setInputFocused(false)}\n                  placeholder=\"Ask mvp.ai a question...\"\n                  containerClassName=\"w-full\"\n                  className={cn(\n                    'w-full px-4 py-3',\n                    'resize-none',\n                    'bg-transparent',\n                    'border-none',\n                    'text-foreground text-sm',\n                    'focus:outline-none',\n                    'placeholder:text-muted-foreground',\n                    'min-h-[60px]',\n                  )}\n                  style={{\n                    overflow: 'hidden',\n                  }}\n                  showRing={false}\n                />\n              </div>\n\n              <AnimatePresence>\n                {attachments.length > 0 && (\n                  <motion.div\n                    className=\"flex flex-wrap gap-2 px-4 pb-3\"\n                    initial={{ opacity: 0, height: 0 }}\n                    animate={{ opacity: 1, height: 'auto' }}\n                    exit={{ opacity: 0, height: 0 }}\n                  >\n                    {attachments.map((file, index) => (\n                      <motion.div\n                        key={index}\n                        className=\"bg-primary/5 text-muted-foreground flex items-center gap-2 rounded-lg px-3 py-1.5 text-xs\"\n                        initial={{ opacity: 0, scale: 0.9 }}\n                        animate={{ opacity: 1, scale: 1 }}\n                        exit={{ opacity: 0, scale: 0.9 }}\n                      >\n                        <span>{file}</span>\n                        <button\n                          onClick={() => removeAttachment(index)}\n                          className=\"text-muted-foreground hover:text-foreground transition-colors\"\n                        >\n                          <XIcon className=\"h-3 w-3\" />\n                        </button>\n                      </motion.div>\n                    ))}\n                  </motion.div>\n                )}\n              </AnimatePresence>\n\n              <div className=\"border-border flex items-center justify-between gap-4 border-t p-4\">\n                <div className=\"flex items-center gap-3\">\n                  <motion.button\n                    type=\"button\"\n                    onClick={handleAttachFile}\n                    whileTap={{ scale: 0.94 }}\n                    className=\"group text-muted-foreground hover:text-foreground relative rounded-lg p-2 transition-colors\"\n                  >\n                    <Paperclip className=\"h-4 w-4\" />\n                    <motion.span\n                      className=\"bg-primary/10 absolute inset-0 rounded-lg opacity-0 transition-opacity group-hover:opacity-100\"\n                      layoutId=\"button-highlight\"\n                    />\n                  </motion.button>\n                  <motion.button\n                    type=\"button\"\n                    data-command-button\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      setShowCommandPalette((prev) => !prev);\n                    }}\n                    whileTap={{ scale: 0.94 }}\n                    className={cn(\n                      'group text-muted-foreground hover:text-foreground relative rounded-lg p-2 transition-colors',\n                      showCommandPalette && 'bg-primary/20 text-foreground',\n                    )}\n                  >\n                    <Command className=\"h-4 w-4\" />\n                    <motion.span\n                      className=\"bg-primary/10 absolute inset-0 rounded-lg opacity-0 transition-opacity group-hover:opacity-100\"\n                      layoutId=\"button-highlight\"\n                    />\n                  </motion.button>\n                </div>\n\n                <motion.button\n                  type=\"button\"\n                  onClick={handleSendMessage}\n                  whileHover={{ scale: 1.01 }}\n                  whileTap={{ scale: 0.98 }}\n                  disabled={isTyping || !value.trim()}\n                  className={cn(\n                    'rounded-lg px-4 py-2 text-sm font-medium transition-all',\n                    'flex items-center gap-2',\n                    value.trim()\n                      ? 'bg-primary text-primary-foreground shadow-primary/10 shadow-lg'\n                      : 'bg-muted/50 text-muted-foreground',\n                  )}\n                >\n                  {isTyping ? (\n                    <LoaderIcon className=\"h-4 w-4 animate-[spin_2s_linear_infinite]\" />\n                  ) : (\n                    <SendIcon className=\"h-4 w-4\" />\n                  )}\n                  <span>Send</span>\n                </motion.button>\n              </div>\n            </motion.div>\n\n            <div className=\"flex flex-wrap items-center justify-center gap-2\">\n              {commandSuggestions.map((suggestion, index) => (\n                <motion.button\n                  key={suggestion.prefix}\n                  onClick={() => selectCommandSuggestion(index)}\n                  className=\"group bg-primary/5 text-muted-foreground hover:bg-primary/10 hover:text-foreground relative flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all\"\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                >\n                  {suggestion.icon}\n                  <span>{suggestion.label}</span>\n                  <motion.div\n                    className=\"border-border/50 absolute inset-0 rounded-lg border\"\n                    initial={false}\n                    animate={{\n                      opacity: [0, 1],\n                      scale: [0.98, 1],\n                    }}\n                    transition={{\n                      duration: 0.3,\n                      ease: 'easeOut',\n                    }}\n                  />\n                </motion.button>\n              ))}\n            </div>\n          </motion.div>\n        </div>\n\n        <AnimatePresence>\n          {isTyping && (\n            <motion.div\n              className=\"border-border bg-background/80 fixed bottom-8 mx-auto -translate-x-1/2 transform rounded-full border px-4 py-2 shadow-lg backdrop-blur-2xl\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: 20 }}\n            >\n              <div className=\"flex items-center gap-3\">\n                <div className=\"bg-primary/10 flex h-7 w-8 items-center justify-center rounded-full text-center\">\n                  <Sparkles className=\"text-primary h-4 w-4\" />\n                </div>\n                <div className=\"text-muted-foreground flex items-center gap-2 text-sm\">\n                  <span>Thinking</span>\n                  <TypingDots />\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n\n        {inputFocused && (\n          <motion.div\n            className=\"from-primary via-primary/80 to-secondary pointer-events-none fixed z-0 h-[50rem] w-[50rem] rounded-full bg-gradient-to-r opacity-[0.02] blur-[96px]\"\n            animate={{\n              x: mousePosition.x - 400,\n              y: mousePosition.y - 400,\n            }}\n            transition={{\n              type: 'spring',\n              damping: 25,\n              stiffness: 150,\n              mass: 0.5,\n            }}\n          />\n        )}\n      </div>\n    </div>\n  );\n}\n\nfunction TypingDots() {\n  return (\n    <div className=\"ml-1 flex items-center\">\n      {[1, 2, 3].map((dot) => (\n        <motion.div\n          key={dot}\n          className=\"bg-primary mx-0.5 h-1.5 w-1.5 rounded-full\"\n          initial={{ opacity: 0.3 }}\n          animate={{\n            opacity: [0.3, 0.9, 0.3],\n            scale: [0.85, 1.1, 0.85],\n          }}\n          transition={{\n            duration: 1.2,\n            repeat: Infinity,\n            delay: dot * 0.15,\n            ease: 'easeInOut',\n          }}\n          style={{\n            boxShadow: '0 0 4px rgba(255, 255, 255, 0.3)',\n          }}\n        />\n      ))}\n    </div>\n  );\n}\n\ninterface ActionButtonProps {\n  icon: React.ReactNode;\n  label: string;\n}\n\nconst rippleKeyframes = `\n@keyframes ripple {\n  0% { transform: scale(0.5); opacity: 0.6; }\n  100% { transform: scale(2); opacity: 0; }\n}\n`;\n\nif (typeof document !== 'undefined') {\n  const style = document.createElement('style');\n  style.innerHTML = rippleKeyframes;\n  document.head.appendChild(style);\n}\n", "path": "/components/mvpblocks/chatbot-ui/animated-ai-chat.tsx", "target": "components/mvpblocks/animated-ai-chat.tsx"}]}