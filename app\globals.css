@import "tailwindcss";
@import "fumadocs-ui/css/neutral.css";
@import "fumadocs-ui/css/preset.css";

::selection {
  background-color: hsl(var(--primary));
  color: #fff;
  text-shadow:
    0 0 1px #fff,
    0 0 1px #fff;
}

::-moz-selection {
  background-color: hsl(var(--primary));
  color: #fff;
  text-shadow:
    0 0 1px #fff,
    0 0 1px #fff;
}

@custom-variant dark (&:is(.dark *));

@theme inline {
  --animate-scroll: scroll var(--animation-duration, 40s) var(--animation-direction, forwards) linear infinite;

  @keyframes scroll {
    to {
      transform: translate(calc(-50% - 0.5rem));
    }
  }

  @keyframes marquee {
    from {
      transform: translateX(0);
    }

    to {
      transform: translateX(calc(-100% - var(--gap)));
    }
  }

  @keyframes marquee-vertical {
    from {
      transform: translateY(0);
    }

    to {
      transform: translateY(calc(-100% - var(--gap)));
    }
  }

  --animate-marquee: marquee var(--animation-duration, 10s) var(--animation-direction, forwards) linear infinite;
  --animate-marquee-vertical: marquee-vertical var(--animation-duration, 10s) var(--animation-direction, forwards) linear infinite;

  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: hsl(var(--sidebar-ring));
  --color-sidebar-border: hsl(var(--sidebar-border));
  --color-sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));
  --color-sidebar-accent: hsl(var(--sidebar-accent));
  --color-sidebar-primary-foreground: hsl(var(--sidebar-primary-foreground));
  --color-sidebar-primary: hsl(var(--sidebar-primary));
  --color-sidebar-foreground: hsl(var(--sidebar-foreground));
  --color-sidebar: hsl(var(--sidebar));
  --color-chart-5: hsl(var(--chart-5));
  --color-chart-4: hsl(var(--chart-4));
  --color-chart-3: hsl(var(--chart-3));
  --color-chart-2: hsl(var(--chart-2));
  --color-chart-1: hsl(var(--chart-1));
  --color-chart-6: hsl(var(--chart-6));
  --color-chart-7: hsl(var(--chart-7));
  --color-ring: hsl(var(--ring));
  --color-input: hsl(var(--input));
  --color-border: hsl(var(--border));
  --color-destructive: hsl(var(--destructive));
  --color-accent-foreground: hsl(var(--accent-foreground));
  --color-accent: hsl(var(--accent));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --color-muted: hsl(var(--muted));
  --color-secondary-foreground: hsl(var(--secondary-foreground));
  --color-secondary: hsl(var(--secondary));
  --color-primary-foreground: hsl(var(--primary-foreground));
  --color-primary: hsl(var(--primary));
  --color-popover-foreground: hsl(var(--popover-foreground));
  --color-popover: hsl(var(--popover));
  --color-card-foreground: hsl(var(--card-foreground));
  --color-card: hsl(var(--card));
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --color-fd-primary: hsl(var(--primary)) !important;
  --color-fd-primary-foreground: hsl(var(--primary-foreground)) !important;
  --color-fd-accent: hsl(var(--accent)) !important;
  --color-fd-accent-foreground: hsl(var(--accent-foreground)) !important;

  --color-fd-seconday: hsl(var(--secondary)) !important;
}

:root {
  --fd-page-width: 1400px;
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 240 10% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 240 10% 3.9%;
  --primary: 349 72.35% 52.76%;
  --primary-foreground: 355.7 100% 97.3%;
  --secondary: 240 4.8% 95.9%;
  --secondary-foreground: 240 5.9% 10%;
  --muted: 240 4.8% 95.9%;
  --muted-foreground: 240 3.8% 46.1%;
  --accent: 240 4.8% 95.9%;
  --accent-foreground: 240 5.9% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 240 5.9% 90%;
  --input: 240 5.9% 90%;
  --ring: 346.8 77.2% 49.8%;
  --radius: 0.5rem;

  /* Chart Colors */
  --chart-1: 347 77% 50%;
  --chart-2: 352 83% 91%;
  --chart-3: 350 80% 72%;
  --chart-4: 351 83% 82%;
  --chart-5: 349 77% 62%;
  --chart-6: 348 72% 52%;
  --chart-7: 347 77% 50%;
  --chart-8: 352 83% 91%;

  /* FD */
  --color-fd-primary: 349 72.35% 52.76%;
  --color-fd-primary-foreground: 355.7 100% 97.3%;

  /* Sidebar */
  --sidebar: 0 0% 98%;
  --sidebar-foreground: 240 5.3% 26.1%;
  --sidebar-primary: 240 5.9% 10%;
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 240 4.8% 95.9%;
  --sidebar-accent-foreground: 240 5.9% 10%;
  --sidebar-border: 220 13% 91%;
  --sidebar-ring: 217.2 91.2% 59.8%;
}

.logoimg {
  margin: 0 !important
}

.simplep {
  margin-bottom: 0 !important;
}

.dark {
  --fd-page-width: 1400px;
  --background: 20 14.3% 4.1%;
  --foreground: 0 0% 95%;
  --card: 24 9.8% 10%;
  --card-foreground: 0 0% 95%;
  --popover: 0 0% 9%;
  --popover-foreground: 0 0% 95%;
  --primary: 349 72.35% 52.76%;
  --primary-foreground: 355.7 100% 97.3%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 0 0% 15%;
  --muted-foreground: 240 5% 64.9%;
  --accent: 12 6.5% 15.1%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 85.7% 97.3%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --ring: 346.8 77.2% 49.8%;

  /* Chart Colors */
  --chart-1: 347 77% 50%;
  --chart-2: 352 83% 91%;
  --chart-3: 350 80% 72%;
  --chart-4: 351 83% 82%;
  --chart-5: 349 77% 62%;
  --chart-6: 348 72% 52%;
  --chart-7: 347 77% 50%;
  --chart-8: 352 83% 91%;

  /* FD */
  --color-fd-primary: 349 72.35% 52.76%;
  --color-fd-primary-foreground: 355.7 100% 97.3%;

  /* Sidebar */
  --sidebar: 240 5.9% 10%;
  --sidebar-foreground: 240 4.8% 95.9%;
  --sidebar-primary: 224.3 76.3% 48%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 240 3.7% 15.9%;
  --sidebar-accent-foreground: 240 4.8% 95.9%;
  --sidebar-border: 240 3.7% 15.9%;
  --sidebar-ring: 217.2 91.2% 59.8%;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

pre,
code {
  font-size: 16px !important;
  line-height: 1.6rem !important;
}

@keyframes tile {

  0%,
  12.5%,
  100% {
    opacity: 1;
  }

  25%,
  82.5% {
    opacity: 0;
  }
}

.animate-tile {
  animation: tile 2s ease-in-out infinite;
}

.bg-radial {
  --tw-gradient-position: in oklab;
  background-image: radial-gradient(var(--tw-gradient-stops));
}

.fade-left-lg {
  mask-image: linear-gradient(90deg, #0000 15%, #000);
}

.fade-top-lg {
  mask-image: linear-gradient(#0000 15%, #000);
}

.glass:where(.dark, .dark *) {
  display: flex;
  justify-content: center !important;
  align-items: center !important;
  border-radius: 16px !important;
  background: radial-gradient(circle at center,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(30, 0, 0, 0.1) 60%,
      #2a0e0e 100%) !important;

  border: 1px solid rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: blur(2px) !important;
  align-items: center !important;
  justify-content: center !important;
}

.glass:where(.dark, .dark *).rose {
  background: radial-gradient(circle at center,
      rgba(255, 200, 220, 0.08) 0%,
      rgba(80, 10, 40, 0.2) 40%,
      hsl(340, 30%, 10%) 100%) !important;
  box-shadow:
    inset 0 2px 4px rgba(255, 255, 255, 0.08),
    inset 0 -4px 6px rgba(0, 0, 0, 0.6),
    0 3px 8px rgba(230, 10, 100, 0.3),
    0 0 1px rgba(255, 100, 150, 0.15),
    0 0 8px rgba(255, 100, 150, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.04) !important;
  border-radius: 16px !important;
  backdrop-filter: blur(4px) saturate(130%) !important;
  transition: all 0.3s ease !important;
}

.glass:where(.dark, .dark *).rose::after {
  content: "" !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  width: 80px !important;
  height: 80px !important;
  background: radial-gradient(circle,
      rgba(181, 162, 168, 0.3) 0%,
      transparent 70%) !important;
  transform: translate(-50%, -50%) !important;
  border-radius: 50% !important;
  filter: blur(12px) !important;
  pointer-events: none !important;
  z-index: 0 !important;
}

.glass {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 16px;
  background: radial-gradient(circle at center,
      rgba(255, 255, 255, 0.6) 0%,
      rgba(255, 220, 230, 0.3) 60%,
      #f9f2f4 100%);
  border: 1px solid rgba(255, 150, 180, 0.1);
  backdrop-filter: blur(3px) saturate(180%);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.glass.rose {
  background: radial-gradient(circle at center,
      rgba(255, 220, 235, 0.3) 0%,
      rgba(255, 190, 210, 0.15) 40%,
      #fef4f7 100%);
  box-shadow:
    inset 0 2px 4px rgba(255, 200, 220, 0.2),
    inset 0 -4px 6px rgba(255, 180, 200, 0.3),
    0 3px 8px rgba(255, 150, 180, 0.2),
    0 0 8px rgba(255, 170, 200, 0.1);
  border: 1px solid rgba(255, 150, 180, 0.15);
  backdrop-filter: blur(5px) saturate(160%);
  position: relative;
  overflow: hidden;
}

.glass.rose::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80px;
  height: 80px;
  background: radial-gradient(circle,
      rgba(255, 160, 190, 0.3) 0%,
      transparent 70%);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  filter: blur(10px);
  pointer-events: none;
  z-index: 0;
}

.shiki .highlighted {
  background: hsl(var(--secondary)/50%);
}

.spin {
  animation: spin 5s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* NOOO */
/* Add glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sidebar badge styles */
.sidebar-badge {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  pointer-events: none;
}

/* Make sure badges don't overlap with text */
[data-sidebar="menu-button"] {
  position: relative;
  padding-right: 70px !important;
}

[data-radix-scroll-area-viewport]>div {
  display: block !important;
}

.glass2 {
  border-radius: 16px;
  background: radial-gradient(circle,
      #00000000 0%,
      #ff9e9e1a 60%,
      #ffb9d1 100%) !important;
  border: 1px solid #ffffff0d;
  backdrop-filter: blur(2px);
  justify-content: center;
  transition: all 0.3s;
}

.prose :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  word-break: break-all;
}

.fadein-blur {
  animation: fadein-blur 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s both;
  animation-delay: 1s;
}

@keyframes fadein-blur {
  0% {
    opacity: 0;
    filter: blur(16px);
  }

  100% {
    opacity: 1;
    filter: blur(0);
  }
}

.neumorphic-button::after {
  content: '';
  @apply absolute inset-0 opacity-0 transition-opacity duration-300 bg-gradient-to-br from-[#9b87f5]/20 to-transparent rounded-full;
}

.neumorphic-button:hover::after {
  @apply opacity-100;
}

/* Markdown CSS */
/* Headings */
.markdown-body h1 {
  font-size: 2.5rem;
  /* 40px */
}

.markdown-body h2 {
  font-size: 2rem;
  /* 32px */
}

.markdown-body h3 {
  font-size: 1.75rem;
  /* 28px */
}

.markdown-body h4 {
  font-size: 1.5rem;
  /* 24px */
}

.markdown-body h5 {
  font-size: 1.25rem;
  /* 20px */
}

.markdown-body h6 {
  font-size: 1rem;
  /* 16px */
}

/* Headings */
.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  font-weight: 600;
  line-height: 1.15;
  color: #fff0e8;
  margin-top: 15px;
  margin-bottom: 10px;
}

/* Paragraphs */
.markdown-body p {
  margin-bottom: 10px;
}

/* Links */
.markdown-body a {
  @apply text-rose-500;
  text-decoration: none;
}

.markdown-body a:hover {
  text-decoration: underline;
}

.markdown-body ul,
.markdown-body ol {
  padding-left: 20px;
}

.markdown-body ul li {
  list-style-type: disc;
}

.markdown-body blockquote {
  border-left: 4px solid #955d73;
  margin-left: 0;
  margin-right: 0;
  padding-left: 15px;
  color: #a33d73;
}

.markdown-body pre {
  background-color: #464646dc;
  border-left: 3px solid #e60a64;
  color: white;
  padding: 10px;
  overflow-x: auto;
}

.markdown-body code {
  padding: 2px 4px;
  font-size: 90%;
  border-radius: 3px;
}

/* Tables */
.markdown-body table {
  width: 100%;
  border-collapse: collapse;
}

.markdown-body th,
.markdown-body td {
  border: 1px solid hsl(0, 0%, 32%);
  padding: 8px;
  text-align: left;
}

.markdown-body th {
  background-color: #626161;
}

/* Animated minimalist hero */
@keyframes word-appear {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.8);
    filter: blur(10px);
  }

  50% {
    opacity: 0.8;
    transform: translateY(10px) scale(0.95);
    filter: blur(2px);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

@keyframes grid-draw {
  0% {
    stroke-dashoffset: 1000;
    opacity: 0;
  }

  50% {
    opacity: 0.3;
  }

  100% {
    stroke-dashoffset: 0;
    opacity: 0.15;
  }
}

@keyframes pulse-glow {

  0%,
  100% {
    opacity: 0.1;
    transform: scale(1);
  }

  50% {
    opacity: 0.3;
    transform: scale(1.1);
  }
}

.word {
  display: inline-block;
  opacity: 0;
  margin: 0 0.1em;
  transition: all 0.3s ease;
}

.word:hover {
  color: #c8b4a0;
  transform: translateY(-2px);
}

.grid-line {
  stroke: #c8b4a0;
  stroke-width: 0.5;
  opacity: 0;
  stroke-dasharray: 5 5;
  stroke-dashoffset: 1000;
  animation: grid-draw 2s ease-out forwards;
}

.detail-dot {
  fill: #c8b4a0;
  opacity: 0;
  animation: pulse-glow 3s ease-in-out infinite;
}

.corner-element {
  position: absolute;
  width: 40px;
  height: 40px;
  border: 1px solid rgba(200, 180, 160, 0.2);
  opacity: 0;
  animation: word-appear 1s ease-out forwards;
}

.corner-element::before {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  background: rgba(200, 180, 160, 0.3);
  border-radius: 50%;
}

.text-decoration {
  position: relative;
}

.text-decoration::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #c8b4a0, transparent);
  animation: underline-grow 2s ease-out forwards;
  animation-delay: 2s;
}

@keyframes underline-grow {
  to {
    width: 100%;
  }
}

.floating-element {
  position: absolute;
  width: 2px;
  height: 2px;
  background: #c8b4a0;
  border-radius: 50%;
  opacity: 0;
  animation: float 4s ease-in-out infinite;
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0) translateX(0);
    opacity: 0.2;
  }

  25% {
    transform: translateY(-10px) translateX(5px);
    opacity: 0.6;
  }

  50% {
    transform: translateY(-5px) translateX(-3px);
    opacity: 0.4;
  }

  75% {
    transform: translateY(-15px) translateX(7px);
    opacity: 0.8;
  }
}

.rose-gradient {
  background: #f8f9fa;
  background: radial-gradient(circle,
      rgba(248, 249, 250, 1) 0%,
      rgba(240, 242, 247, 1) 86%,
      rgba(246, 59, 118, 0.3) 100%);
}

.dark .rose-gradient {
  background: radial-gradient(circle,
      rgba(23, 23, 23, 0) 0%,
      rgba(20, 17, 10, 0.2) 86%,
      rgba(204, 27, 89, 0.52) 100%);
}

.golden-button {
  touch-action: manipulation;
  text-decoration: none;
  display: inline-block;
  outline: none;
  font-family: inherit;
  font-size: 1em;
  box-sizing: border-box;
  border: none;
  border-radius: 0.3em;
  height: 40px;
  line-height: 2.5em;
  text-transform: uppercase;
  padding: 0 1em;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(110, 80, 20, 0.4),
    inset 0 -2px 5px 1px rgba(139, 66, 8, 1),
    inset 0 -1px 1px 3px rgba(250, 227, 133, 1);
  background-image: linear-gradient(160deg,
      #a54e07,
      #b47e11,
      #fef1a2,
      #bc881b,
      #a54e07);
  border: 1px solid #a55d07;
  color: rgb(120, 50, 5);
  text-shadow: 0 2px 2px rgba(250, 227, 133, 1);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  background-size: 100% 100%;
  background-position: center;
}

.golden-button:focus,
.golden-button:hover {
  background-size: 150% 150%;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23),
    inset 0 -2px 5px 1px #b17d10, inset 0 -1px 1px 3px rgba(250, 227, 133, 1);
  border: 1px solid rgba(165, 93, 7, 0.6);
  color: rgba(120, 50, 5, 0.8);
}

/* Notebook Hero */
@keyframes moveGradientLeft {
  0% {
    background-position: 0% 0%;
  }

  100% {
    background-position: -200% 0%;
  }
}

.animate-gradient-x {
  animation: moveGradientLeft 20s linear infinite;
}