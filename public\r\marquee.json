{"name": "marquee", "type": "registry:ui", "dependencies": ["react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:ui", "content": "import { cn } from '@/lib/utils';\nimport { ComponentPropsWithoutRef } from 'react';\n\ninterface MarqueeProps extends ComponentPropsWithoutRef<'div'> {\n  /**\n   * Optional CSS class name to apply custom styles\n   */\n  className?: string;\n  /**\n   * Whether to reverse the animation direction\n   * @default false\n   */\n  reverse?: boolean;\n  /**\n   * Whether to pause the animation on hover\n   * @default false\n   */\n  pauseOnHover?: boolean;\n  /**\n   * Content to be displayed in the marquee\n   */\n  children: React.ReactNode;\n  /**\n   * Whether to animate vertically instead of horizontally\n   * @default false\n   */\n  vertical?: boolean;\n  /**\n   * Number of times to repeat the content\n   * @default 4\n   */\n  repeat?: number;\n}\n\nexport function Marquee({\n  className,\n  reverse = false,\n  pauseOnHover = false,\n  children,\n  vertical = false,\n  repeat = 4,\n  ...props\n}: MarqueeProps) {\n  return (\n    <div\n      {...props}\n      className={cn(\n        'group flex [gap:var(--gap)] overflow-hidden p-2 [--duration:40s] [--gap:1rem]',\n        {\n          'flex-row': !vertical,\n          'flex-col': vertical,\n        },\n        className,\n      )}\n    >\n      {Array(repeat)\n        .fill(0)\n        .map((_, i) => (\n          <div\n            key={i}\n            className={cn('flex shrink-0 justify-around [gap:var(--gap)]', {\n              'animate-marquee flex-row': !vertical,\n              'animate-marquee-vertical flex-col': vertical,\n              'group-hover:[animation-play-state:paused]': pauseOnHover,\n              '[animation-direction:reverse]': reverse,\n            })}\n          >\n            {children}\n          </div>\n        ))}\n    </div>\n  );\n}\n", "path": "/components/ui/marquee.tsx", "target": "components/ui/marquee.tsx"}], "tailwind": {"config": {"theme": {"extend": {"animation": {"marquee": "marquee var(--duration) linear infinite", "marquee-vertical": "marquee-vertical var(--duration) linear infinite"}, "keyframes": {"marquee": {"from": {"transform": "translateX(0)"}, "to": {"transform": "translateX(calc(-100% - var(--gap)))"}}, "marquee-vertical": {"from": {"transform": "translateY(0)"}, "to": {"transform": "translateY(calc(-100% - var(--gap)))"}}}}}}}}