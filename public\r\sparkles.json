{"name": "sparkles", "type": "registry:ui", "dependencies": ["@tsparticles/engine", "@tsparticles/react", "@tsparticles/slim", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:ui", "content": "'use client';\nimport React, { useId, useMemo } from 'react';\nimport { useEffect, useState } from 'react';\nimport Particles, { initParticlesEngine } from '@tsparticles/react';\nimport type { Container, SingleOrMultiple } from '@tsparticles/engine';\nimport { loadSlim } from '@tsparticles/slim';\nimport { cn } from '@/lib/utils';\nimport { motion, useAnimation } from 'motion/react';\n\ntype ParticlesProps = {\n  id?: string;\n  className?: string;\n  background?: string;\n  particleSize?: number;\n  minSize?: number;\n  maxSize?: number;\n  speed?: number;\n  particleColor?: string;\n  particleDensity?: number;\n};\nexport const SparklesCore = (props: ParticlesProps) => {\n  const {\n    id,\n    className,\n    background,\n    minSize,\n    maxSize,\n    speed,\n    particleColor,\n    particleDensity,\n  } = props;\n  const [init, setInit] = useState(false);\n  useEffect(() => {\n    initParticlesEngine(async (engine) => {\n      await loadSlim(engine);\n    }).then(() => {\n      setInit(true);\n    });\n  }, []);\n  const controls = useAnimation();\n\n  const particlesLoaded = async (container?: Container) => {\n    if (container) {\n      controls.start({\n        opacity: 1,\n        transition: {\n          duration: 1,\n        },\n      });\n    }\n  };\n\n  const generatedId = useId();\n  return (\n    <motion.div animate={controls} className={cn('opacity-0', className)}>\n      {init && (\n        <Particles\n          id={id || generatedId}\n          className={cn('h-full w-full')}\n          particlesLoaded={particlesLoaded}\n          options={{\n            background: {\n              color: {\n                value: background || '#0d47a1',\n              },\n            },\n            fullScreen: {\n              enable: false,\n              zIndex: 1,\n            },\n\n            fpsLimit: 120,\n            interactivity: {\n              events: {\n                onClick: {\n                  enable: true,\n                  mode: 'push',\n                },\n                onHover: {\n                  enable: false,\n                  mode: 'repulse',\n                },\n                resize: true as any,\n              },\n              modes: {\n                push: {\n                  quantity: 4,\n                },\n                repulse: {\n                  distance: 200,\n                  duration: 0.4,\n                },\n              },\n            },\n            particles: {\n              bounce: {\n                horizontal: {\n                  value: 1,\n                },\n                vertical: {\n                  value: 1,\n                },\n              },\n              collisions: {\n                absorb: {\n                  speed: 2,\n                },\n                bounce: {\n                  horizontal: {\n                    value: 1,\n                  },\n                  vertical: {\n                    value: 1,\n                  },\n                },\n                enable: false,\n                maxSpeed: 50,\n                mode: 'bounce',\n                overlap: {\n                  enable: true,\n                  retries: 0,\n                },\n              },\n              color: {\n                value: particleColor || '#ffffff',\n                animation: {\n                  h: {\n                    count: 0,\n                    enable: false,\n                    speed: 1,\n                    decay: 0,\n                    delay: 0,\n                    sync: true,\n                    offset: 0,\n                  },\n                  s: {\n                    count: 0,\n                    enable: false,\n                    speed: 1,\n                    decay: 0,\n                    delay: 0,\n                    sync: true,\n                    offset: 0,\n                  },\n                  l: {\n                    count: 0,\n                    enable: false,\n                    speed: 1,\n                    decay: 0,\n                    delay: 0,\n                    sync: true,\n                    offset: 0,\n                  },\n                },\n              },\n              effect: {\n                close: true,\n                fill: true,\n                options: {},\n                type: {} as SingleOrMultiple<string> | undefined,\n              },\n              groups: {},\n              move: {\n                angle: {\n                  offset: 0,\n                  value: 90,\n                },\n                attract: {\n                  distance: 200,\n                  enable: false,\n                  rotate: {\n                    x: 3000,\n                    y: 3000,\n                  },\n                },\n                center: {\n                  x: 50,\n                  y: 50,\n                  mode: 'percent',\n                  radius: 0,\n                },\n                decay: 0,\n                distance: {},\n                direction: 'none',\n                drift: 0,\n                enable: true,\n                gravity: {\n                  acceleration: 9.81,\n                  enable: false,\n                  inverse: false,\n                  maxSpeed: 50,\n                },\n                path: {\n                  clamp: true,\n                  delay: {\n                    value: 0,\n                  },\n                  enable: false,\n                  options: {},\n                },\n                outModes: {\n                  default: 'out',\n                },\n                random: false,\n                size: false,\n                speed: {\n                  min: 0.1,\n                  max: 1,\n                },\n                spin: {\n                  acceleration: 0,\n                  enable: false,\n                },\n                straight: false,\n                trail: {\n                  enable: false,\n                  length: 10,\n                  fill: {},\n                },\n                vibrate: false,\n                warp: false,\n              },\n              number: {\n                density: {\n                  enable: true,\n                  width: 400,\n                  height: 400,\n                },\n                limit: {\n                  mode: 'delete',\n                  value: 0,\n                },\n                value: particleDensity || 120,\n              },\n              opacity: {\n                value: {\n                  min: 0.1,\n                  max: 1,\n                },\n                animation: {\n                  count: 0,\n                  enable: true,\n                  speed: speed || 4,\n                  decay: 0,\n                  delay: 0,\n                  sync: false,\n                  mode: 'auto',\n                  startValue: 'random',\n                  destroy: 'none',\n                },\n              },\n              reduceDuplicates: false,\n              shadow: {\n                blur: 0,\n                color: {\n                  value: '#000',\n                },\n                enable: false,\n                offset: {\n                  x: 0,\n                  y: 0,\n                },\n              },\n              shape: {\n                close: true,\n                fill: true,\n                options: {},\n                type: 'circle',\n              },\n              size: {\n                value: {\n                  min: minSize || 1,\n                  max: maxSize || 3,\n                },\n                animation: {\n                  count: 0,\n                  enable: false,\n                  speed: 5,\n                  decay: 0,\n                  delay: 0,\n                  sync: false,\n                  mode: 'auto',\n                  startValue: 'random',\n                  destroy: 'none',\n                },\n              },\n              stroke: {\n                width: 0,\n              },\n              zIndex: {\n                value: 0,\n                opacityRate: 1,\n                sizeRate: 1,\n                velocityRate: 1,\n              },\n              destroy: {\n                bounds: {},\n                mode: 'none',\n                split: {\n                  count: 1,\n                  factor: {\n                    value: 3,\n                  },\n                  rate: {\n                    value: {\n                      min: 4,\n                      max: 9,\n                    },\n                  },\n                  sizeOffset: true,\n                },\n              },\n              roll: {\n                darken: {\n                  enable: false,\n                  value: 0,\n                },\n                enable: false,\n                enlighten: {\n                  enable: false,\n                  value: 0,\n                },\n                mode: 'vertical',\n                speed: 25,\n              },\n              tilt: {\n                value: 0,\n                animation: {\n                  enable: false,\n                  speed: 0,\n                  decay: 0,\n                  sync: false,\n                },\n                direction: 'clockwise',\n                enable: false,\n              },\n              twinkle: {\n                lines: {\n                  enable: false,\n                  frequency: 0.05,\n                  opacity: 1,\n                },\n                particles: {\n                  enable: false,\n                  frequency: 0.05,\n                  opacity: 1,\n                },\n              },\n              wobble: {\n                distance: 5,\n                enable: false,\n                speed: {\n                  angle: 50,\n                  move: 10,\n                },\n              },\n              life: {\n                count: 0,\n                delay: {\n                  value: 0,\n                  sync: false,\n                },\n                duration: {\n                  value: 0,\n                  sync: false,\n                },\n              },\n              rotate: {\n                value: 0,\n                animation: {\n                  enable: false,\n                  speed: 0,\n                  decay: 0,\n                  sync: false,\n                },\n                direction: 'clockwise',\n                path: false,\n              },\n              orbit: {\n                animation: {\n                  count: 0,\n                  enable: false,\n                  speed: 1,\n                  decay: 0,\n                  delay: 0,\n                  sync: false,\n                },\n                enable: false,\n                opacity: 1,\n                rotation: {\n                  value: 45,\n                },\n                width: 1,\n              },\n              links: {\n                blink: false,\n                color: {\n                  value: '#fff',\n                },\n                consent: false,\n                distance: 100,\n                enable: false,\n                frequency: 1,\n                opacity: 1,\n                shadow: {\n                  blur: 5,\n                  color: {\n                    value: '#000',\n                  },\n                  enable: false,\n                },\n                triangles: {\n                  enable: false,\n                  frequency: 1,\n                },\n                width: 1,\n                warp: false,\n              },\n              repulse: {\n                value: 0,\n                enabled: false,\n                distance: 1,\n                duration: 1,\n                factor: 1,\n                speed: 1,\n              },\n            },\n            detectRetina: true,\n          }}\n        />\n      )}\n    </motion.div>\n  );\n};\n", "path": "/components/ui/sparkles.tsx", "target": "components/ui/sparkles.tsx"}]}