{"name": "breadcrumb", "type": "registry:ui", "dependencies": ["@radix-ui/react-slot"], "files": [{"type": "registry:ui", "content": "import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { ChevronRight, MoreHorizontal } from 'lucide-react';\n\nimport { cn } from '@/lib/utils';\n\nconst Breadcrumb = React.forwardRef<\n  HTMLElement,\n  React.ComponentPropsWithoutRef<'nav'> & {\n    separator?: React.ReactNode;\n  }\n>(({ ...props }, ref) => <nav ref={ref} aria-label=\"breadcrumb\" {...props} />);\nBreadcrumb.displayName = 'Breadcrumb';\n\nconst BreadcrumbList = React.forwardRef<\n  HTMLOListElement,\n  React.ComponentPropsWithoutRef<'ol'>\n>(({ className, ...props }, ref) => (\n  <ol\n    ref={ref}\n    className={cn(\n      'text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5',\n      className,\n    )}\n    {...props}\n  />\n));\nBreadcrumbList.displayName = 'BreadcrumbList';\n\nconst BreadcrumbItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentPropsWithoutRef<'li'>\n>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    className={cn('inline-flex items-center gap-1.5', className)}\n    {...props}\n  />\n));\nBreadcrumbItem.displayName = 'BreadcrumbItem';\n\nconst BreadcrumbLink = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentPropsWithoutRef<'a'> & {\n    asChild?: boolean;\n  }\n>(({ asChild, className, ...props }, ref) => {\n  const Comp = asChild ? Slot : 'a';\n\n  return (\n    <Comp\n      ref={ref}\n      className={cn('hover:text-foreground transition-colors', className)}\n      {...props}\n    />\n  );\n});\nBreadcrumbLink.displayName = 'BreadcrumbLink';\n\nconst BreadcrumbPage = React.forwardRef<\n  HTMLSpanElement,\n  React.ComponentPropsWithoutRef<'span'>\n>(({ className, ...props }, ref) => (\n  <span\n    ref={ref}\n    role=\"link\"\n    aria-disabled=\"true\"\n    aria-current=\"page\"\n    className={cn('text-foreground font-normal', className)}\n    {...props}\n  />\n));\nBreadcrumbPage.displayName = 'BreadcrumbPage';\n\nconst BreadcrumbSeparator = ({\n  children,\n  className,\n  ...props\n}: React.ComponentProps<'li'>) => (\n  <li\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn('[&>svg]:h-3.5 [&>svg]:w-3.5', className)}\n    {...props}\n  >\n    {children ?? <ChevronRight />}\n  </li>\n);\nBreadcrumbSeparator.displayName = 'BreadcrumbSeparator';\n\nconst BreadcrumbEllipsis = ({\n  className,\n  ...props\n}: React.ComponentProps<'span'>) => (\n  <span\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn('flex h-9 w-9 items-center justify-center', className)}\n    {...props}\n  >\n    <MoreHorizontal className=\"h-4 w-4\" />\n    <span className=\"sr-only\">More</span>\n  </span>\n);\nBreadcrumbEllipsis.displayName = 'BreadcrumbElipssis';\n\nexport {\n  Breadcrumb,\n  BreadcrumbList,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n  BreadcrumbEllipsis,\n};\n", "path": "/components/ui/breadcrumb.tsx", "target": "components/ui/breadcrumb.tsx"}]}