---
title: Installation
description: How to install and set up Mvpblocks in your project.
icon: Package
---

import { DocsInstallationCard } from '@/components/DocInstall.tsx';

<div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
  <DocsInstallationCard
    title="Next.js"
    light="/nextjs_icon_dark.svg"
    dark="/nextjs_icon_dark.svg"
    url="/docs/installation/nextjs"
  />
  <DocsInstallationCard
    title="Vite"
    light="/vitejs.svg"
    dark="/vitejs.svg"
    url="/docs/installation/vite"
  />
  <DocsInstallationCard
    title="Laravel"
    light="/laravel.svg"
    dark="/laravel.svg"
    url="/docs/installation/laravel"
  />
  <DocsInstallationCard
    title="React Router"
    light="/reactrouter.svg"
    dark="/reactrouter.svg"
    url="/docs/installation/react-router"
  />
  <DocsInstallationCard
    title="Remix"
    light="/remix_light.svg"
    dark="/remix_dark.svg"
    url="/docs/installation/remix"
  />
  <DocsInstallationCard
    title="Astro"
    light="/astro.svg"
    dark="/astro_dark.svg"
    url="/docs/installation/astro"
  />
  <DocsInstallationCard
    title="Gatsby"
    light="/gatsby.svg"
    dark="/gatsby.svg"
    url="/docs/installation/gatsby"
  />
  <DocsInstallationCard
    title="TanStack"
    light="/tanstack.svg"
    dark="/tanstack.svg"
    url="/docs/installation/tanstack"
  />
  <DocsInstallationCard
    title="TanStack Router"
    light="/tanstack.svg"
    dark="/tanstack.svg"
    url="/docs/installation/tanstack-router"
  />
  <DocsInstallationCard
    title="Manual Installation"
    light="/react_light.svg"
    dark="/react_dark.svg"
    url="/docs/installation/manual"
  />
</div>
