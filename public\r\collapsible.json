{"name": "collapsible", "type": "registry:ui", "dependencies": ["@radix-ui/react-collapsible"], "files": [{"type": "registry:ui", "content": "'use client';\n\nimport * as CollapsiblePrimitive from '@radix-ui/react-collapsible';\n\nconst Collapsible = CollapsiblePrimitive.Root;\n\nconst CollapsibleTrigger = CollapsiblePrimitive.CollapsibleTrigger;\n\nconst CollapsibleContent = CollapsiblePrimitive.CollapsibleContent;\n\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent };\n", "path": "/components/ui/collapsible.tsx", "target": "components/ui/collapsible.tsx"}]}