{"name": "interactive-tooltip", "author": "nuelst", "type": "registry:block", "dependencies": ["motion/react", "react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\nimport {\n  AnimatePresence,\n  motion,\n  useMotionValue,\n  useSpring,\n  useTransform,\n} from 'motion/react';\nimport { useState } from 'react';\n\nconst defaultItems = [\n  {\n    id: 1,\n    name: '<PERSON>',\n    designation: 'Lead Developer',\n    image:\n      'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',\n  },\n  {\n    id: 2,\n    name: '<PERSON><PERSON>',\n    designation: 'UI/UX Designer',\n    image:\n      'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face',\n  },\n  {\n    id: 3,\n    name: '<PERSON>',\n    designation: 'Product Manager',\n    image:\n      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',\n  },\n  {\n    id: 4,\n    name: '<PERSON>',\n    designation: 'Marketing Lead',\n    image:\n      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',\n  },\n];\n\nexport default function InteractiveTooltip({\n  items = defaultItems,\n}: {\n  items?: {\n    id: number;\n    name: string;\n    designation: string;\n    image: string;\n  }[];\n}) {\n  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);\n  const springConfig = { stiffness: 100, damping: 5 };\n  const x = useMotionValue(0); // going to set this value on mouse move\n  // rotate the tooltip\n  const rotate = useSpring(\n    useTransform(x, [-100, 100], [-45, 45]),\n    springConfig,\n  );\n  // translate the tooltip\n  const translateX = useSpring(\n    useTransform(x, [-100, 100], [-50, 50]),\n    springConfig,\n  );\n  const handleMouseMove = (event: any) => {\n    const halfWidth = event.target.offsetWidth / 2;\n    x.set(event.nativeEvent.offsetX - halfWidth); // set the x value, which is then used in transform and rotate\n  };\n\n  return (\n    <>\n      {items.map((item, idx) => (\n        <div\n          className=\"group relative -mr-4\"\n          key={item.name}\n          onMouseEnter={() => setHoveredIndex(item.id)}\n          onMouseLeave={() => setHoveredIndex(null)}\n        >\n          <AnimatePresence mode=\"popLayout\">\n            {hoveredIndex === item.id && (\n              <motion.div\n                initial={{ opacity: 0, y: 20, scale: 0.6 }}\n                animate={{\n                  opacity: 1,\n                  y: 0,\n                  scale: 1,\n                  transition: {\n                    type: 'spring',\n                    stiffness: 260,\n                    damping: 10,\n                  },\n                }}\n                exit={{ opacity: 0, y: 20, scale: 0.6 }}\n                style={{\n                  translateX: translateX,\n                  rotate: rotate,\n                  whiteSpace: 'nowrap',\n                }}\n                className=\"bg-background/95 border-border absolute -top-16 left-1/2 z-50 flex -translate-x-1/2 flex-col items-center justify-center rounded-lg border px-4 py-2 text-xs shadow-xl backdrop-blur-sm\"\n              >\n                <div className=\"from-primary/5 to-primary/5 absolute inset-0 rounded-lg bg-gradient-to-b via-transparent\" />\n                <div className=\"via-primary absolute inset-x-10 -bottom-px z-30 h-px w-[20%] bg-gradient-to-r from-transparent to-transparent\" />\n                <div className=\"via-primary/60 absolute -bottom-px left-10 z-30 h-px w-[40%] bg-gradient-to-r from-transparent to-transparent\" />\n                <div className=\"text-foreground relative z-30 text-base font-bold\">\n                  {item.name}\n                </div>\n                <div className=\"text-muted-foreground text-xs\">\n                  {item.designation}\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n          <img\n            onMouseMove={handleMouseMove}\n            height={100}\n            width={100}\n            src={item.image}\n            alt={item.name}\n            className=\"border-background ring-primary/20 group-hover:ring-primary/40 relative !m-0 h-14 w-14 rounded-full border-2 object-cover object-top !p-0 ring-2 transition duration-500 group-hover:z-30 group-hover:scale-105\"\n          />\n        </div>\n      ))}\n    </>\n  );\n}\n", "path": "/components/mvpblocks/creative/interactive-tooltip.tsx", "target": "components/mvpblocks/interactive-tooltip.tsx"}]}