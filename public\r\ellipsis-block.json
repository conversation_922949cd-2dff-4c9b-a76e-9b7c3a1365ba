{"name": "ellipsis-block", "type": "registry:block", "dependencies": ["react", "lucide-react", "framer-motion"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { motion } from 'framer-motion';\nimport { Clipboard, ClipboardCheck } from 'lucide-react';\nimport { useEffect, useState } from 'react';\n\nexport default function EllipsisBlock() {\n  const [status, setStatus] = useState<'idle' | 'copied'>('idle');\n\n  useEffect(() => {\n    if (status !== 'idle') {\n      const timer = setTimeout(() => setStatus('idle'), 1500);\n      return () => clearTimeout(timer);\n    }\n  }, [status]);\n\n  const handleClick = () => {\n    setStatus('copied');\n  };\n\n  const icons = {\n    idle: <Clipboard strokeWidth={2.5} size={16} />,\n    copied: (\n      <ClipboardCheck\n        strokeWidth={2.5}\n        className=\"stroke-green-500 stroke-2\"\n        size={16}\n      />\n    ),\n  };\n\n  return (\n    <div className=\"relative w-full max-w-2xl rounded-xl p-0.5\">\n      <div className=\"rounded-xl p-4\">\n        <div className=\"flex items-center justify-between rounded-t-xl border-b border-gray-700 bg-neutral-900 px-4 py-2\">\n          <div className=\"flex items-center justify-center gap-2\">\n            <span className=\"size-3 rounded-full bg-[#FF5F56]\" />\n            <span className=\"size-3 rounded-full bg-[#FFBD2E]\" />\n            <span className=\"size-3 rounded-full bg-[#27C93F]\" />\n          </div>\n\n          <p className=\"text-sm font-medium text-gray-400\">app.tsx</p>\n          <button\n            aria-label=\"Copy\"\n            onClick={handleClick}\n            className=\"rounded-xl bg-gray-800 p-2 text-gray-100 hover:bg-gray-700 focus:outline-none\"\n          >\n            <motion.div\n              key={status}\n              initial={{ opacity: 0, scale: 0.5 }}\n              animate={{ opacity: 1, scale: 1 }}\n              exit={{ opacity: 0, scale: 0.5 }}\n              transition={{ type: 'spring', stiffness: 500, damping: 30 }}\n            >\n              {icons[status]}\n            </motion.div>\n          </button>\n        </div>\n        <pre className=\"overflow-x-auto rounded-b-xl bg-stone-800 p-4 text-sm text-blue-100\">\n          <code>\n            <span style={{ color: '#80b6f7' }}>import</span>{' '}\n            <span style={{ color: '#ffffff' }}>{'{'}</span>\n            <span style={{ color: '#8be9fd' }}>useState</span>\n            <span style={{ color: '#ffffff' }}>{'}'}</span>{' '}\n            <span style={{ color: '#80b6f7' }}>from</span>{' '}\n            <span style={{ color: '#50fa7b' }}>&quot;react&quot;</span>;\n            <br />\n            <br />\n            <span style={{ color: '#80b6f7' }}>function</span>{' '}\n            <span style={{ color: '#ff79c6' }}>Counter</span>() {'{'}\n            <br />\n            &nbsp;&nbsp;\n            <span style={{ color: '#80b6f7' }}>const</span>{' '}\n            <span style={{ color: '#8be9fd' }}>[count, setCount]</span> ={' '}\n            <span style={{ color: '#ff79c6' }}>useState</span>(\n            <span style={{ color: '#50fa7b' }}>0</span>);\n            <br />\n            <br />\n            &nbsp;&nbsp;\n            <span style={{ color: '#80b6f7' }}>return</span> (\n            <br />\n            &nbsp;&nbsp;&nbsp;&nbsp;\n            <span style={{ color: '#50fa7b' }}>&lt;button</span>{' '}\n            <span style={{ color: '#8be9fd' }}>onClick</span>=\n            <span style={{ color: '#bd93f9' }}>\n              {'{() => setCount(count + 1)}'}\n            </span>\n            <span style={{ color: '#50fa7b' }}>&gt;</span>\n            <br />\n            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Clicked {'{count}'} times\n            <br />\n            &nbsp;&nbsp;&nbsp;&nbsp;\n            <span style={{ color: '#50fa7b' }}>&lt;/button&gt;</span>\n            <br />\n            &nbsp;&nbsp;);\n            <br />\n            {'}'}\n          </code>\n        </pre>\n      </div>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/cards/code/ellipsis-block.tsx", "target": "components/mvpblocks/ellipsis-block.tsx"}]}