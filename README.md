# 🧱 MVPBlocks

**The Ultimate Open Source Component Library for MVPs.**  
Copy, paste, customize—and launch your idea faster than ever.

![MVPBlocks banner](./app/opengraph-image.png)

## ⚡ What is MVPBlocks?

MVPBlocks is a fully open-source, developer-first component library built using **Next Js** and **TailwindCSS**, designed to help you launch your MVPs in record time. No bloated packages, no unnecessary installs—just clean, copyable code to plug right into your next big thing.

Whether you're building a SaaS dashboard, a landing page, or a personal portfolio—MVPBlocks offers a curated set of reusable blocks designed to work beautifully right out of the box.

## 💎 What We Provide

We don’t just give you UI blocks—we give you **freedom to build without friction**. Here's what you get with MVPBlocks:

- 🧑‍💻 **Developer-Friendly**  
  Tailored for developers to create and iterate fast, with minimal overhead and maximum flexibility.

- 🔧 **CLI Support**  
  Seamlessly integrate MVPBlocks into your workflow using our smart CLI support, optimized for speed and efficiency.

- 🎨 **Easily Customisable**  
  Every block is built to be editable. From layout to logic, style to structure—make it your own.

- 🚀 **v0 Support**  
  Even at version zero, you can launch fast with confidence. Ideal for MVPs, prototypes, and weekend hacks.

- 📚 **Fully Functional Docs Understanding**  
  Comprehensive documentation and inline guides help you quickly understand how to use, modify, or contribute components.

- 🖥️ **Multi Viewport**  
  Preview and copy blocks optimized for every screen size—from mobile to ultra-wide monitors.

- 🧩 **Easy-to-Use Interface**  
  Our web UI is intuitive, clean, and minimal. Preview, click, copy. That’s it.

- 💥 **Add Yours!**  
  Add your own blocks to the ecosystem. No setup required. Just submit a PR and let the world use your work.

## ✨ Want to Contribute?

We LOVE contributions. And we believe in equality—if you contribute a block, **you’ll be credited** as the author, always.

- No need for perfect formatting—we’ll clean it up.
- You can even submit raw component files.
- Mobile responsiveness and TailwindCSS are all we ask.
- Submitting a block? Follow this guide:

🔗 [Add Your Own Block →](https://blocks.mvp-subha.me/docs/add-a-block)

## 🌐 Explore MVPBlocks

Head over to [**blocks.mvp-subha.me**](https://blocks.mvp-subha.me) to:

- 🔍 Browse through 100+ unique blocks
- 🎯 Filter by category (Auth, Dashboard, Hero, Pricing, and more)
- 📋 Copy with a single click
- 📘 Read full documentation
- 🚀 Contribute your own

## 💬 Join the Community

Whether you have questions, ideas, or just wanna hang out—come join us!

- 🐙 [GitHub Discussions](https://github.com/subhadeeproy3902/mvpblocks/discussions)
- 📥 [Submit a Pull Request](https://github.com/subhadeeproy3902/mvpblocks/pulls)
- 🚨 [Report an Issue](https://github.com/subhadeeproy3902/mvpblocks/issues)

## 📜 Terms and Conditions

- You can freely use, modify, and copy blocks from MVPBlocks.
- If you contribute a block, you retain authorship. We believe in **equality and attribution**.
- Please don’t use MVPBlocks content for piracy or unethical purposes.
- No need to contact us for using blocks—just give credit if possible.
- Refer to our full [Terms & Conditions](https://blocks.mvp-subha.me/terms) page for more.

## 🛡️ License

MVPBlocks is released under the [BSD 3-Clause License](./LICENSE).  
Use it commercially, personally, and freely. Just don’t resell components as-is.

## 🌟 Open Source With ❤️

MVPBlocks is proudly open source and built by passionate developers.  
If you find it helpful:

- ⭐ **Star** the repo to show your support  
- 🛠 **Contribute** a block, idea, or fix  
- 🐞 **Raise an issue** if something’s broken

Together, let’s build a better internet—one block at a time.

## 💪 Thanks to all Contributors

Thanks to all contributors for helping this project grow! 🍻

<img src="https://contrib.rocks/image?repo=subhadeeproy3902/mvpblocks" width=390 />

---

Made with ❤️ by [Subhadeep Roy](x.com/mvp_Subha)
