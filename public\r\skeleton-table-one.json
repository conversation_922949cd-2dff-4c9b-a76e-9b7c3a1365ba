{"name": "skeleton-table-one", "author": "midhunkalarikkal", "type": "registry:block", "dependencies": [], "registryDependencies": [], "files": [{"type": "registry:block", "content": "export interface SkeletonTableProps {\n  rowCount?: number;\n  columnCount?: number;\n  showTopBar?: boolean;\n  showFilter?: boolean;\n  showColumnToggle?: boolean;\n  bodyClassName?: string;\n  showTableHeading?: boolean;\n  tableHeadings?: string[];\n  columnWidthArray?: string[];\n}\n\ninterface ShimmerComponentProps {\n  className?: string;\n}\n\ninterface ShimmerTableProps {\n  rowCount?: number;\n  columnCount?: number;\n  renderHeading?: React.ReactNode;\n  columnWidthArray?: string[];\n}\n\nexport const ShimmerComponent: React.FC<ShimmerComponentProps> = ({\n  className = '',\n}) => {\n  return <div className={`animate-pulse rounded bg-gray-500 ${className}`} />;\n};\n\nexport const ErrorMessage: React.FC<{ message: string }> = ({ message }) => {\n  return <h3 className=\"text-center text-red-400\">{message}</h3>;\n};\n\nexport const ShimmerTable: React.FC<ShimmerTableProps> = ({\n  rowCount = 5,\n  columnCount = 5,\n  renderHeading,\n  columnWidthArray,\n}) => {\n  if (columnWidthArray && columnCount !== columnWidthArray.length) {\n    return (\n      <ErrorMessage\n        message={\n          'Please ensure that columnCount and columnWidthArray length is equal'\n        }\n      />\n    );\n  }\n\n  return (\n    <div className=\"w-full overflow-x-auto\">\n      <div className=\"flex min-w-full flex-col overflow-hidden rounded-md border-2\">\n        {renderHeading}\n        {Array.from({ length: rowCount }).map((_, rowIdx) => (\n          <div\n            key={rowIdx}\n            className={`mb-1 flex h-10 items-center ${\n              rowCount - rowIdx === 1 ? 'border-b-0' : 'border-b-2'\n            }`}\n          >\n            {Array.from({ length: columnCount }).map((_, colIdx) => (\n              <div\n                key={colIdx}\n                className={`flex h-full items-center ${columnWidthArray ? columnWidthArray[colIdx] : 'w-full'} ${colIdx !== columnCount - 1 && 'border-r-2'}`}\n              >\n                <ShimmerComponent key={colIdx} className={`mx-2 h-3 w-full`} />\n              </div>\n            ))}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nconst ShimmerTopBar: React.FC<{\n  showFilter: boolean;\n  showColumnToggle: boolean;\n}> = ({ showFilter, showColumnToggle }) => (\n  <div className=\"mb-2 flex w-full flex-col overflow-hidden rounded-md\">\n    <div className=\"flex h-15 items-center justify-between gap-4\">\n      {showFilter && (\n        <ShimmerComponent className=\"h-6 w-6/12 md:h-8 lg:w-3/12\" />\n      )}\n      {showColumnToggle && (\n        <ShimmerComponent className=\"ml-auto h-6 w-2/12 md:h-8 lg:w-1/12\" />\n      )}\n    </div>\n  </div>\n);\n\nexport default function SkeletonTableOneWrapper({\n  rowCount = 5,\n  columnCount = 5,\n  showTopBar = true,\n  showFilter = true,\n  showColumnToggle = true,\n  bodyClassName = 'px-10',\n  columnWidthArray,\n}: SkeletonTableProps) {\n  return (\n    <div className={`w-full ${bodyClassName}`}>\n      {showTopBar && (\n        <ShimmerTopBar\n          showFilter={showFilter}\n          showColumnToggle={showColumnToggle}\n        />\n      )}\n      <ShimmerTable\n        rowCount={rowCount}\n        columnCount={columnCount}\n        columnWidthArray={columnWidthArray}\n      />\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/shimmers/skeleton-table-1.tsx", "target": "components/mvpblocks/skeleton-table-1.tsx"}]}