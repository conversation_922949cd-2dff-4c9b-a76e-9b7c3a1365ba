{"name": "team-6", "author": "mosespace", "type": "registry:block", "dependencies": ["lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { cn } from '@/lib/utils';\nimport { Poppins } from 'next/font/google';\n\nconst mont = Poppins({\n  subsets: ['latin'],\n  weight: ['400', '500', '600', '700'],\n});\n\n// Team member data type\ntype TeamMember = {\n  id: number;\n  name: string;\n  role: string;\n  image: string;\n  troubleMaker?: boolean;\n};\n\ntype TeamSectionProps = {\n  title?: string;\n  subtitle?: string;\n  teamMembers: TeamMember[];\n  backgroundColor?: string;\n  textColor?: string;\n  secondaryColor?: string;\n  className?: string;\n};\n\nexport interface CreativeTeamProps extends TeamSectionProps {\n  gradientColor?: string;\n  accentColor?: string;\n  verticalText?: {\n    left: string;\n    right: string;\n  };\n}\n\nconst teamMembers3D: TeamMember[] = [\n  {\n    id: 1,\n    name: '<PERSON><PERSON>',\n    role: 'Chief Executive Officer',\n\n    image:\n      'https://img.freepik.com/free-psd/3d-rendering-avatar_23-2150833554.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 2,\n    name: '<PERSON>',\n    role: 'Chief Technology Officer',\n\n    image:\n      'https://img.freepik.com/premium-photo/png-headset-headphones-portrait-cartoon_53876-762197.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 3,\n    name: 'Zainab Rahman',\n    role: 'Chief Operations Officer',\n\n    image:\n      'https://img.freepik.com/premium-photo/png-cartoon-portrait-glasses-white-background_53876-905385.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 4,\n    name: 'Aiden Davis',\n    role: 'Chief Marketing Officer',\n\n    image:\n      'https://img.freepik.com/premium-psd/3d-avatar-character_975163-690.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 5,\n    name: 'Aysha Hussain',\n    role: 'UX Designer',\n\n    image:\n      'https://img.freepik.com/free-photo/fun-3d-illustration-american-referee_183364-81231.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 6,\n    name: 'Samira Shah',\n    role: 'Product Manager',\n    image:\n      'https://img.freepik.com/premium-psd/lego-character-with-blue-button-his-chest_1217673-223400.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 7,\n    name: 'Ethan Williams',\n    role: 'Backend Developer',\n    image:\n      'https://img.freepik.com/premium-photo/there-is-black-girl-with-headphones-yellow-jacket_1034474-106535.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 8,\n    name: 'Amina Khan',\n    role: 'Frontend Developer',\n    image:\n      'https://img.freepik.com/free-photo/portrait-young-student-with-book-education-day_23-2150980030.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n];\n\nexport default function Team6({\n  title = 'Meet our team!',\n  subtitle,\n  teamMembers = teamMembers3D,\n  backgroundColor = '#111111',\n  textColor = '#ffffff',\n  accentColor = '#ef4444',\n  secondaryColor = '#ffffff',\n  className,\n  gradientColor = 'from-rose-500 to-rose-300',\n  verticalText = {\n    left: 'Meet',\n    right: 'Team',\n  },\n}: CreativeTeamProps) {\n  // Define the layout for the team members based on the image\n  const layoutPositions = [\n    { gridArea: '1 / 1 / 3 / 2', className: 'col-span-1 row-span-2' },\n    { gridArea: '1 / 2 / 2 / 3', className: 'col-span-1 row-span-1' },\n    { gridArea: '2 / 2 / 4 / 3', className: 'col-span-1 row-span-2' },\n    { gridArea: '1 / 3 / 3 / 4', className: 'col-span-1 row-span-2' },\n  ];\n\n  return (\n    <section\n      className={cn(\n        'relative w-full overflow-hidden rounded-3xl py-16',\n        className,\n      )}\n      style={{ backgroundColor, color: textColor }}\n    >\n      <div className=\"relative container mx-auto px-4\">\n        {/* Vertical text on left */}\n        <div className=\"absolute top-0 bottom-0 left-0 flex items-center\">\n          <div\n            className=\"origin-center -rotate-90 transform text-8xl font-bold whitespace-nowrap opacity-20\"\n            style={{ color: accentColor }}\n          >\n            {verticalText.left}\n          </div>\n        </div>\n\n        {/* Vertical text on right */}\n        <div className=\"absolute top-0 right-0 bottom-0 flex items-center\">\n          <div\n            className=\"origin-center -rotate-90 transform text-8xl font-bold whitespace-nowrap opacity-20\"\n            style={{ color: accentColor }}\n          >\n            {verticalText.right}\n          </div>\n        </div>\n\n        <div className=\"mb-12 text-center\">\n          <h2 className={cn('text-4xl font-bold md:text-5xl', mont.className)}>\n            {title}\n          </h2>\n          {subtitle && (\n            <p className=\"mt-4 text-lg\" style={{ color: secondaryColor }}>\n              {subtitle}\n            </p>\n          )}\n        </div>\n\n        <div className=\"mx-auto grid max-w-4xl grid-cols-1 gap-6 md:grid-cols-3\">\n          {teamMembers.slice(0, 6).map((member, index) => (\n            <div\n              key={member.id}\n              className={cn(\n                'relative overflow-hidden',\n                index < layoutPositions.length\n                  ? layoutPositions[index].className\n                  : '',\n              )}\n              style={{\n                gridArea:\n                  index < layoutPositions.length\n                    ? layoutPositions[index].gridArea\n                    : undefined,\n              }}\n            >\n              <div className=\"relative aspect-[3/4] overflow-hidden\">\n                <div\n                  className={`absolute inset-0 bg-gradient-to-b ${gradientColor} z-10 opacity-40 mix-blend-multiply`}\n                ></div>\n                <img\n                  src={member.image}\n                  alt={member.name}\n                  className=\"object-cover\"\n                />\n                <div className=\"absolute right-0 bottom-0 left-0 z-20 p-4\">\n                  <h3 className=\"text-xl font-bold text-white\">\n                    {member.name}\n                  </h3>\n                  <p className=\"text-white opacity-90\">{member.role}</p>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/team/team-6.tsx", "target": "components/mvpblocks/team-6.tsx"}]}