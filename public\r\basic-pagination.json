{"name": "basic-pagination", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/pagination.json", "https://blocks.mvp-subha.me/r/button.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport {\n  Pagination,\n  PaginationContent,\n  PaginationEllipsis,\n  PaginationItem,\n  PaginationLink,\n  PaginationNext,\n  PaginationPrevious,\n} from '@/components/ui/pagination';\n\ninterface BasicPaginationProps {\n  totalPages: number;\n  initialPage?: number;\n  siblingsCount?: number;\n  onPageChange?: (page: number) => void;\n  className?: string;\n  variant?: 'default' | 'outline' | 'rounded';\n  showDemo?: boolean;\n}\n\nexport default function BasicPagination({\n  totalPages = 10,\n  initialPage = 1,\n  siblingsCount = 1,\n  onPageChange,\n  className,\n  variant = 'default',\n  showDemo = false,\n}: BasicPaginationProps) {\n  const [currentPage, setCurrentPage] = useState(initialPage);\n\n  // Generate page numbers array\n  const generatePagination = () => {\n    // Always show first and last page\n    const firstPage = 1;\n    const lastPage = totalPages;\n\n    // Calculate range of pages to show around current page\n    const leftSiblingIndex = Math.max(currentPage - siblingsCount, firstPage);\n    const rightSiblingIndex = Math.min(currentPage + siblingsCount, lastPage);\n\n    // Determine whether to show ellipses\n    const shouldShowLeftDots = leftSiblingIndex > firstPage + 1;\n    const shouldShowRightDots = rightSiblingIndex < lastPage - 1;\n\n    // Initialize the array of page numbers\n    const pageNumbers: (number | string)[] = [];\n\n    // Always add first page\n    pageNumbers.push(firstPage);\n\n    // Add left ellipsis if needed\n    if (shouldShowLeftDots) {\n      pageNumbers.push('leftEllipsis');\n    }\n\n    // Add page numbers between ellipses\n    for (let i = leftSiblingIndex; i <= rightSiblingIndex; i++) {\n      if (i !== firstPage && i !== lastPage) {\n        pageNumbers.push(i);\n      }\n    }\n\n    // Add right ellipsis if needed\n    if (shouldShowRightDots) {\n      pageNumbers.push('rightEllipsis');\n    }\n\n    // Always add last page if it's not the same as first page\n    if (lastPage !== firstPage) {\n      pageNumbers.push(lastPage);\n    }\n\n    return pageNumbers;\n  };\n\n  const handlePageChange = (page: number) => {\n    if (page === currentPage) return;\n\n    setCurrentPage(page);\n    onPageChange?.(page);\n  };\n\n  const pageNumbers = generatePagination();\n\n  // Variants for motion animations\n  const itemVariants = {\n    initial: { opacity: 0, y: 5 },\n    animate: { opacity: 1, y: 0 },\n    exit: { opacity: 0, y: -5 },\n    hover: { scale: 1.05, transition: { duration: 0.2 } },\n  };\n\n  // Get button style based on variant\n  const getButtonStyle = (isActive: boolean) => {\n    if (variant === 'outline') {\n      return isActive\n        ? 'border-primary text-primary hover:bg-primary/10'\n        : 'border-border hover:border-primary/50 hover:text-primary';\n    }\n\n    if (variant === 'rounded') {\n      return isActive\n        ? 'bg-primary text-primary-foreground rounded-full'\n        : 'hover:bg-muted rounded-full';\n    }\n\n    // Default variant\n    return isActive ? 'bg-primary text-primary-foreground' : 'hover:bg-muted';\n  };\n\n  const PaginationComponent = (\n    <Pagination className={cn('py-4', className)}>\n      <PaginationContent>\n        <PaginationItem>\n          <motion.div\n            initial=\"initial\"\n            animate=\"animate\"\n            exit=\"exit\"\n            whileHover=\"hover\"\n            variants={itemVariants}\n            transition={{ duration: 0.3 }}\n          >\n            <PaginationPrevious\n              href=\"#\"\n              onClick={(e) => {\n                e.preventDefault();\n                if (currentPage > 1) {\n                  handlePageChange(currentPage - 1);\n                }\n              }}\n              className={cn(\n                currentPage <= 1 ? 'pointer-events-none opacity-50' : '',\n                variant === 'rounded' ? 'rounded-full' : '',\n              )}\n            />\n          </motion.div>\n        </PaginationItem>\n\n        {pageNumbers.map((page, index) => {\n          if (page === 'leftEllipsis' || page === 'rightEllipsis') {\n            return (\n              <PaginationItem key={`ellipsis-${index}`}>\n                <motion.div\n                  initial=\"initial\"\n                  animate=\"animate\"\n                  exit=\"exit\"\n                  variants={itemVariants}\n                  transition={{ duration: 0.3, delay: index * 0.05 }}\n                >\n                  <PaginationEllipsis />\n                </motion.div>\n              </PaginationItem>\n            );\n          }\n\n          const pageNum = page as number;\n          const isActive = pageNum === currentPage;\n\n          return (\n            <PaginationItem key={pageNum}>\n              <motion.div\n                initial=\"initial\"\n                animate=\"animate\"\n                exit=\"exit\"\n                whileHover=\"hover\"\n                variants={itemVariants}\n                transition={{ duration: 0.3, delay: index * 0.05 }}\n              >\n                <PaginationLink\n                  href=\"#\"\n                  isActive={isActive}\n                  onClick={(e) => {\n                    e.preventDefault();\n                    handlePageChange(pageNum);\n                  }}\n                  className={cn(\n                    getButtonStyle(isActive),\n                    variant === 'outline' && 'border',\n                    'transition-all duration-200',\n                  )}\n                >\n                  {pageNum}\n                </PaginationLink>\n              </motion.div>\n            </PaginationItem>\n          );\n        })}\n\n        <PaginationItem>\n          <motion.div\n            initial=\"initial\"\n            animate=\"animate\"\n            exit=\"exit\"\n            whileHover=\"hover\"\n            variants={itemVariants}\n            transition={{ duration: 0.3 }}\n          >\n            <PaginationNext\n              href=\"#\"\n              onClick={(e) => {\n                e.preventDefault();\n                if (currentPage < totalPages) {\n                  handlePageChange(currentPage + 1);\n                }\n              }}\n              className={cn(\n                currentPage >= totalPages\n                  ? 'pointer-events-none opacity-50'\n                  : '',\n                variant === 'rounded' ? 'rounded-full' : '',\n              )}\n            />\n          </motion.div>\n        </PaginationItem>\n      </PaginationContent>\n    </Pagination>\n  );\n\n  // If showDemo is true, render the demo with all variants\n  if (showDemo) {\n    // Sample data for pagination demo\n    const demoItems = [\n      {\n        id: 1,\n        title: 'Getting Started with MVPBlocks',\n        category: 'Tutorial',\n        date: 'Jan 15, 2023',\n      },\n      {\n        id: 2,\n        title: 'Building Responsive UIs',\n        category: 'Design',\n        date: 'Feb 3, 2023',\n      },\n      {\n        id: 3,\n        title: 'Advanced Animation Techniques',\n        category: 'Animation',\n        date: 'Mar 12, 2023',\n      },\n      {\n        id: 4,\n        title: 'State Management Patterns',\n        category: 'Development',\n        date: 'Apr 5, 2023',\n      },\n      {\n        id: 5,\n        title: 'Optimizing Performance',\n        category: 'Performance',\n        date: 'May 20, 2023',\n      },\n      {\n        id: 6,\n        title: 'Accessibility Best Practices',\n        category: 'Accessibility',\n        date: 'Jun 8, 2023',\n      },\n      {\n        id: 7,\n        title: 'Component Composition',\n        category: 'Architecture',\n        date: 'Jul 17, 2023',\n      },\n      {\n        id: 8,\n        title: 'Testing Strategies',\n        category: 'Testing',\n        date: 'Aug 22, 2023',\n      },\n      {\n        id: 9,\n        title: 'Deployment Workflows',\n        category: 'DevOps',\n        date: 'Sep 14, 2023',\n      },\n      {\n        id: 10,\n        title: 'Theme Customization',\n        category: 'Design',\n        date: 'Oct 30, 2023',\n      },\n      {\n        id: 11,\n        title: 'API Integration Patterns',\n        category: 'Development',\n        date: 'Nov 11, 2023',\n      },\n      {\n        id: 12,\n        title: 'Building Design Systems',\n        category: 'Design',\n        date: 'Dec 5, 2023',\n      },\n      {\n        id: 13,\n        title: 'Mobile-First Approach',\n        category: 'Design',\n        date: 'Jan 19, 2024',\n      },\n      {\n        id: 14,\n        title: 'Server-Side Rendering',\n        category: 'Performance',\n        date: 'Feb 8, 2024',\n      },\n      {\n        id: 15,\n        title: 'Authentication Flows',\n        category: 'Security',\n        date: 'Mar 22, 2024',\n      },\n    ];\n\n    // Items per page\n    const itemsPerPage = 3;\n\n    // Enhanced pagination component with content\n    const EnhancedPagination = ({\n      variant = 'default',\n    }: {\n      variant?: 'default' | 'outline' | 'rounded';\n    }) => {\n      const [page, setPage] = useState(1);\n\n      // Calculate total pages\n      const totalPages = Math.ceil(demoItems.length / itemsPerPage);\n\n      // Get current items\n      const startIndex = (page - 1) * itemsPerPage;\n      const currentItems = demoItems.slice(\n        startIndex,\n        startIndex + itemsPerPage,\n      );\n\n      return (\n        <div className=\"space-y-6\">\n          {/* Content area */}\n          <motion.div\n            className=\"bg-card/50 rounded-md border p-4\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            key={`page-${page}`} // Key changes force re-animation\n            transition={{ duration: 0.3 }}\n          >\n            <div className=\"mb-4 flex items-center justify-between border-b pb-2\">\n              <h4 className=\"text-muted-foreground text-sm font-medium\">\n                Showing {startIndex + 1}-\n                {Math.min(startIndex + itemsPerPage, demoItems.length)} of{' '}\n                {demoItems.length} items\n              </h4>\n              <span className=\"bg-primary/10 text-primary rounded-full px-2 py-1 text-xs font-medium\">\n                Page {page} of {totalPages}\n              </span>\n            </div>\n\n            <div className=\"space-y-4\">\n              {currentItems.map((item) => (\n                <motion.div\n                  key={item.id}\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{\n                    duration: 0.3,\n                    delay: (item.id % itemsPerPage) * 0.1,\n                  }}\n                  className=\"hover:bg-muted/50 flex items-center justify-between rounded-md border p-3\"\n                >\n                  <div>\n                    <h3 className=\"font-medium\">{item.title}</h3>\n                    <div className=\"text-muted-foreground flex items-center gap-2 text-sm\">\n                      <span className=\"bg-primary/10 text-primary rounded-full px-2 py-0.5 text-xs font-medium\">\n                        {item.category}\n                      </span>\n                      <span>{item.date}</span>\n                    </div>\n                  </div>\n                  <button className=\"hover:bg-muted rounded-full p-2\">\n                    <ChevronRight className=\"text-muted-foreground h-4 w-4\" />\n                  </button>\n                </motion.div>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Pagination controls */}\n          <Pagination className=\"py-2\">\n            <PaginationContent>\n              <PaginationItem>\n                <motion.div\n                  initial=\"initial\"\n                  animate=\"animate\"\n                  exit=\"exit\"\n                  whileHover=\"hover\"\n                  variants={itemVariants}\n                  transition={{ duration: 0.3 }}\n                >\n                  <PaginationPrevious\n                    href=\"#\"\n                    onClick={(e) => {\n                      e.preventDefault();\n                      if (page > 1) setPage(page - 1);\n                    }}\n                    className={cn(\n                      page <= 1 ? 'pointer-events-none opacity-50' : '',\n                      variant === 'rounded' ? 'rounded-full' : '',\n                    )}\n                  />\n                </motion.div>\n              </PaginationItem>\n\n              {Array.from({ length: totalPages }).map((_, i) => {\n                const pageNum = i + 1;\n                const isActive = pageNum === page;\n\n                // Show ellipsis for many pages\n                if (totalPages > 5) {\n                  // Always show first and last page\n                  if (pageNum === 1 || pageNum === totalPages) {\n                    return (\n                      <PaginationItem key={pageNum}>\n                        <motion.div\n                          initial=\"initial\"\n                          animate=\"animate\"\n                          exit=\"exit\"\n                          whileHover=\"hover\"\n                          variants={itemVariants}\n                          transition={{ duration: 0.3 }}\n                        >\n                          <PaginationLink\n                            href=\"#\"\n                            isActive={isActive}\n                            onClick={(e) => {\n                              e.preventDefault();\n                              setPage(pageNum);\n                            }}\n                            className={cn(\n                              getButtonStyle(isActive),\n                              variant === 'outline' && 'border',\n                              'transition-all duration-200',\n                            )}\n                          >\n                            {pageNum}\n                          </PaginationLink>\n                        </motion.div>\n                      </PaginationItem>\n                    );\n                  }\n\n                  // Show current page and adjacent pages\n                  if (\n                    pageNum === page ||\n                    pageNum === page - 1 ||\n                    pageNum === page + 1\n                  ) {\n                    return (\n                      <PaginationItem key={pageNum}>\n                        <motion.div\n                          initial=\"initial\"\n                          animate=\"animate\"\n                          exit=\"exit\"\n                          whileHover=\"hover\"\n                          variants={itemVariants}\n                          transition={{ duration: 0.3 }}\n                        >\n                          <PaginationLink\n                            href=\"#\"\n                            isActive={isActive}\n                            onClick={(e) => {\n                              e.preventDefault();\n                              setPage(pageNum);\n                            }}\n                            className={cn(\n                              getButtonStyle(isActive),\n                              variant === 'outline' && 'border',\n                              'transition-all duration-200',\n                            )}\n                          >\n                            {pageNum}\n                          </PaginationLink>\n                        </motion.div>\n                      </PaginationItem>\n                    );\n                  }\n\n                  // Show ellipsis\n                  if (\n                    (pageNum === 2 && page > 3) ||\n                    (pageNum === totalPages - 1 && page < totalPages - 2)\n                  ) {\n                    return (\n                      <PaginationItem key={`ellipsis-${pageNum}`}>\n                        <motion.div\n                          initial=\"initial\"\n                          animate=\"animate\"\n                          exit=\"exit\"\n                          variants={itemVariants}\n                          transition={{ duration: 0.3 }}\n                        >\n                          <PaginationEllipsis />\n                        </motion.div>\n                      </PaginationItem>\n                    );\n                  }\n\n                  return null;\n                }\n\n                // Show all pages if total pages <= 5\n                return (\n                  <PaginationItem key={pageNum}>\n                    <motion.div\n                      initial=\"initial\"\n                      animate=\"animate\"\n                      exit=\"exit\"\n                      whileHover=\"hover\"\n                      variants={itemVariants}\n                      transition={{ duration: 0.3 }}\n                    >\n                      <PaginationLink\n                        href=\"#\"\n                        isActive={isActive}\n                        onClick={(e) => {\n                          e.preventDefault();\n                          setPage(pageNum);\n                        }}\n                        className={cn(\n                          getButtonStyle(isActive),\n                          variant === 'outline' && 'border',\n                          'transition-all duration-200',\n                        )}\n                      >\n                        {pageNum}\n                      </PaginationLink>\n                    </motion.div>\n                  </PaginationItem>\n                );\n              })}\n\n              <PaginationItem>\n                <motion.div\n                  initial=\"initial\"\n                  animate=\"animate\"\n                  exit=\"exit\"\n                  whileHover=\"hover\"\n                  variants={itemVariants}\n                  transition={{ duration: 0.3 }}\n                >\n                  <PaginationNext\n                    href=\"#\"\n                    onClick={(e) => {\n                      e.preventDefault();\n                      if (page < totalPages) setPage(page + 1);\n                    }}\n                    className={cn(\n                      page >= totalPages\n                        ? 'pointer-events-none opacity-50'\n                        : '',\n                      variant === 'rounded' ? 'rounded-full' : '',\n                    )}\n                  />\n                </motion.div>\n              </PaginationItem>\n            </PaginationContent>\n          </Pagination>\n        </div>\n      );\n    };\n\n    return (\n      <div className=\"mx-auto max-w-6xl px-4 py-12\">\n        <div className=\"mb-12 text-center\">\n          <h2 className=\"mb-2 text-3xl font-bold tracking-tight\">\n            Pagination Examples\n          </h2>\n          <p className=\"text-muted-foreground mx-auto max-w-2xl\">\n            Interactive pagination components that demonstrate real content\n            navigation. Each example shows how pagination can be used to\n            navigate through a collection of items.\n          </p>\n        </div>\n\n        <div className=\"space-y-16\">\n          {/* Default Variant */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n            className=\"bg-card rounded-lg border p-6 shadow-sm\"\n          >\n            <h3 className=\"mb-6 text-xl font-semibold\">Default Style</h3>\n            <EnhancedPagination />\n          </motion.div>\n\n          {/* Outline Variant */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.1 }}\n            className=\"bg-card rounded-lg border p-6 shadow-sm\"\n          >\n            <h3 className=\"mb-6 text-xl font-semibold\">Outline Style</h3>\n            <EnhancedPagination variant=\"outline\" />\n          </motion.div>\n\n          {/* Rounded Variant */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.2 }}\n            className=\"bg-card rounded-lg border p-6 shadow-sm\"\n          >\n            <h3 className=\"mb-6 text-xl font-semibold\">Rounded Style</h3>\n            <EnhancedPagination variant=\"rounded\" />\n          </motion.div>\n        </div>\n      </div>\n    );\n  }\n\n  // Otherwise, just return the pagination component\n  return PaginationComponent;\n}\n", "path": "/components/mvpblocks/basics/pagination/basic-pagination.tsx", "target": "components/mvpblocks/basic-pagination.tsx"}]}