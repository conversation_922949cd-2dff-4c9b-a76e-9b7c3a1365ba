{"name": "pricing-2", "author": "mosespace", "type": "registry:block", "dependencies": ["lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/card.json", "https://blocks.mvp-subha.me/r/button.json"], "files": [{"type": "registry:block", "content": "import Link from 'next/link';\nimport { Button } from '@/components/ui/button';\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardFooter,\n  CardHeader,\n  CardTitle,\n} from '@/components/ui/card';\nimport { Check } from 'lucide-react';\n\ntype ButtonVariant =\n  | 'outline'\n  | 'default'\n  | 'link'\n  | 'destructive'\n  | 'secondary'\n  | 'ghost';\n\nconst plans: {\n  name: string;\n  price: string;\n  description: string;\n  features: string[];\n  buttonVariant: ButtonVariant | null | undefined;\n  badge: string | null;\n}[] = [\n  {\n    name: 'Free',\n    price: '$0 / mo',\n    description: 'Per editor',\n    features: [\n      'Basic Analytics Dashboard',\n      '5GB Cloud Storage',\n      'Email and Chat Support',\n    ],\n    buttonVariant: 'outline',\n    badge: null,\n  },\n  {\n    name: 'Pro',\n    price: '$19 / mo',\n    description: 'Per editor',\n    features: [\n      'Everything in Free Plan',\n      '5GB Cloud Storage',\n      'Email and Chat Support',\n      'Access to Community Forum',\n      'Single User Access',\n      'Access to Basic Templates',\n      'Mobile App Access',\n      '1 Custom Report Per Month',\n      'Monthly Product Updates',\n      'Standard Security Features',\n    ],\n    buttonVariant: 'default',\n    badge: 'Popular',\n  },\n  {\n    name: 'Startup',\n    price: '$29 / mo',\n    description: 'Per editor',\n    features: [\n      'Everything in Pro Plan',\n      '5GB Cloud Storage',\n      'Email and Chat Support',\n    ],\n    buttonVariant: 'outline',\n    badge: null,\n  },\n];\n\nexport default function PricingTwo() {\n  return (\n    <section className=\"not-prose relative w-full py-16 md:py-32\">\n      <div className=\"absolute inset-0 -z-10 overflow-hidden\">\n        <div className=\"bg-primary/10 absolute -top-[10%] left-[50%] h-[40%] w-[60%] -translate-x-1/2 rounded-full blur-3xl\" />\n        <div className=\"bg-primary/5 absolute -right-[10%] -bottom-[10%] h-[40%] w-[40%] rounded-full blur-3xl\" />\n        <div className=\"bg-primary/5 absolute -bottom-[10%] -left-[10%] h-[40%] w-[40%] rounded-full blur-3xl\" />\n      </div>\n\n      <div className=\"mx-auto max-w-5xl px-6\">\n        <div className=\"mx-auto max-w-2xl space-y-6 text-center\">\n          <h1 className=\"text-center text-4xl font-semibold lg:text-5xl\">\n            Pricing that Scales with You\n          </h1>\n          <p>\n            Gemini is evolving to be more than just the models. It supports an\n            entire suite of APIs and platforms helping developers and businesses\n            innovate.\n          </p>\n        </div>\n\n        <div className=\"mt-8 grid gap-6 md:mt-20 md:grid-cols-3\">\n          {plans.map((plan, index) => (\n            <Card\n              key={index}\n              className={`relative flex flex-col ${\n                plan.badge ? 'border border-amber-300' : ''\n              }`}\n            >\n              {plan.badge && (\n                <span className=\"border-primary/20 bg-primary absolute inset-x-0 -top-3 mx-auto flex h-6 w-fit items-center rounded-full bg-linear-to-br/increasing from-purple-400 to-amber-300 px-3 py-1 text-xs font-medium text-amber-950 ring-1 ring-white/20 ring-offset-1 ring-offset-gray-950/5 ring-inset\">\n                  {plan.badge}\n                </span>\n              )}\n\n              <CardHeader>\n                <CardTitle className=\"font-medium\">{plan.name}</CardTitle>\n                <span className=\"my-3 block text-2xl font-semibold\">\n                  {plan.price}\n                </span>\n                <CardDescription className=\"text-sm\">\n                  {plan.description}\n                </CardDescription>\n              </CardHeader>\n\n              <CardContent className=\"space-y-4\">\n                <hr className=\"border-dashed\" />\n                <ul className=\"list-outside space-y-3 text-sm\">\n                  {plan.features.map((feature, i) => (\n                    <li key={i} className=\"flex items-center gap-2\">\n                      <Check className=\"size-3\" />\n                      {feature}\n                    </li>\n                  ))}\n                </ul>\n              </CardContent>\n\n              <CardFooter className=\"mt-auto\">\n                <Button asChild variant={plan.buttonVariant} className=\"w-full\">\n                  <Link href=\"\">Get Started</Link>\n                </Button>\n              </CardFooter>\n            </Card>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/pricing/pricing-2.tsx", "target": "components/mvpblocks/pricing-2.tsx"}]}