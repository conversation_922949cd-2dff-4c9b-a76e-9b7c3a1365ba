{"name": "about-us-2", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "next-themes", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/border-beam.json", "https://blocks.mvp-subha.me/r/counter.json", "https://blocks.mvp-subha.me/r/spotlight.json", "https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { motion, useInView } from 'framer-motion';\nimport { useRef } from 'react';\nimport { NumberTicker } from '@/components/ui/counter';\nimport { useTheme } from 'next-themes';\nimport { cn } from '@/lib/utils';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  Users,\n  Award,\n  Briefcase,\n  Code,\n  Sparkles,\n  Building,\n  LineChart,\n  CheckCircle,\n  Clock,\n  Zap,\n} from 'lucide-react';\n\ninterface StatItemProps {\n  value: number;\n  label: string;\n  icon: React.ReactNode;\n  delay?: number;\n  decimalPlaces?: number;\n  color?: string;\n}\n\nconst StatItem = ({\n  value,\n  label,\n  icon,\n  delay = 0,\n  decimalPlaces = 0,\n  color = 'from-primary to-primary/70',\n}: StatItemProps) => {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, amount: 0.3 });\n  const { resolvedTheme } = useTheme();\n\n  return (\n    <motion.div\n      ref={ref}\n      initial={{ opacity: 0, y: 20 }}\n      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n      transition={{ duration: 0.6, delay: delay, ease: 'easeOut' }}\n      className={cn(\n        'group border-border/30 bg-card relative overflow-hidden rounded-xl border p-6',\n        resolvedTheme === 'dark'\n          ? 'shadow-xl shadow-black/5'\n          : 'shadow-lg shadow-black/[0.03]',\n      )}\n    >\n      <div\n        className={cn(\n          'absolute -top-6 -right-6 h-24 w-24 rounded-full bg-gradient-to-br opacity-20 blur-2xl transition-all duration-500 group-hover:opacity-30 group-hover:blur-3xl',\n          color,\n        )}\n      />\n\n      <div className=\"flex items-center gap-4\">\n        <div\n          className={cn(\n            'flex h-12 w-12 shrink-0 items-center justify-center rounded-lg bg-gradient-to-br text-white',\n            color,\n          )}\n        >\n          {icon}\n        </div>\n\n        <div className=\"flex flex-col\">\n          <h3 className=\"flex items-baseline text-3xl font-bold tracking-tight\">\n            <NumberTicker\n              value={value}\n              decimalPlaces={decimalPlaces}\n              className=\"tabular-nums\"\n            />\n            <span className=\"ml-1 text-sm font-medium opacity-70\">+</span>\n          </h3>\n          <p className=\"text-muted-foreground text-sm font-medium\">{label}</p>\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default function AboutUs2() {\n  const aboutRef = useRef(null);\n  const statsRef = useRef(null);\n  const timelineRef = useRef(null);\n\n  const aboutInView = useInView(aboutRef, { once: true, amount: 0.3 });\n  const timelineInView = useInView(timelineRef, { once: true, amount: 0.2 });\n\n  const stats = [\n    {\n      value: 5000,\n      label: 'Happy Clients',\n      icon: <Users className=\"h-5 w-5\" />,\n      delay: 0,\n      color: 'from-rose-500 to-orange-500',\n      decimalPlaces: 0,\n    },\n    {\n      value: 15,\n      label: 'Years Experience',\n      icon: <Clock className=\"h-5 w-5\" />,\n      delay: 0.1,\n      color: 'from-blue-500 to-cyan-500',\n      decimalPlaces: 0,\n    },\n    {\n      value: 100,\n      label: 'Projects Completed',\n      icon: <CheckCircle className=\"h-5 w-5\" />,\n      delay: 0.2,\n      color: 'from-green-500 to-emerald-500',\n      decimalPlaces: 0,\n    },\n    {\n      value: 24,\n      label: 'Awards Won',\n      icon: <Award className=\"h-5 w-5\" />,\n      delay: 0.3,\n      color: 'from-purple-500 to-violet-500',\n      decimalPlaces: 0,\n    },\n  ];\n\n  return (\n    <section className=\"relative w-full overflow-hidden py-16 md:py-24\">\n      {/* Background pattern */}\n      <div className=\"absolute inset-0 -z-10 opacity-[0.02] dark:opacity-[0.05]\">\n        <svg className=\"h-full w-full\" xmlns=\"http://www.w3.org/2000/svg\">\n          <defs>\n            <pattern\n              id=\"grid\"\n              width=\"40\"\n              height=\"40\"\n              patternUnits=\"userSpaceOnUse\"\n            >\n              <path\n                d=\"M 40 0 L 0 0 0 40\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1\"\n              />\n            </pattern>\n          </defs>\n          <rect width=\"100%\" height=\"100%\" fill=\"url(#grid)\" />\n        </svg>\n      </div>\n\n      <div className=\"relative z-10 container mx-auto max-w-6xl px-4 md:px-6\">\n        {/* Header Section with Badge */}\n        <div className=\"mx-auto mb-16 max-w-3xl text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 10 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, ease: 'easeOut' }}\n            className=\"mb-4 flex justify-center\"\n          >\n            <Badge\n              variant=\"outline\"\n              className=\"border-primary/20 bg-primary/5 rounded-full px-4 py-1 text-sm font-medium\"\n            >\n              <Sparkles className=\"text-primary mr-1 h-3.5 w-3.5\" />\n              About Us\n            </Badge>\n          </motion.div>\n\n          <motion.h1\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.1, ease: 'easeOut' }}\n            className=\"from-foreground to-foreground/70 bg-gradient-to-b bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl\"\n          >\n            About Our Company\n          </motion.h1>\n\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2, ease: 'easeOut' }}\n            className=\"text-muted-foreground mt-4 text-xl\"\n          >\n            Delivering excellence for over 15 years\n          </motion.p>\n        </div>\n\n        {/* Stats Section */}\n        <div ref={statsRef} className=\"mb-20\">\n          <div className=\"grid gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n            {stats.map((stat, index) => (\n              <StatItem\n                key={index}\n                value={stat.value}\n                label={stat.label}\n                icon={stat.icon}\n                delay={stat.delay || index * 0.1}\n                decimalPlaces={stat.decimalPlaces}\n                color={stat.color}\n              />\n            ))}\n          </div>\n        </div>\n\n        {/* About Content Section */}\n        <div ref={aboutRef} className=\"relative mx-auto mb-20\">\n          <div className=\"grid gap-16 md:grid-cols-2\">\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={\n                aboutInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }\n              }\n              transition={{ duration: 0.7, delay: 0.1, ease: 'easeOut' }}\n              className=\"relative space-y-6\"\n            >\n              <div className=\"from-primary/80 to-primary/60 inline-flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br text-white shadow-lg\">\n                <Zap className=\"h-6 w-6\" />\n              </div>\n\n              <h2 className=\"text-2xl font-bold tracking-tight\">Our Mission</h2>\n\n              <p className=\"text-muted-foreground text-base leading-relaxed\">\n                To empower businesses with innovative digital solutions that\n                drive growth, enhance user experiences, and create lasting value\n                in an ever-evolving technological landscape.\n              </p>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={\n                aboutInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }\n              }\n              transition={{ duration: 0.7, delay: 0.3, ease: 'easeOut' }}\n              className=\"relative space-y-6\"\n            >\n              <div className=\"inline-flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500/80 to-blue-500/60 text-white shadow-lg\">\n                <LineChart className=\"h-6 w-6\" />\n              </div>\n\n              <h2 className=\"text-2xl font-bold tracking-tight\">Our Vision</h2>\n\n              <p className=\"text-muted-foreground text-base leading-relaxed\">\n                To be the leading provider of transformative digital\n                experiences, recognized globally for our commitment to\n                excellence, innovation, and client success.\n              </p>\n            </motion.div>\n          </div>\n\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={aboutInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n            transition={{ duration: 0.7, delay: 0.5, ease: 'easeOut' }}\n            className=\"mt-16 flex items-start gap-4\"\n          >\n            <div className=\"from-primary/20 to-primary/5 text-primary inline-flex h-10 w-10 shrink-0 items-center justify-center rounded-lg bg-gradient-to-br\">\n              <Building className=\"h-5 w-5\" />\n            </div>\n            <p className=\"text-muted-foreground text-base leading-relaxed\">\n              We are a passionate team of experts dedicated to delivering\n              exceptional solutions that help businesses thrive in the digital\n              landscape. Our commitment to innovation and quality has made us a\n              trusted partner for organizations worldwide.\n            </p>\n          </motion.div>\n        </div>\n\n        {/* Timeline Section */}\n        <div ref={timelineRef} className=\"relative mx-auto max-w-4xl\">\n          <motion.h2\n            initial={{ opacity: 0, y: 20 }}\n            animate={\n              timelineInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }\n            }\n            transition={{ duration: 0.6, ease: 'easeOut' }}\n            className=\"mb-10 text-center text-2xl font-bold tracking-tight md:text-3xl\"\n          >\n            Our Journey\n          </motion.h2>\n\n          <div className=\"border-border/60 relative ml-4 border-l pl-8 md:ml-0 md:border-none md:pl-0\">\n            {[\n              {\n                year: '2008',\n                title: 'Founded',\n                description:\n                  'Our company was established with a vision to transform digital experiences.',\n              },\n              {\n                year: '2015',\n                title: 'Global Expansion',\n                description:\n                  'Expanded operations to serve clients across 20+ countries worldwide.',\n              },\n              {\n                year: '2019',\n                title: 'Innovation Award',\n                description:\n                  'Recognized for our cutting-edge solutions and technological innovation.',\n              },\n              {\n                year: '2023',\n                title: 'New Horizons',\n                description:\n                  'Launched new service offerings to meet evolving market demands.',\n              },\n            ].map((item, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, x: -20 }}\n                animate={\n                  timelineInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }\n                }\n                transition={{\n                  duration: 0.5,\n                  delay: 0.1 * index,\n                  ease: 'easeOut',\n                }}\n                className=\"relative mb-10 md:grid md:grid-cols-5 md:gap-8\"\n              >\n                <div className=\"md:col-span-1\">\n                  <div className=\"border-border bg-card absolute -left-12 flex h-8 w-8 items-center justify-center rounded-full border text-sm font-bold md:static md:h-auto md:w-auto md:rounded-none md:border-none md:bg-transparent md:text-xl\">\n                    {item.year}\n                  </div>\n                </div>\n                <div className=\"md:col-span-4\">\n                  <h3 className=\"text-lg font-bold md:text-xl\">{item.title}</h3>\n                  <p className=\"text-muted-foreground mt-1\">\n                    {item.description}\n                  </p>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/about/about-us-2.tsx", "target": "components/mvpblocks/about-us-2.tsx"}]}