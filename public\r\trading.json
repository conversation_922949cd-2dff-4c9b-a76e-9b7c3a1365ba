{"name": "trading", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/typewriter.json", "https://blocks.mvp-subha.me/r/border-beam.json"], "files": [{"type": "registry:block", "content": "import { ArrowRight, Command } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { motion } from 'framer-motion';\nimport TextGenerateEffect from '@/components/ui/typewriter';\nimport { BorderBeam } from '@/components/ui/border-beam';\n\nexport default function Trading() {\n  return (\n    <motion.section\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n      className=\"relative container mx-auto max-w-6xl px-4 py-20\"\n    >\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 0.2 }}\n        className=\"mb-4 inline-block w-fit rounded-full border bg-white/5 px-4 py-1.5 backdrop-blur-lg\"\n        style={{ boxShadow: '0 0 10px 0 #e60a6430 inset' }}\n      >\n        <span className=\"text-sm font-medium\">\n          <Command className=\"mr-2 inline-block h-4 w-4\" />\n          Next-gen crypto trading platform\n        </span>\n      </motion.div>\n\n      <div className=\"relative z-10 mt-6\">\n        <h1 className=\"mb-4 text-left text-5xl font-normal tracking-tight md:text-7xl\">\n          <span className=\"text-foreground\">\n            <TextGenerateEffect words=\"Trade crypto with\" />\n          </span>\n          <br />\n          <span className=\"text-foreground font-medium\">\n            <TextGenerateEffect words=\"confidence & security\" />\n          </span>\n        </h1>\n\n        <motion.p\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n          className=\"text-foreground/50 mb-8 max-w-2xl text-left text-lg md:text-xl\"\n        >\n          Experience seamless cryptocurrency trading with advanced features,\n          real-time analytics, and institutional-grade security.{' '}\n          <span className=\"text-foreground/50\">Start trading in minutes.</span>\n        </motion.p>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.5 }}\n          className=\"flex flex-col items-start gap-4 sm:flex-row\"\n        >\n          <Button\n            size=\"lg\"\n            className=\"rounded-full bg-gradient-to-b from-rose-500 to-rose-700 text-white shadow-[0px_2px_0px_0px_rgba(255,255,255,0.3)_inset]\"\n          >\n            Start Trading Now\n          </Button>\n          <Button size=\"lg\" variant=\"link\">\n            View Markets <ArrowRight className=\"ml-2 h-4 w-4\" />\n          </Button>\n        </motion.div>\n      </div>\n\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.6 }}\n        className=\"relative mx-auto mt-20 max-w-5xl overflow-hidden rounded-xl\"\n      >\n        <div className=\"overflow-hidden rounded-xl\">\n          <img\n            src=\"https://blocks.mvp-subha.me/assets/trading-hero/db.webp\"\n            alt=\"CryptoTrade Dashboard\"\n            className=\"h-auto w-full\"\n          />\n        </div>\n        <BorderBeam\n          duration={6}\n          size={400}\n          className=\"from-transparent via-red-500 to-transparent\"\n        />\n        <BorderBeam\n          duration={6}\n          delay={3}\n          size={400}\n          className=\"from-transparent via-blue-500 to-transparent\"\n        />\n      </motion.div>\n    </motion.section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/hero/trading.tsx", "target": "components/mvpblocks/trading.tsx"}]}