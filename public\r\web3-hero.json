{"name": "web3-hero", "type": "registry:block", "dependencies": ["react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\n\nimport React, { useEffect } from 'react';\n\nexport default function Web3LandingPage() {\n  useEffect(() => {\n    const canvas = document.getElementById(\n      'particle-bg',\n    ) as HTMLCanvasElement | null;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d')!;\n    const resize = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n    resize();\n\n    type Particle = {\n      hue: number;\n      sat: number;\n      lum: number;\n      x: number;\n      y: number;\n      xLast: number;\n      yLast: number;\n      xSpeed: number;\n      ySpeed: number;\n      age: number;\n      ageSinceStuck: number;\n      attractor: { oldIndex: number; gridSpotIndex: number };\n      name: string;\n    };\n\n    type Spot = {\n      x: number;\n      y: number;\n      busyAge: number;\n      spotIndex: number;\n      isEdge: string | false;\n      field: number;\n    };\n\n    const App = {\n      width: canvas.width,\n      height: canvas.height,\n      ctx,\n      dataToImageRatio: 1,\n      particles: [] as Particle[],\n      stepCount: 0,\n      lifespan: 1000,\n      popPerBirth: 1,\n      maxPop: 300,\n      birthFreq: 2,\n      gridSize: 8,\n      gridSteps: Math.floor(1000 / 8),\n      grid: [] as Spot[],\n      drawnInLastFrame: 0,\n      deathCount: 0,\n      xC: canvas.width / 2,\n      yC: canvas.height / 2,\n      gridMaxIndex: 0,\n      birth() {\n        const idx = Math.floor(Math.random() * this.gridMaxIndex);\n        const spot = this.grid[idx];\n        this.particles.push({\n          hue: 276,\n          sat: 95,\n          lum: 60 + Math.floor(25 * Math.random()),\n          x: spot.x,\n          y: spot.y,\n          xLast: spot.x,\n          yLast: spot.y,\n          xSpeed: 0,\n          ySpeed: 0,\n          age: 0,\n          ageSinceStuck: 0,\n          attractor: {\n            oldIndex: idx,\n            gridSpotIndex: idx,\n          },\n          name: `seed-${Math.ceil(Math.random() * 1e7)}`,\n        });\n      },\n      kill(name: string) {\n        this.particles = this.particles.filter(\n          (p: { name: string }) => p.name !== name,\n        );\n      },\n      move() {\n        const k = 8,\n          visc = 0.4;\n        this.particles.forEach(\n          (p: {\n            xLast: any;\n            x: number;\n            yLast: any;\n            y: number;\n            attractor: { gridSpotIndex: any; oldIndex: any };\n            ageSinceStuck: number;\n            name: any;\n            xSpeed: number;\n            ySpeed: number;\n            age: number;\n          }) => {\n            p.xLast = p.x;\n            p.yLast = p.y;\n\n            let idx = p.attractor.gridSpotIndex;\n            let spot = this.grid[idx];\n\n            if (Math.random() < 0.5 && !spot.isEdge) {\n              const neighbours = [\n                this.grid[idx - 1],\n                this.grid[idx + 1],\n                this.grid[idx - this.gridSteps],\n                this.grid[idx + this.gridSteps],\n              ];\n              const chaos = 30;\n              const best = neighbours.reduce((a, b) =>\n                a.field + chaos * Math.random() >\n                b.field + chaos * Math.random()\n                  ? a\n                  : b,\n              );\n              if (best.busyAge === 0 || best.busyAge > 15) {\n                p.ageSinceStuck = 0;\n                p.attractor.oldIndex = idx;\n                p.attractor.gridSpotIndex = best.spotIndex;\n                spot = best;\n                spot.busyAge = 1;\n              } else {\n                p.ageSinceStuck++;\n              }\n            } else {\n              p.ageSinceStuck++;\n            }\n            if (p.ageSinceStuck === 10) this.kill(p.name);\n\n            const dx = p.x - spot.x,\n              dy = p.y - spot.y,\n              xAcc = -k * dx,\n              yAcc = -k * dy;\n            p.xSpeed = (p.xSpeed + xAcc) * visc;\n            p.ySpeed = (p.ySpeed + yAcc) * visc;\n            p.x += 0.1 * p.xSpeed;\n            p.y += 0.1 * p.ySpeed;\n\n            if (++p.age > this.lifespan) {\n              this.kill(p.name);\n              this.deathCount++;\n            }\n          },\n        );\n      },\n      dataXYtoCanvasXY(x: number, y: number) {\n        const z = 1.6;\n        return {\n          x: this.xC + x * z * this.dataToImageRatio,\n          y: this.yC + y * z * this.dataToImageRatio,\n        };\n      },\n      draw() {\n        this.drawnInLastFrame = 0;\n        if (!this.particles.length) return;\n        const ctx = this.ctx;\n        ctx.fillStyle = 'rgba(10,5,25,0.08)';\n        ctx.fillRect(0, 0, this.width, this.height);\n\n        this.particles.forEach(\n          (p: {\n            hue: number;\n            sat: any;\n            lum: any;\n            xLast: any;\n            yLast: any;\n            x: any;\n            y: any;\n            attractor: {\n              gridSpotIndex: string | number;\n              oldIndex: string | number;\n            };\n          }) => {\n            const h = p.hue + this.stepCount / 30,\n              s = p.sat,\n              l = p.lum,\n              last = this.dataXYtoCanvasXY(p.xLast, p.yLast),\n              now = this.dataXYtoCanvasXY(p.x, p.y),\n              spot = this.grid[p.attractor.gridSpotIndex],\n              spotXY = this.dataXYtoCanvasXY(spot.x, spot.y),\n              old = this.grid[p.attractor.oldIndex],\n              oldXY = this.dataXYtoCanvasXY(old.x, old.y);\n\n            ctx.strokeStyle = `hsla(${h},${s}%,${l}%,1)`;\n            ctx.lineWidth = 1.5 * this.dataToImageRatio;\n            ctx.beginPath();\n            ctx.moveTo(last.x, last.y);\n            ctx.lineTo(now.x, now.y);\n            ctx.stroke();\n\n            ctx.beginPath();\n            ctx.moveTo(oldXY.x, oldXY.y);\n            ctx.lineTo(spotXY.x, spotXY.y);\n            ctx.arc(\n              spotXY.x,\n              spotXY.y,\n              1.5 * this.dataToImageRatio,\n              0,\n              Math.PI * 2,\n            );\n            ctx.stroke();\n            this.drawnInLastFrame++;\n          },\n        );\n      },\n      evolve() {\n        this.stepCount++;\n        this.grid.forEach((s: { busyAge: number }) => {\n          if (s.busyAge > 0) s.busyAge++;\n        });\n        if (\n          this.stepCount % this.birthFreq === 0 &&\n          this.particles.length + this.popPerBirth < this.maxPop\n        )\n          this.birth();\n        this.move();\n        this.draw();\n      },\n      initDraw() {\n        this.ctx.fillStyle = '#050514';\n        this.ctx.fillRect(0, 0, this.width, this.height);\n      },\n    } as any;\n\n    (() => {\n      let i = 0;\n      for (let xx = -500; xx < 500; xx += 8) {\n        for (let yy = -500; yy < 500; yy += 8) {\n          const r = Math.sqrt(xx * xx + yy * yy),\n            r0 = 100,\n            field = r < r0 ? (255 / r0) * r : 255 - Math.min(255, (r - r0) / 2);\n          App.grid.push({\n            x: xx,\n            y: yy,\n            busyAge: 0,\n            spotIndex: i,\n            isEdge:\n              xx === -500\n                ? 'left'\n                : xx === -500 + 8 * (App.gridSteps - 1)\n                  ? 'right'\n                  : yy === -500\n                    ? 'top'\n                    : yy === -500 + 8 * (App.gridSteps - 1)\n                      ? 'bottom'\n                      : false,\n            field,\n          });\n          i++;\n        }\n      }\n      App.gridMaxIndex = i;\n    })();\n\n    App.initDraw();\n\n    const loop = () => {\n      App.evolve();\n      requestAnimationFrame(loop);\n    };\n    loop();\n\n    setTimeout(() => {\n      document\n        .querySelectorAll<HTMLElement>('.animate-seq')\n        .forEach((el, idx) => {\n          setTimeout(() => el.classList.add('visible'), idx * 120);\n        });\n    }, 100);\n\n    const onResize = () => {\n      resize();\n      App.width = canvas.width;\n      App.height = canvas.height;\n      App.xC = App.width / 2;\n      App.yC = App.height / 2;\n    };\n    window.addEventListener('resize', onResize);\n\n    return () => window.removeEventListener('resize', onResize);\n  }, []);\n\n  return (\n    <div\n      className=\"relative overflow-x-hidden bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950 text-slate-200 antialiased selection:bg-fuchsia-400/30 selection:text-fuchsia-200\"\n      style={{ fontFamily: 'Inter, Roboto, sans-serif' }}\n    >\n      <canvas\n        id=\"particle-bg\"\n        className=\"pointer-events-none fixed inset-0 h-full w-full\"\n        style={{ zIndex: -1 }}\n      />\n\n      <div className=\"pointer-events-none absolute -top-32 left-0 h-[350px] w-[350px] rounded-full bg-fuchsia-500/15 blur-[100px]\" />\n      <div className=\"pointer-events-none absolute right-0 bottom-0 h-[380px] w-[320px] rounded-full bg-fuchsia-600/20 blur-3xl\" />\n\n      <main className=\"container mx-auto mt-14 mb-10 max-w-7xl px-4 lg:mt-20\">\n        <div className=\"flex flex-col-reverse items-center gap-10 lg:flex-row lg:gap-16\">\n          <section className=\"animate-seq animate-delay-300 flex w-full max-w-xl flex-col items-center text-center lg:items-start lg:text-left\">\n            <h1\n              className=\"font-space-mono mb-5 text-4xl font-medium tracking-tighter text-white sm:text-5xl md:text-6xl\"\n              style={{ fontFamily: 'Inter, Roboto, sans-serif' }}\n            >\n              Unleash the Chain\n              <br />\n              Own the Future\n            </h1>\n            <p className=\"font-geist mr-auto mb-6 ml-auto max-w-2xl text-lg text-gray-300 sm:text-xl\">\n              Monitor your holdings in real time, swap at lightning speed and\n              stake with a single click. Your hub for everything Web&nbsp;3.\n            </p>\n            <div className=\"mt-2 mb-1 flex w-full flex-col items-center justify-center gap-3 sm:w-auto sm:flex-row\">\n              <button className=\"button font-geist\" aria-label=\"Get Started\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  strokeWidth=\"2\"\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  className=\"lucide lucide-cube icon\"\n                >\n                  <path d=\"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\" />\n                  <polyline points=\"3.27 6.96 12 12.01 20.73 6.96\" />\n                  <line x1=\"12\" y1=\"22.08\" x2=\"12\" y2=\"12\" />\n                </svg>\n                Start Building\n              </button>\n              <button\n                className=\"button-secondary font-geist\"\n                aria-label=\"Learn More\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"#a855f7\"\n                  strokeWidth=\"2\"\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  className=\"lucide lucide-info icon\"\n                >\n                  <circle cx=\"12\" cy=\"12\" r=\"10\" />\n                  <path d=\"M12 16v-4\" />\n                  <path d=\"M12 8h.01\" />\n                </svg>\n                Learn More\n              </button>\n            </div>\n          </section>\n\n          <section\n            className=\"animate-seq animate-delay-500 glass-card-gaming relative mx-auto w-full max-w-md overflow-hidden rounded-3xl border border-fuchsia-500/40 bg-gradient-to-br from-white/10 via-violet-900/40 to-white/5 shadow-2xl ring-1 ring-fuchsia-400/30 backdrop-blur-[18px]\"\n            style={{ fontFamily: 'Roboto, Inter, sans-serif' }}\n          >\n            <div\n              className=\"relative items-center justify-center space-y-6 bg-gradient-to-b from-white/15 via-white/0 to-white/0 pt-6 pr-6 pb-6 pl-6\"\n              style={{\n                backdropFilter: 'blur(25px)',\n                borderBottom: '1px solid rgba(255,255,255,0.09)',\n              }}\n            >\n              <div className=\"mb-8 flex items-center justify-between\">\n                <div className=\"flex items-center gap-2\">\n                  <span className=\"flex h-9 w-9 items-center justify-center rounded-full bg-gradient-to-tr from-fuchsia-400 to-violet-400 shadow-inner ring-2 ring-white/10\">\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      width=\"24\"\n                      height=\"24\"\n                      viewBox=\"0 0 24 24\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      strokeWidth=\"2\"\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      className=\"lucide lucide-user h-5 w-5 text-white\"\n                    >\n                      <path d=\"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2\" />\n                      <circle cx=\"12\" cy=\"7\" r=\"4\" />\n                    </svg>\n                  </span>\n                  <span className=\"font-space-mono text-2xl text-slate-50\">\n                    CryptoX\n                  </span>\n                </div>\n                <span className=\"font-geist inline-flex items-center gap-1 rounded-full border border-fuchsia-400/50 bg-fuchsia-400/30 px-3 py-1 text-xs text-fuchsia-200 uppercase\">\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    width=\"24\"\n                    height=\"24\"\n                    viewBox=\"0 0 24 24\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    strokeWidth=\"2\"\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    className=\"lucide lucide-star h-3.5 w-3.5\"\n                  >\n                    <path d=\"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z\" />\n                  </svg>\n                  Genesis\n                </span>\n              </div>\n\n              <div\n                className=\"controller-animate-group relative mb-8 flex items-center justify-center\"\n                style={{ zIndex: 2 }}\n              >\n                <div\n                  className=\"pointer-events-none absolute -inset-4 blur-[40px]\"\n                  style={{\n                    zIndex: 1,\n                    background:\n                      'radial-gradient(circle at 70% 60%, rgba(232,121,249,0.12) 0%, rgba(168,85,247,0.14) 60%, transparent 100%)',\n                  }}\n                />\n                <img\n                  src=\"https://i.imgur.com/ckSgzLQ.webp\"\n                  alt=\"Crypto wallet holographic card\"\n                  className=\"controller-img pointer-events-none relative z-10 h-auto w-[260px] object-contain drop-shadow-[0_8px_24px_rgba(232,121,249,0.18)] select-none md:w-[340px] lg:w-[390px]\"\n                  draggable={false}\n                  loading=\"lazy\"\n                />\n              </div>\n\n              <div className=\"mb-7 grid grid-cols-3 gap-2 text-center\">\n                <div>\n                  <div className=\"font-space-mono text-2xl text-fuchsia-400\">\n                    $26k\n                  </div>\n                  <div className=\"font-geist text-xs text-slate-50 uppercase\">\n                    Portfolio\n                  </div>\n                </div>\n                <div>\n                  <div className=\"font-space-mono text-2xl text-fuchsia-400\">\n                    0.003\n                  </div>\n                  <div className=\"font-geist text-xs text-slate-50 uppercase\">\n                    Gas (ETH)\n                  </div>\n                </div>\n                <div>\n                  <div className=\"font-space-mono text-2xl text-fuchsia-400\">\n                    14%\n                  </div>\n                  <div className=\"font-geist text-xs text-slate-50 uppercase\">\n                    APY\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"mb-6 h-px bg-gradient-to-r from-fuchsia-400/10 via-fuchsia-300/20 to-white/10\" />\n\n              <div className=\"mb-7 grid grid-cols-2 gap-x-4 gap-y-5\">\n                {[\n                  { label: 'Layer‑2 Speed', icon: 'zap' },\n                  { label: 'Immutable Ledger', icon: 'vibrate' },\n                  { label: 'On‑chain Voice', icon: 'mic' },\n                  { label: 'Fast Withdrawals', icon: 'usb' },\n                ].map(({ label, icon }, idx) => (\n                  <div key={idx} className=\"flex items-center gap-3\">\n                    <span className=\"flex h-9 w-9 items-center justify-center rounded-lg border border-fuchsia-400/30 bg-violet-800/30 text-fuchsia-100 shadow-inner\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        width=\"24\"\n                        height=\"24\"\n                        viewBox=\"0 0 24 24\"\n                        fill=\"none\"\n                        stroke=\"currentColor\"\n                        strokeWidth=\"2\"\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        className={`lucide lucide-${icon} h-5 w-5`}\n                      >\n                        {icon === 'zap' && (\n                          <path d=\"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z\" />\n                        )}\n                        {icon === 'vibrate' && (\n                          <>\n                            <path d=\"m2 8 2 2-2 2 2 2-2 2\" />\n                            <path d=\"m22 8-2 2 2 2-2 2 2 2\" />\n                            <rect width=\"8\" height=\"14\" x=\"8\" y=\"5\" rx=\"1\" />\n                          </>\n                        )}\n                        {icon === 'mic' && (\n                          <>\n                            <path d=\"M12 19v3\" />\n                            <path d=\"M19 10v2a7 7 0 0 1-14 0v-2\" />\n                            <rect x=\"9\" y=\"2\" width=\"6\" height=\"13\" rx=\"3\" />\n                          </>\n                        )}\n                        {icon === 'usb' && (\n                          <>\n                            <circle cx=\"10\" cy=\"7\" r=\"1\" />\n                            <circle cx=\"4\" cy=\"20\" r=\"1\" />\n                            <path d=\"M4.7 19.3 19 5\" />\n                            <path d=\"m21 3-3 1 2 2Z\" />\n                            <path d=\"M9.26 7.68 5 12l2 5\" />\n                            <path d=\"m10 14 5 2 3.5-3.5\" />\n                            <path d=\"m18 12 1-1 1 1-1 1Z\" />\n                          </>\n                        )}\n                      </svg>\n                    </span>\n                    <span className=\"font-geist text-sm text-white/90\">\n                      {label}\n                    </span>\n                  </div>\n                ))}\n              </div>\n\n              <div className=\"mb-6 h-px bg-gradient-to-r from-fuchsia-400/10 via-fuchsia-300/20 to-white/10\" />\n\n              <div className=\"flex items-center justify-between gap-3\">\n                <button className=\"font-geist flex items-center gap-2 rounded-lg border border-fuchsia-400/40 bg-gradient-to-r from-fuchsia-400/90 to-fuchsia-400/80 px-4 py-2 text-white shadow-md transition hover:scale-105 focus-visible:ring-2 focus-visible:ring-fuchsia-400 active:scale-95\">\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    width=\"24\"\n                    height=\"24\"\n                    viewBox=\"0 0 24 24\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    strokeWidth=\"2\"\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    className=\"lucide lucide-play-circle h-5 w-5\"\n                  >\n                    <circle cx=\"12\" cy=\"12\" r=\"10\" />\n                    <polygon points=\"10 8 16 12 10 16 10 8\" />\n                  </svg>\n                  Swap Now\n                </button>\n                <button className=\"font-geist flex items-center gap-2 rounded-lg border border-fuchsia-400/30 bg-gradient-to-r from-white/10 to-white/5 px-4 py-2 text-fuchsia-200 transition hover:bg-violet-900/15 focus-visible:ring-2 focus-visible:ring-fuchsia-400 active:scale-95\">\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    width=\"24\"\n                    height=\"24\"\n                    viewBox=\"0 0 24 24\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    strokeWidth=\"2\"\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    className=\"lucide lucide-bar-chart-3 h-5 w-5\"\n                  >\n                    <path d=\"M3 3v16a2 2 0 0 0 2 2h16\" />\n                    <path d=\"M18 17V9\" />\n                    <path d=\"M13 17V5\" />\n                    <path d=\"M8 17v-3\" />\n                  </svg>\n                  Stats\n                </button>\n              </div>\n            </div>\n          </section>\n        </div>\n      </main>\n\n      <style jsx>{`\n        /* Entrance Animations */\n        .animate-seq {\n          opacity: 0;\n          filter: blur(24px);\n          transform: translateY(28px) scale(0.98);\n          transition:\n            opacity 0.8s cubic-bezier(0.44, 1.2, 0.6, 1.09),\n            filter 0.8s cubic-bezier(0.33, 1.3, 0.7, 1.1),\n            transform 0.8s cubic-bezier(0.44, 1.2, 0.6, 1.09);\n          will-change: opacity, filter, transform;\n        }\n        .animate-seq.visible {\n          opacity: 1;\n          filter: blur(0);\n          transform: translateY(0) scale(1);\n        }\n        .animate-delay-100 {\n          transition-delay: 0.1s;\n        }\n        .animate-delay-300 {\n          transition-delay: 0.3s;\n        }\n        .animate-delay-500 {\n          transition-delay: 0.5s;\n        }\n\n        /* Responsive tweaks mirroring original media queries */\n        @media (max-width: 1024px) {\n          .lg\\:flex-row {\n            flex-direction: column-reverse !important;\n          }\n          .lg\\:items-start {\n            align-items: center !important;\n          }\n          .lg\\:text-left {\n            text-align: center !important;\n          }\n          .controller-img {\n            width: 260px !important;\n          }\n        }\n        @media (max-width: 500px) {\n          .max-w-md {\n            max-width: 98vw !important;\n          }\n          .w-60 {\n            width: 140px !important;\n          }\n          .px-6 {\n            padding-left: 1rem !important;\n            padding-right: 1rem !important;\n          }\n          .max-w-xl {\n            max-width: 98vw !important;\n          }\n          .px-4 {\n            padding-left: 0.5rem !important;\n            padding-right: 0.5rem !important;\n          }\n          .controller-img {\n            width: 180px !important;\n          }\n        }\n\n        /* Floating wallet (controller) animation */\n        @keyframes controller-float {\n          0% {\n            transform: translateY(0) rotate(-10deg) scale(1);\n          }\n          60% {\n            transform: translateY(-14px) rotate(-15deg) scale(1.06);\n          }\n          100% {\n            transform: translateY(0) rotate(-10deg) scale(1);\n          }\n        }\n        .controller-animate-group:focus-within .controller-img,\n        .controller-animate-group:hover .controller-img {\n          animation-play-state: paused !important;\n          transform: translateY(-7px) rotate(-17deg) scale(1.1) !important;\n          transition: transform 0.3s cubic-bezier(0.4, 2, 0.4, 1);\n          filter: brightness(1.12) drop-shadow(0 12px 36px #e879f9cc);\n        }\n        .controller-img {\n          will-change: transform, filter;\n          transition:\n            transform 0.3s cubic-bezier(0.4, 2, 0.4, 1),\n            filter 0.3s cubic-bezier(0.4, 2, 0.4, 1);\n          outline: none;\n          cursor: pointer;\n          animation: controller-float 2.7s ease-in-out infinite alternate;\n        }\n\n        /* Glass card shadow tweak for violet glow */\n        .glass-card-gaming {\n          box-shadow:\n            0 8px 32px 0 rgba(232, 121, 249, 0.23),\n            0 2px 8px 0 rgba(168, 85, 247, 0.09) inset;\n        }\n\n        /* Primary & secondary button styles (re‑using original classes but with purple accents) */\n        .button {\n          position: relative;\n          transition: all 0.3s ease-in-out;\n          box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.2);\n          padding-block: 0.5rem;\n          padding-inline: 1.25rem;\n          background-color: rgb(139 92 246);\n          border-radius: 9999px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          cursor: pointer;\n          color: #ffff;\n          gap: 10px;\n          font-weight: 600;\n          border: 3px solid #a855f7;\n          outline: none;\n          overflow: hidden;\n          font-size: 15px;\n        }\n        .button .icon {\n          width: 24px;\n          height: 24px;\n          transition: all 0.3s ease-in-out;\n        }\n        .button:hover {\n          transform: scale(1.05);\n          border-color: #c084fc;\n        }\n        .button:hover .icon {\n          transform: translate(4px);\n        }\n        .button:hover::before {\n          animation: shine 1.5s ease-out infinite;\n        }\n        .button::before {\n          content: '';\n          position: absolute;\n          width: 100px;\n          height: 100%;\n          background-image: linear-gradient(\n            120deg,\n            rgba(255, 255, 255, 0) 30%,\n            rgba(255, 255, 255, 0.8),\n            rgba(255, 255, 255, 0) 70%\n          );\n          top: 0;\n          left: -100px;\n          opacity: 0.6;\n        }\n        @keyframes shine {\n          0% {\n            left: -100px;\n          }\n          60% {\n            left: 100%;\n          }\n          to {\n            left: 100%;\n          }\n        }\n        .button-secondary {\n          position: relative;\n          transition: all 0.2s cubic-bezier(0.4, 2, 0.4, 1);\n          box-shadow: none;\n          padding-block: 0.5rem;\n          padding-inline: 1.1rem;\n          background: transparent;\n          border-radius: 9999px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          cursor: pointer;\n          color: #d8b4fe;\n          gap: 10px;\n          font-weight: 600;\n          border: 3px solid #a855f7;\n          outline: none;\n          font-size: 15px;\n        }\n        .button-secondary .icon {\n          width: 22px;\n          height: 22px;\n          transition: all 0.2s;\n          stroke: #a855f7 !important;\n        }\n        .button-secondary:hover {\n          background-color: rgba(232, 121, 249, 0.08);\n          color: #d8b4fe;\n          border-color: #d8b4fe;\n        }\n      `}</style>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/hero/web3-hero.tsx", "target": "components/mvpblocks/web3-hero.tsx"}]}