{"name": "toc-dialog", "type": "registry:block", "dependencies": ["react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/dialog.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { useRef, useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n  DialogTrigger,\n} from '@/components/ui/dialog';\n\nexport default function TocDialog() {\n  const [hasReadToBottom, setHasReadToBottom] = useState(false);\n  const contentRef = useRef<HTMLDivElement>(null);\n\n  const handleScroll = () => {\n    const content = contentRef.current;\n    if (!content) return;\n\n    const scrollPercentage =\n      content.scrollTop / (content.scrollHeight - content.clientHeight);\n    if (scrollPercentage >= 0.99 && !hasReadToBottom) {\n      setHasReadToBottom(true);\n    }\n  };\n\n  return (\n    <Dialog>\n      <DialogTrigger asChild>\n        <Button variant=\"outline\">Terms & Conditions</Button>\n      </DialogTrigger>\n      <DialogContent className=\"flex flex-col gap-0 p-0 sm:max-h-[min(640px,80vh)] sm:max-w-lg [&>button:last-child]:top-3.5\">\n        <DialogHeader className=\"contents space-y-0 text-left\">\n          <DialogTitle className=\"border-b px-6 py-4 text-base\">\n            Terms & Conditions\n          </DialogTitle>\n          <div\n            ref={contentRef}\n            onScroll={handleScroll}\n            className=\"overflow-y-auto\"\n          >\n            <DialogDescription asChild>\n              <div className=\"px-6 py-4\">\n                <div className=\"[&_strong]:text-foreground space-y-4 [&_strong]:font-semibold\">\n                  <div className=\"space-y-4\">\n                    <div className=\"space-y-1\">\n                      <p>\n                        <strong>Acceptance of Terms</strong>\n                      </p>\n                      <p>\n                        By accessing and using this website, users agree to\n                        comply with and be bound by these Terms of Service.\n                        Users who do not agree with these terms should\n                        discontinue use of the website immediately.\n                      </p>\n                    </div>\n\n                    <div className=\"space-y-1\">\n                      <p>\n                        <strong>User Account Responsibilities</strong>\n                      </p>\n                      <p>\n                        Users are responsible for maintaining the\n                        confidentiality of their account credentials. Any\n                        activities occurring under a user&lsquo;s account are\n                        the sole responsibility of the account holder. Users\n                        must notify the website administrators immediately of\n                        any unauthorized account access.\n                      </p>\n                    </div>\n\n                    <div className=\"space-y-1\">\n                      <p>\n                        <strong>Content Usage and Restrictions</strong>\n                      </p>\n                      <p>\n                        The website and its original content are protected by\n                        intellectual property laws. Users may not reproduce,\n                        distribute, modify, create derivative works, or\n                        commercially exploit any content without explicit\n                        written permission from the website owners.\n                      </p>\n                    </div>\n\n                    <div className=\"space-y-1\">\n                      <p>\n                        <strong>Limitation of Liability</strong>\n                      </p>\n                      <p>\n                        The website provides content &ldquo;as is&ldquo; without\n                        any warranties. The website owners shall not be liable\n                        for direct, indirect, incidental, consequential, or\n                        punitive damages arising from user interactions with the\n                        platform.\n                      </p>\n                    </div>\n\n                    <div className=\"space-y-1\">\n                      <p>\n                        <strong>User Conduct Guidelines</strong>\n                      </p>\n                      <ul className=\"list-disc pl-6\">\n                        <li>Not upload harmful or malicious content</li>\n                        <li>Respect the rights of other users</li>\n                        <li>\n                          Avoid activities that could disrupt website\n                          functionality\n                        </li>\n                        <li>\n                          Comply with applicable local and international laws\n                        </li>\n                      </ul>\n                    </div>\n\n                    <div className=\"space-y-1\">\n                      <p>\n                        <strong>Modifications to Terms</strong>\n                      </p>\n                      <p>\n                        The website reserves the right to modify these terms at\n                        any time. Continued use of the website after changes\n                        constitutes acceptance of the new terms.\n                      </p>\n                    </div>\n\n                    <div className=\"space-y-1\">\n                      <p>\n                        <strong>Termination Clause</strong>\n                      </p>\n                      <p>\n                        The website may terminate or suspend user access without\n                        prior notice for violations of these terms or for any\n                        other reason deemed appropriate by the administration.\n                      </p>\n                    </div>\n\n                    <div className=\"space-y-1\">\n                      <p>\n                        <strong>Governing Law</strong>\n                      </p>\n                      <p>\n                        These terms are governed by the laws of the jurisdiction\n                        where the website is primarily operated, without regard\n                        to conflict of law principles.\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </DialogDescription>\n          </div>\n        </DialogHeader>\n        <DialogFooter className=\"border-t px-6 py-4 sm:items-center\">\n          {!hasReadToBottom && (\n            <span className=\"text-muted-foreground grow text-xs max-sm:text-center\">\n              Read all terms before accepting.\n            </span>\n          )}\n          <DialogClose asChild>\n            <Button type=\"button\" variant=\"outline\">\n              Cancel\n            </Button>\n          </DialogClose>\n          <DialogClose asChild>\n            <Button type=\"button\" disabled={!hasReadToBottom}>\n              I agree\n            </Button>\n          </DialogClose>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  );\n}\n", "path": "/components/mvpblocks/basics/modals/toc-dialog.tsx", "target": "components/mvpblocks/toc-dialog.tsx"}]}