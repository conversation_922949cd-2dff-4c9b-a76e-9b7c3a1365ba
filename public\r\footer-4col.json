{"name": "footer-4col", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "import {\n  Dribbble,\n  Facebook,\n  Github,\n  Instagram,\n  Mail,\n  MapPin,\n  Phone,\n  Twitter,\n} from 'lucide-react';\nimport Link from 'next/link';\n\nconst data = {\n  facebookLink: 'https://facebook.com/mvpblocks',\n  instaLink: 'https://instagram.com/mvpblocks',\n  twitterLink: 'https://twitter.com/mvpblocks',\n  githubLink: 'https://github.com/mvpblocks',\n  dribbbleLink: 'https://dribbble.com/mvpblocks',\n  services: {\n    webdev: '/web-development',\n    webdesign: '/web-design',\n    marketing: '/marketing',\n    googleads: '/google-ads',\n  },\n  about: {\n    history: '/company-history',\n    team: '/meet-the-team',\n    handbook: '/employee-handbook',\n    careers: '/careers',\n  },\n  help: {\n    faqs: '/faqs',\n    support: '/support',\n    livechat: '/live-chat',\n  },\n  contact: {\n    email: '<EMAIL>',\n    phone: '+91 8637373116',\n    address: 'Kolkata, West Bengal, India',\n  },\n  company: {\n    name: 'Mvpblocks',\n    description:\n      'Building beautiful and functional web experiences with modern technologies. We help startups and businesses create their digital presence.',\n    logo: '/logo.webp',\n  },\n};\n\nconst socialLinks = [\n  { icon: Facebook, label: 'Facebook', href: data.facebookLink },\n  { icon: Instagram, label: 'Instagram', href: data.instaLink },\n  { icon: Twitter, label: 'Twitter', href: data.twitterLink },\n  { icon: Github, label: 'GitHub', href: data.githubLink },\n  { icon: Dribbble, label: 'Dribbble', href: data.dribbbleLink },\n];\n\nconst aboutLinks = [\n  { text: 'Company History', href: data.about.history },\n  { text: 'Meet the Team', href: data.about.team },\n  { text: 'Employee Handbook', href: data.about.handbook },\n  { text: 'Careers', href: data.about.careers },\n];\n\nconst serviceLinks = [\n  { text: 'Web Development', href: data.services.webdev },\n  { text: 'Web Design', href: data.services.webdesign },\n  { text: 'Marketing', href: data.services.marketing },\n  { text: 'Google Ads', href: data.services.googleads },\n];\n\nconst helpfulLinks = [\n  { text: 'FAQs', href: data.help.faqs },\n  { text: 'Support', href: data.help.support },\n  { text: 'Live Chat', href: data.help.livechat, hasIndicator: true },\n];\n\nconst contactInfo = [\n  { icon: Mail, text: data.contact.email },\n  { icon: Phone, text: data.contact.phone },\n  { icon: MapPin, text: data.contact.address, isAddress: true },\n];\n\nexport default function Footer4Col() {\n  return (\n    <footer className=\"bg-secondary dark:bg-secondary/20 mt-16 w-full place-self-end rounded-t-xl\">\n      <div className=\"mx-auto max-w-screen-xl px-4 pt-16 pb-6 sm:px-6 lg:px-8 lg:pt-24\">\n        <div className=\"grid grid-cols-1 gap-8 lg:grid-cols-3\">\n          <div>\n            <div className=\"text-primary flex justify-center gap-2 sm:justify-start\">\n              <img\n                src={data.company.logo || '/placeholder.svg'}\n                alt=\"logo\"\n                className=\"h-8 w-8 rounded-full\"\n              />\n              <span className=\"text-2xl font-semibold\">\n                {data.company.name}\n              </span>\n            </div>\n\n            <p className=\"text-foreground/50 mt-6 max-w-md text-center leading-relaxed sm:max-w-xs sm:text-left\">\n              {data.company.description}\n            </p>\n\n            <ul className=\"mt-8 flex justify-center gap-6 sm:justify-start md:gap-8\">\n              {socialLinks.map(({ icon: Icon, label, href }) => (\n                <li key={label}>\n                  <Link\n                    href={href}\n                    className=\"text-primary hover:text-primary/80 transition\"\n                  >\n                    <span className=\"sr-only\">{label}</span>\n                    <Icon className=\"size-6\" />\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 md:grid-cols-4 lg:col-span-2\">\n            <div className=\"text-center sm:text-left\">\n              <p className=\"text-lg font-medium\">About Us</p>\n              <ul className=\"mt-8 space-y-4 text-sm\">\n                {aboutLinks.map(({ text, href }) => (\n                  <li key={text}>\n                    <a\n                      className=\"text-secondary-foreground/70 transition\"\n                      href={href}\n                    >\n                      {text}\n                    </a>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div className=\"text-center sm:text-left\">\n              <p className=\"text-lg font-medium\">Our Services</p>\n              <ul className=\"mt-8 space-y-4 text-sm\">\n                {serviceLinks.map(({ text, href }) => (\n                  <li key={text}>\n                    <a\n                      className=\"text-secondary-foreground/70 transition\"\n                      href={href}\n                    >\n                      {text}\n                    </a>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div className=\"text-center sm:text-left\">\n              <p className=\"text-lg font-medium\">Helpful Links</p>\n              <ul className=\"mt-8 space-y-4 text-sm\">\n                {helpfulLinks.map(({ text, href, hasIndicator }) => (\n                  <li key={text}>\n                    <a\n                      href={href}\n                      className={`${\n                        hasIndicator\n                          ? 'group flex justify-center gap-1.5 sm:justify-start'\n                          : 'text-secondary-foreground/70 transition'\n                      }`}\n                    >\n                      <span className=\"text-secondary-foreground/70 transition\">\n                        {text}\n                      </span>\n                      {hasIndicator && (\n                        <span className=\"relative flex size-2\">\n                          <span className=\"bg-primary absolute inline-flex h-full w-full animate-ping rounded-full opacity-75\" />\n                          <span className=\"bg-primary relative inline-flex size-2 rounded-full\" />\n                        </span>\n                      )}\n                    </a>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div className=\"text-center sm:text-left\">\n              <p className=\"text-lg font-medium\">Contact Us</p>\n              <ul className=\"mt-8 space-y-4 text-sm\">\n                {contactInfo.map(({ icon: Icon, text, isAddress }) => (\n                  <li key={text}>\n                    <a\n                      className=\"flex items-center justify-center gap-1.5 sm:justify-start\"\n                      href=\"#\"\n                    >\n                      <Icon className=\"text-primary size-5 shrink-0 shadow-sm\" />\n                      {isAddress ? (\n                        <address className=\"text-secondary-foreground/70 -mt-0.5 flex-1 not-italic transition\">\n                          {text}\n                        </address>\n                      ) : (\n                        <span className=\"text-secondary-foreground/70 flex-1 transition\">\n                          {text}\n                        </span>\n                      )}\n                    </a>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"mt-12 border-t pt-6\">\n          <div className=\"text-center sm:flex sm:justify-between sm:text-left\">\n            <p className=\"text-sm\">\n              <span className=\"block sm:inline\">All rights reserved.</span>\n            </p>\n\n            <p className=\"text-secondary-foreground/70 mt-4 text-sm transition sm:order-first sm:mt-0\">\n              &copy; 2025 {data.company.name}\n            </p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n", "path": "/components/mvpblocks/required/footers/footer-4col.tsx", "target": "components/mvpblocks/footer-4col.tsx"}]}