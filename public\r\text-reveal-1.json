{"name": "text-reveal-1", "type": "registry:block", "dependencies": [], "registryDependencies": ["https://blocks.mvp-subha.me/r/text-reveal.json", "https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "import { TextReveal } from '@/components/ui/text-reveal';\nimport { cn } from '@/lib/utils';\nimport { <PERSON>ei<PERSON> } from 'next/font/google';\n\nconst geist = Geist({\n  subsets: ['latin'],\n  variable: '--font-geist',\n  weight: '400',\n});\n\nexport default function TextRevealLetters() {\n  return (\n    <TextReveal\n      className={cn(\n        `bg-primary from-foreground to-primary via-rose-200 bg-clip-text text-6xl font-bold text-transparent dark:bg-gradient-to-b`,\n        geist.className,\n      )}\n      from=\"bottom\"\n      split=\"letter\"\n    >\n      Welcome to Mvpblocks!\n    </TextReveal>\n  );\n}\n", "path": "/components/mvpblocks/text-animations/text-reveal-1.tsx", "target": "components/mvpblocks/text-reveal-1.tsx"}]}