{"name": "globe1", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/globe.json"], "files": [{"type": "registry:block", "content": "import Earth from '@/components/ui/globe';\n\nexport default function Globe1() {\n  return (\n    <>\n      <div className=\"bg-background flex flex-col items-center justify-center overflow-hidden\">\n        <article className=\"border-border relative mx-auto my-8 max-w-[500px] rounded-xl border p-5 text-center\">\n          <div className=\"relative z-10\">\n            <h1 className=\"text-7xl leading-[100%] font-semibold tracking-tighter\">\n              Welcome to Mvpblocks\n            </h1>\n            {/* Normalized RGB values i.e (RGB or color / 255) */}\n            <Earth\n              baseColor={[1, 0, 0.3]}\n              markerColor={[1, 0, 0.33]}\n              glowColor={[1, 0, 0.3]}\n            />\n          </div>\n        </article>\n      </div>\n    </>\n  );\n}\n", "path": "/components/mvpblocks/creative/globe1.tsx", "target": "components/mvpblocks/globe1.tsx"}]}