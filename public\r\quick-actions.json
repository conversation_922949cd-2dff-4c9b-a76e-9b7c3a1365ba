{"name": "quick-actions", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { memo } from 'react';\nimport { motion } from 'framer-motion';\nimport { Button } from '@/components/ui/button';\nimport { Users, BarChart3, Download, Settings } from 'lucide-react';\n\ninterface QuickActionsProps {\n  onAddUser: () => void;\n  onExport: () => void;\n}\n\nconst actions = [\n  {\n    icon: Users,\n    label: 'Add New User',\n    color: 'blue',\n    shortcut: 'Ctrl+N',\n    action: 'addUser',\n  },\n  {\n    icon: BarChart3,\n    label: 'View Analytics',\n    color: 'green',\n    shortcut: 'Ctrl+A',\n    action: 'analytics',\n  },\n  {\n    icon: Download,\n    label: 'Export Data',\n    color: 'purple',\n    shortcut: 'Ctrl+E',\n    action: 'export',\n  },\n  {\n    icon: Settings,\n    label: 'System Settings',\n    color: 'orange',\n    shortcut: 'Ctrl+S',\n    action: 'settings',\n  },\n];\n\nexport const QuickActions = memo(\n  ({ onAddUser, onExport }: QuickActionsProps) => {\n    const handleAction = (action: string) => {\n      switch (action) {\n        case 'addUser':\n          onAddUser();\n          break;\n        case 'analytics':\n          console.log('Viewing analytics...');\n          break;\n        case 'export':\n          onExport();\n          break;\n        case 'settings':\n          console.log('Opening settings...');\n          break;\n      }\n    };\n\n    return (\n      <div className=\"border-border bg-card/40 rounded-xl border p-6\">\n        <h3 className=\"mb-4 text-xl font-semibold\">Quick Actions</h3>\n        <div className=\"space-y-3\">\n          {actions.map((action, index) => {\n            const Icon = action.icon;\n            return (\n              <motion.div\n                key={action.label}\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <Button\n                  variant=\"outline\"\n                  className={`h-12 w-full justify-start hover:bg-${action.color}-500/10 hover:border-${action.color}-500/50 transition-all duration-200`}\n                  onClick={() => handleAction(action.action)}\n                >\n                  <Icon className={`mr-3 h-5 w-5 text-${action.color}-500`} />\n                  <span className=\"font-medium\">{action.label}</span>\n                  <div className=\"text-muted-foreground ml-auto text-xs\">\n                    {action.shortcut}\n                  </div>\n                </Button>\n              </motion.div>\n            );\n          })}\n        </div>\n      </div>\n    );\n  },\n);\n\nQuickActions.displayName = 'QuickActions';\n", "path": "/components/mvpblocks/dashboards/admin-dashboard-1/ui/quick-actions.tsx", "target": "components/mvpblocks/ui/quick-actions.tsx"}]}