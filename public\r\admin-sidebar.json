{"name": "admin-sidebar", "type": "registry:block", "dependencies": ["lucide-react", "next-themes", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/sidebar.json", "https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/input.json", "https://blocks.mvp-subha.me/r/separator.json", "https://blocks.mvp-subha.me/r/sheet.json", "https://blocks.mvp-subha.me/r/skeleton.json", "https://blocks.mvp-subha.me/r/tooltip.json", "https://blocks.mvp-subha.me/r/use-mobile.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { memo } from 'react';\nimport { useTheme } from 'next-themes';\nimport Link from 'next/link';\nimport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarMenu,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarRail,\n} from '@/components/ui/sidebar';\nimport {\n  LayoutDashboard,\n  Users,\n  BarChart3,\n  FileText,\n  Activity,\n  Database,\n  Shield,\n  Zap,\n  Bell,\n  Settings,\n  Moon,\n  Sun,\n  User,\n} from 'lucide-react';\n\nconst menuItems = [\n  { title: 'Dashboard', icon: LayoutDashboard, href: '#dashboard' },\n  { title: 'Analytics', icon: BarChart3, href: '#analytics' },\n  { title: 'Users', icon: Users, href: '#users' },\n  { title: 'Content', icon: FileText, href: '#content' },\n  { title: 'Activity', icon: Activity, href: '#activity' },\n  { title: 'Database', icon: Database, href: '#database' },\n  { title: 'Security', icon: Shield, href: '#security' },\n  { title: 'Performance', icon: Zap, href: '#performance' },\n  { title: 'Notifications', icon: Bell, href: '#notifications' },\n  { title: 'Settings', icon: Settings, href: '#settings' },\n];\n\nexport const AdminSidebar = memo(() => {\n  const { theme, setTheme } = useTheme();\n\n  return (\n    <Sidebar collapsible=\"icon\">\n      <SidebarHeader>\n        <SidebarMenu>\n          <SidebarMenuItem>\n            <SidebarMenuButton size=\"lg\" asChild>\n              <Link href=\"#dashboard\">\n                <div className=\"bg-primary text-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg\">\n                  <LayoutDashboard className=\"h-5 w-5\" />\n                </div>\n                <div className=\"grid flex-1 text-left text-sm leading-tight\">\n                  <span className=\"truncate font-semibold\">TechCorp</span>\n                  <span className=\"truncate text-xs\">Admin Panel</span>\n                </div>\n              </Link>\n            </SidebarMenuButton>\n          </SidebarMenuItem>\n        </SidebarMenu>\n      </SidebarHeader>\n\n      <SidebarContent>\n        <SidebarGroup>\n          <SidebarGroupLabel>Navigation</SidebarGroupLabel>\n          <SidebarGroupContent>\n            <SidebarMenu>\n              {menuItems.map((item) => {\n                const Icon = item.icon;\n                return (\n                  <SidebarMenuItem key={item.href}>\n                    <SidebarMenuButton asChild>\n                      <Link href={item.href}>\n                        <Icon />\n                        <span>{item.title}</span>\n                      </Link>\n                    </SidebarMenuButton>\n                  </SidebarMenuItem>\n                );\n              })}\n            </SidebarMenu>\n          </SidebarGroupContent>\n        </SidebarGroup>\n      </SidebarContent>\n\n      <SidebarFooter>\n        <SidebarMenu>\n          <SidebarMenuItem>\n            <SidebarMenuButton\n              onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}\n            >\n              {theme === 'dark' ? <Sun /> : <Moon />}\n              <span>{theme === 'dark' ? 'Light Mode' : 'Dark Mode'}</span>\n            </SidebarMenuButton>\n          </SidebarMenuItem>\n          <SidebarMenuItem>\n            <SidebarMenuButton asChild>\n              <Link href=\"#profile\">\n                <User />\n                <span>Admin Profile</span>\n              </Link>\n            </SidebarMenuButton>\n          </SidebarMenuItem>\n        </SidebarMenu>\n      </SidebarFooter>\n      <SidebarRail />\n    </Sidebar>\n  );\n});\n\nAdminSidebar.displayName = 'AdminSidebar';\n", "path": "/components/mvpblocks/dashboards/admin-dashboard-1/ui/admin-sidebar.tsx", "target": "components/mvpblocks/ui/admin-sidebar.tsx"}]}