{"name": "glow-card", "type": "registry:block", "dependencies": ["react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\n\nexport default function GlowingCard() {\n  return (\n    <>\n      <div className=\"card\">\n        I glow :)\n        <div className=\"glow\" />\n      </div>\n\n      <style jsx>{`\n        @property --a {\n          syntax: '<angle>';\n          initial-value: 0deg;\n          inherits: false;\n        }\n\n        @keyframes a {\n          to {\n            --a: 1turn;\n          }\n        }\n\n        .card {\n          position: relative;\n          overflow: hidden;\n          width: min(12.5em, 80vmin);\n          aspect-ratio: 1;\n          border-radius: 0.5em;\n\n          display: grid;\n          place-self: center;\n          place-content: center;\n          padding: 0.5em;\n          color: #ededed;\n          font: clamp(1em, 2vw + 2vh, 2em) sans-serif;\n          text-align: center;\n          text-transform: uppercase;\n          text-wrap: balance;\n        }\n\n        .glow {\n          content: '';\n          position: absolute;\n          z-index: -1;\n          inset: -1em;\n          border: solid 1.25em;\n          border-image: conic-gradient(\n              from var(--a),\n              #669900,\n              #99cc33,\n              #ccee66,\n              #006699,\n              #3399cc,\n              #990066,\n              #cc3399,\n              #ff6600,\n              #ff9900,\n              #ffcc00,\n              #669900\n            )\n            1;\n          filter: blur(0.75em);\n          animation: a 4s linear infinite;\n          pointer-events: none;\n        }\n      `}</style>\n    </>\n  );\n}\n", "path": "/components/mvpblocks/cards/glow/glow-card.tsx", "target": "components/mvpblocks/glow-card.tsx"}]}