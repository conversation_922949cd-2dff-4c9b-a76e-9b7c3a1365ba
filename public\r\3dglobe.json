{"name": "3dglobe", "type": "registry:block", "dependencies": ["framer-motion"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\n\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\n\nexport default function Globe3D() {\n  return (\n    <section\n      className=\"relative w-full overflow-hidden bg-[#0a0613] pt-32 pb-10 font-light text-white antialiased md:pt-20 md:pb-16\"\n      style={{\n        background: 'linear-gradient(135deg, #0a0613 0%, #150d27 100%)',\n      }}\n    >\n      <div\n        className=\"absolute top-0 right-0 h-1/2 w-1/2\"\n        style={{\n          background:\n            'radial-gradient(circle at 70% 30%, rgba(155, 135, 245, 0.15) 0%, rgba(13, 10, 25, 0) 60%)',\n        }}\n      />\n      <div\n        className=\"absolute top-0 left-0 h-1/2 w-1/2 -scale-x-100\"\n        style={{\n          background:\n            'radial-gradient(circle at 70% 30%, rgba(155, 135, 245, 0.15) 0%, rgba(13, 10, 25, 0) 60%)',\n        }}\n      />\n\n      <div className=\"relative z-10 container mx-auto max-w-2xl px-4 text-center md:max-w-4xl md:px-6 lg:max-w-7xl\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, ease: 'easeOut' }}\n        >\n          <span className=\"mb-6 inline-block rounded-full border border-[#9b87f5]/30 px-3 py-1 text-xs text-[#9b87f5]\">\n            NEXT GENERATION OF CRYPTO TRADING\n          </span>\n          <h1 className=\"mx-auto mb-6 max-w-4xl text-4xl font-light md:text-5xl lg:text-7xl\">\n            Trade Smarter with{' '}\n            <span className=\"text-[#9b87f5]\">AI-Powered</span> Crypto Insights\n          </h1>\n          <p className=\"mx-auto mb-10 max-w-2xl text-lg text-white/60 md:text-xl\">\n            Lunexa combines artificial intelligence with cutting-edge trading\n            strategies to help you maximize your crypto investments with\n            precision and ease.\n          </p>\n\n          <div className=\"mb-10 flex flex-col items-center justify-center gap-4 sm:mb-0 sm:flex-row\">\n            <Link\n              href=\"/docs/get-started\"\n              className=\"neumorphic-button hover:shadow-[0_0_20px_rgba(155, 135, 245, 0.5)] relative w-full overflow-hidden rounded-full border border-white/10 bg-gradient-to-b from-white/10 to-white/5 px-8 py-4 text-white shadow-lg transition-all duration-300 hover:border-[#9b87f5]/30 sm:w-auto\"\n            >\n              Get Started\n            </Link>\n            <a\n              href=\"#how-it-works\"\n              className=\"flex w-full items-center justify-center gap-2 text-white/70 transition-colors hover:text-white sm:w-auto\"\n            >\n              <span>Learn how it works</span>\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                width=\"16\"\n                height=\"16\"\n                viewBox=\"0 0 24 24\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n              >\n                <path d=\"m6 9 6 6 6-6\"></path>\n              </svg>\n            </a>\n          </div>\n        </motion.div>\n        <motion.div\n          className=\"relative\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.8, ease: 'easeOut', delay: 0.3 }}\n        >\n          <div className=\"relative flex h-40 w-full overflow-hidden md:h-64\">\n            <img\n              src=\"https://blocks.mvp-subha.me/assets/earth.webp\"\n              alt=\"Earth\"\n              className=\"absolute top-0 left-1/2 -z-10 mx-auto -translate-x-1/2 px-4 opacity-80\"\n            />\n          </div>\n          <div className=\"relative z-10 mx-auto max-w-5xl overflow-hidden rounded-lg shadow-[0_0_50px_rgba(155,135,245,0.2)]\">\n            <img\n              src=\"https://blocks.mvp-subha.me/assets/lunexa-db.webp\"\n              alt=\"Lunexa Dashboard\"\n              width={1920}\n              height={1080}\n              className=\"h-auto w-full rounded-lg border border-white/10\"\n            />\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/hero/3dglobe.tsx", "target": "components/mvpblocks/3dglobe.tsx"}]}