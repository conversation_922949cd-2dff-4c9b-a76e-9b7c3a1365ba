{"name": "spiral-loader", "type": "registry:block", "dependencies": ["framer-motion"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "import { motion } from 'framer-motion';\n\nexport default function SpiralLoader() {\n  const dots = 8;\n  const radius = 20;\n\n  return (\n    <div className=\"relative h-16 w-16\">\n      {[...Array(dots)].map((_, index) => {\n        const angle = (index / dots) * (2 * Math.PI);\n        const x = radius * Math.cos(angle);\n        const y = radius * Math.sin(angle);\n\n        return (\n          <motion.div\n            key={index}\n            className=\"absolute h-3 w-3 rounded-full bg-red-500\"\n            style={{\n              left: `calc(50% + ${x}px)`,\n              top: `calc(50% + ${y}px)`,\n            }}\n            animate={{\n              scale: [0, 1, 0],\n              opacity: [0, 1, 0],\n            }}\n            transition={{\n              duration: 1.5,\n              repeat: Infinity,\n              delay: (index / dots) * 1.5,\n              ease: 'easeInOut',\n            }}\n          />\n        );\n      })}\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/basics/loaders/spiral-loader.tsx", "target": "components/mvpblocks/spiral-loader.tsx"}]}