{"name": "app-hero", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { useEffect, useState } from 'react';\nimport { easeInOut, motion, spring } from 'framer-motion';\nimport { Button } from '@/components/ui/button';\nimport {\n  ArrowRight,\n  Database,\n  Sparkles,\n  Zap,\n  ArrowUpRight,\n} from 'lucide-react';\n\nexport default function AppHero() {\n  // State for animated counters\n  const [stats, setStats] = useState({\n    users: 0,\n    transactions: 0,\n    networks: 0,\n  });\n\n  // Animation to count up numbers\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setStats((prev) => {\n        const newUsers = prev.users >= 20000 ? 20000 : prev.users + 500;\n        const newTransactions =\n          prev.transactions >= 1500000 ? 1500000 : prev.transactions + 37500;\n        const newNetworks = prev.networks >= 40 ? 40 : prev.networks + 1;\n\n        if (\n          newUsers === 20000 &&\n          newTransactions === 1500000 &&\n          newNetworks === 40\n        ) {\n          clearInterval(interval);\n        }\n\n        return {\n          users: newUsers,\n          transactions: newTransactions,\n          networks: newNetworks,\n        };\n      });\n    }, 50);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n        delayChildren: 0.3,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: { type: spring, stiffness: 100 },\n    },\n  };\n\n  // Floating animation for the cube\n  const floatingAnimation = {\n    y: [0, -10, 0],\n    transition: {\n      duration: 4,\n      repeat: Infinity,\n      ease: easeInOut,\n    },\n  };\n\n  // Rotation animation for the orbital ring\n  const rotateAnimation = {\n    rotate: 360,\n    transition: {\n      duration: 20,\n      repeat: Infinity,\n      ease: 'linear',\n    },\n  };\n\n  // Glowing effect animation\n  const glowAnimation = {\n    opacity: [0.5, 0.8, 0.5],\n    scale: [1, 1.05, 1],\n    transition: {\n      duration: 3,\n      repeat: Infinity,\n      ease: easeInOut,\n    },\n  };\n\n  // Tooltip animation\n  const tooltipVariants = {\n    hidden: { opacity: 0, scale: 0.8 },\n    visible: {\n      opacity: 1,\n      scale: 1,\n      transition: {\n        type: spring,\n        stiffness: 100,\n        delay: 1.2,\n      },\n    },\n  };\n\n  // Badge pulse animation\n  const badgePulse = {\n    scale: [1, 1.05, 1],\n    opacity: [0.9, 1, 0.9],\n    transition: {\n      duration: 2,\n      repeat: Infinity,\n      ease: 'easeInOut',\n    },\n  };\n\n  return (\n    <section className=\"relative flex min-h-screen w-full flex-col items-center overflow-hidden bg-black py-16 text-white sm:px-6 lg:px-8 lg:py-2\">\n      <div className=\"absolute inset-0 z-0 h-full w-full rotate-180 items-center px-5 py-24 opacity-80 [background:radial-gradient(125%_125%_at_50%_10%,#000_40%,#63e_100%)]\"></div>\n      <svg\n        id=\"noice\"\n        className=\"absolute inset-0 z-10 h-full w-full opacity-30\"\n      >\n        <filter id=\"noise-filter\">\n          <feTurbulence\n            type=\"fractalNoise\"\n            baseFrequency=\"1.34\"\n            numOctaves=\"4\"\n            stitchTiles=\"stitch\"\n          ></feTurbulence>\n          <feColorMatrix type=\"saturate\" values=\"0\"></feColorMatrix>\n          <feComponentTransfer>\n            <feFuncR type=\"linear\" slope=\"0.46\"></feFuncR>\n            <feFuncG type=\"linear\" slope=\"0.46\"></feFuncG>\n            <feFuncB type=\"linear\" slope=\"0.47\"></feFuncB>\n            <feFuncA type=\"linear\" slope=\"0.37\"></feFuncA>\n          </feComponentTransfer>\n          <feComponentTransfer>\n            <feFuncR type=\"linear\" slope=\"1.47\" intercept=\"-0.23\" />\n            <feFuncG type=\"linear\" slope=\"1.47\" intercept=\"-0.23\" />\n            <feFuncB type=\"linear\" slope=\"1.47\" intercept=\"-0.23\" />\n          </feComponentTransfer>\n        </filter>\n        <rect width=\"100%\" height=\"100%\" filter=\"url(#noise-filter)\"></rect>\n      </svg>\n      {/* Background effects */}\n      <div className=\"absolute inset-0 z-0\">\n        {/* Radial gradient */}\n        <div className=\"absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-indigo-900/30 via-black/70 to-gray-950 blur-3xl\"></div>\n\n        {/* Grid pattern */}\n        <div className=\"absolute inset-0 opacity-10\">\n          <div className=\"h-full w-full bg-[linear-gradient(to_right,rgba(255,255,255,0.22)_1px,transparent_1px),linear-gradient(to_bottom,rgba(255,255,255,0.2)_1px,transparent_1px)] bg-[size:4rem_4rem]\"></div>\n        </div>\n\n        {/* Enhanced glow spots */}\n        <div className=\"absolute top-20 -left-20 h-60 w-60 rounded-full bg-purple-600/20 blur-[100px]\"></div>\n        <div className=\"absolute -right-20 bottom-20 h-60 w-60 rounded-full bg-blue-600/20 blur-[100px]\"></div>\n        <motion.div\n          animate={glowAnimation}\n          className=\"absolute top-1/3 left-1/4 h-40 w-40 rounded-full bg-indigo-500/10 blur-[80px]\"\n        ></motion.div>\n        <motion.div\n          animate={glowAnimation}\n          className=\"absolute right-1/4 bottom-1/3 h-40 w-40 rounded-full bg-purple-500/10 blur-[80px]\"\n        ></motion.div>\n\n        {/* Particle effects - subtle dots */}\n        <div className=\"absolute inset-0 opacity-20\">\n          {Array.from({ length: 20 }).map((_, i) => (\n            <motion.div\n              key={i}\n              className=\"absolute h-1 w-1 rounded-full bg-white\"\n              style={{\n                top: `${Math.random() * 100}%`,\n                left: `${Math.random() * 100}%`,\n              }}\n              animate={{\n                opacity: [0.2, 0.8, 0.2],\n                scale: [1, 1.5, 1],\n              }}\n              transition={{\n                duration: 3 + Math.random() * 2,\n                repeat: Infinity,\n                ease: 'easeInOut',\n                delay: Math.random() * 2,\n              }}\n            />\n          ))}\n        </div>\n      </div>\n\n      <div className=\"fadein-blur relative z-0 mx-auto mb-10 h-[300px] w-[300px] lg:absolute lg:top-1/2 lg:right-1/2 lg:mx-0 lg:mb-0 lg:h-[500px] lg:w-[500px] lg:translate-x-1/2 lg:-translate-y-2/3\">\n        <img\n          src=\"https://blocks.mvp-subha.me/Adobe Express - file(1).webp\"\n          alt=\"Nexus Platform 3D Visualization\"\n          className=\"h-full w-full object-contain drop-shadow-[0_0_35px_#3358ea85] transition-all duration-1000 hover:scale-110\"\n        />\n        <motion.div\n          variants={tooltipVariants}\n          className=\"absolute top-4 -left-4 rounded-lg border border-purple-500/30 bg-black/80 p-2 backdrop-blur-md lg:top-1/4 lg:-left-20\"\n        >\n          <div className=\"flex items-center gap-2\">\n            <Zap className=\"h-4 w-4 text-purple-400\" />\n            <span className=\"text-xs font-medium text-purple-200\">\n              High Performance\n            </span>\n          </div>\n        </motion.div>\n\n        <motion.div\n          variants={tooltipVariants}\n          className=\"absolute top-1/2 -right-4 rounded-lg border border-blue-500/30 bg-black/80 p-2 backdrop-blur-md lg:-right-24\"\n        >\n          <div className=\"flex items-center gap-2\">\n            <Database className=\"h-4 w-4 text-blue-400\" />\n            <span className=\"text-xs font-medium text-blue-200\">\n              Decentralized Storage\n            </span>\n          </div>\n        </motion.div>\n\n        <motion.div\n          variants={tooltipVariants}\n          className=\"absolute bottom-4 left-4 rounded-lg border border-indigo-500/30 bg-black/80 p-2 backdrop-blur-md lg:bottom-1/4 lg:left-8\"\n        >\n          <div className=\"flex items-center gap-2\">\n            <Sparkles className=\"h-4 w-4 text-indigo-400\" />\n            <span className=\"text-xs font-medium text-indigo-200\">\n              AI-Powered\n            </span>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Main Content Area */}\n      <motion.main\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n        className=\"relative z-10 mb-10 flex w-full max-w-[1450px] flex-grow flex-col items-center justify-center px-4 text-center sm:px-8 lg:mb-0 lg:items-start lg:justify-end lg:text-left\"\n      >\n        <motion.div className=\"flex w-full flex-col items-center justify-between lg:flex-row lg:items-start\">\n          <div className=\"w-full lg:w-auto\">\n            <motion.div\n              variants={itemVariants}\n              className=\"mb-4 inline-flex items-center rounded-full border border-purple-500/30 bg-purple-500/10 px-3 py-1 text-sm text-purple-300\"\n            >\n              <span className=\"mr-2 rounded-full bg-purple-500 px-2 py-0.5 text-xs font-semibold text-white\">\n                New\n              </span>\n              Introducing Nexus Platform\n            </motion.div>\n\n            <motion.h1\n              variants={itemVariants}\n              className=\"mb-6 bg-gradient-to-r from-white/70 via-white to-slate-500/80 bg-clip-text text-3xl leading-tight text-transparent sm:text-4xl md:text-5xl lg:text-6xl\"\n            >\n              The Bridge Between <br className=\"hidden sm:inline\" />\n              <span className=\"bg-gradient-to-r from-purple-400 via-blue-400 to-purple-400 bg-clip-text text-transparent\">\n                AI and Web3\n              </span>\n            </motion.h1>\n\n            {/* Animated Stats Row */}\n            <motion.div\n              variants={itemVariants}\n              className=\"mb-6 flex flex-wrap justify-center gap-4 md:gap-6 lg:justify-start\"\n            >\n              <div className=\"rounded-lg border border-purple-500/20 bg-black/40 px-4 py-2 backdrop-blur-sm\">\n                <p className=\"text-2xl font-bold text-white\">\n                  {stats.users.toLocaleString()}+\n                </p>\n                <p className=\"text-xs text-gray-400\">Active Users</p>\n              </div>\n              <div className=\"rounded-lg border border-blue-500/20 bg-black/40 px-4 py-2 backdrop-blur-sm\">\n                <p className=\"text-2xl font-bold text-white\">\n                  {stats.transactions.toLocaleString()}+\n                </p>\n                <p className=\"text-xs text-gray-400\">Transactions</p>\n              </div>\n              <div className=\"rounded-lg border border-indigo-500/20 bg-black/40 px-4 py-2 backdrop-blur-sm\">\n                <p className=\"text-2xl font-bold text-white\">\n                  {stats.networks}+\n                </p>\n                <p className=\"text-xs text-gray-400\">Networks</p>\n              </div>\n            </motion.div>\n\n            {/* Integration badges */}\n            <motion.div\n              variants={itemVariants}\n              className=\"mb-8 flex flex-wrap items-center justify-center gap-2 lg:justify-start\"\n            >\n              <span className=\"text-xs font-medium text-gray-400\">\n                Integrates with:\n              </span>\n              <div className=\"flex cursor-pointer items-center gap-2 rounded-full border border-slate-800 bg-slate-900/60 px-2 py-1 text-xs font-medium text-slate-300 backdrop-blur-sm transition-all hover:bg-purple-950\">\n                <div className=\"h-2 w-2 rounded-full bg-blue-400\"></div>\n                Ethereum\n              </div>\n              <div className=\"flex cursor-pointer items-center gap-2 rounded-full border border-slate-800 bg-slate-900/60 px-2 py-1 text-xs font-medium text-slate-300 backdrop-blur-sm transition-all hover:bg-purple-950\">\n                <div className=\"h-2 w-2 rounded-full bg-purple-400\"></div>\n                Solana\n              </div>\n              <div className=\"flex cursor-pointer items-center gap-2 rounded-full border border-slate-800 bg-slate-900/60 px-2 py-1 text-xs font-medium text-slate-300 backdrop-blur-sm transition-all hover:bg-purple-950\">\n                <div className=\"h-2 w-2 rounded-full bg-green-400\"></div>\n                OpenAI\n              </div>\n              <div className=\"flex cursor-pointer items-center gap-2 rounded-full border border-slate-800 bg-slate-900/60 px-2 py-1 text-xs font-medium text-slate-300 backdrop-blur-sm transition-all hover:bg-purple-950\">\n                <div className=\"h-2 w-2 rounded-full bg-yellow-400\"></div>\n                +5 more\n              </div>\n            </motion.div>\n          </div>\n\n          <div className=\"mt-6 flex flex-col items-center lg:mt-0 lg:items-end\">\n            <motion.p\n              variants={itemVariants}\n              className=\"mb-8 max-w-md px-6 text-center text-lg leading-relaxed text-slate-300/90 lg:text-end\"\n            >\n              Nexus connects AI tools with Web3 infrastructure, giving\n              developers the power to build beyond limits. One platform. Endless\n              potential.\n            </motion.p>\n            <motion.div\n              variants={itemVariants}\n              className=\"mb-8 flex flex-col flex-wrap gap-4 sm:flex-row lg:justify-end\"\n            >\n              <Button\n                className=\"group rounded-full border-t border-purple-400 bg-gradient-to-b from-purple-700 to-slate-950/80 px-6 py-6 text-white shadow-lg shadow-purple-600/20 transition-all hover:shadow-purple-600/40\"\n                size=\"lg\"\n              >\n                Start Building\n                <ArrowRight className=\"ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1\" />\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                className=\"rounded-full border-purple-500/30 bg-transparent text-white hover:bg-purple-500/10 hover:text-white\"\n                size=\"lg\"\n              >\n                View Demo\n              </Button>\n            </motion.div>\n\n            {/* Social proof */}\n            <motion.div\n              variants={itemVariants}\n              className=\"mx-auto flex items-center gap-3 rounded-full border border-slate-800 bg-slate-900/50 px-3 py-1 backdrop-blur-sm lg:mx-0 lg:ml-auto\"\n            >\n              <div className=\"flex -space-x-2\">\n                {[1, 2, 3, 4].map((i) => (\n                  <div\n                    key={i}\n                    className=\"h-6 w-6 overflow-hidden rounded-full border-2 border-slate-900 bg-slate-800\"\n                  >\n                    <div className=\"h-full w-full bg-gradient-to-br from-purple-500 to-blue-600 opacity-80\"></div>\n                  </div>\n                ))}\n              </div>\n              <span className=\"text-xs text-slate-300\">\n                <span className=\"font-semibold text-white\">500+</span>{' '}\n                developers already building\n              </span>\n              <ArrowUpRight className=\"h-3 w-3 text-purple-400\" />\n            </motion.div>\n          </div>\n        </motion.div>\n      </motion.main>\n      <div className=\"absolute right-auto -bottom-40 left-1/2 h-96 w-20 -translate-x-1/2 -rotate-45 rounded-full bg-gray-200/30 blur-[80px] lg:right-96 lg:left-auto lg:translate-x-0\"></div>\n      <div className=\"absolute right-auto -bottom-52 left-1/2 h-96 w-20 -translate-x-1/2 -rotate-45 rounded-full bg-gray-300/20 blur-[80px] lg:right-auto lg:left-auto lg:translate-x-0\"></div>\n      <div className=\"absolute right-auto -bottom-60 left-1/2 h-96 w-10 -translate-x-20 -rotate-45 rounded-full bg-gray-300/20 blur-[80px] lg:right-96 lg:left-auto lg:-translate-x-40\"></div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/hero/app-hero.tsx", "target": "components/mvpblocks/app-hero.tsx"}]}