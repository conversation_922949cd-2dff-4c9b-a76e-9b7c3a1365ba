{"name": "team-4", "author": "mosespace", "type": "registry:block", "dependencies": ["lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { useState } from 'react';\nimport { cn } from '@/lib/utils';\n\ntype SocialMediaLinks = {\n  facebook?: string;\n  twitter?: string;\n  instagram?: string;\n  linkedin?: string;\n  github?: string;\n  website?: string;\n  email?: string;\n  dribbble?: string;\n};\n\ntype TeamMember = {\n  id: number;\n  name: string;\n  role: string;\n  email?: string;\n  bio?: string;\n  image: string;\n  backgroundColor?: string;\n  socialMedia?: SocialMediaLinks;\n  expertise?: string[];\n  department?: string;\n};\n\ntype TeamSectionProps = {\n  title?: string;\n  subtitle?: string;\n  teamMembers: TeamMember[];\n  backgroundColor?: string;\n  textColor?: string;\n  secondaryColor?: string;\n  className?: string;\n};\n\ntype Department =\n  | 'all'\n  | 'management'\n  | 'product'\n  | 'design'\n  | 'marketing'\n  | 'sales'\n  | 'customer'\n  | 'operations';\n\nexport interface ElegantTeamProps extends TeamSectionProps {\n  departments?: Array<{\n    id: Department;\n    label: string;\n  }>;\n}\n\nconst elegantTeamMembers: TeamMember[] = [\n  {\n    id: 1,\n    name: '<PERSON>',\n    role: 'Co-Founder and CEO',\n    department: 'management',\n    image:\n      'https://img.freepik.com/premium-psd/3d-avatar-character_975163-690.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 2,\n    name: 'Orlando Diggs',\n    role: 'Co-Founder and COO',\n    department: 'management',\n    image:\n      'https://img.freepik.com/premium-psd/3d-avatar-character_975163-678.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 3,\n    name: 'Sophie Chamberlain',\n    role: 'Head of Sales',\n    department: 'sales',\n    image:\n      'https://img.freepik.com/premium-psd/3d-avatar-character_975163-725.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 4,\n    name: 'Lana Steiner',\n    role: 'VP of Customer Success',\n    department: 'customer',\n    image:\n      'https://img.freepik.com/premium-photo/female-character-3d-rendering-isolated-background_150525-107.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 5,\n    name: 'Emily Donnavan',\n    role: 'Product Lead',\n    department: 'product',\n    image:\n      'https://img.freepik.com/free-psd/3d-illustration-human-avatar-profile_23-2150671163.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 6,\n    name: 'Sasha Kindred',\n    role: 'VP of Marketing',\n    department: 'marketing',\n    image:\n      'https://img.freepik.com/free-psd/3d-illustration-human-avatar-profile_23-2150671132.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 7,\n    name: 'Jessica Dobrev',\n    role: 'Backend Lead',\n    department: 'operations',\n    image:\n      'https://img.freepik.com/free-psd/3d-illustration-human-avatar-profile_23-2150671159.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n  {\n    id: 8,\n    name: 'Drew Cano',\n    role: 'Head of UX',\n    department: 'design',\n    image:\n      'https://img.freepik.com/free-psd/3d-illustration-human-avatar-profile_23-2150671136.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n  },\n];\n\nexport default function Team4({\n  title = 'Meet the team that makes the magic happen',\n  subtitle = 'Meet our diverse team of world-class creators, designers, and problem solvers.',\n  teamMembers = elegantTeamMembers,\n  backgroundColor = '#ffffff',\n  textColor = '#000000',\n  secondaryColor = '#666666',\n  className,\n  departments = [\n    { id: 'all', label: 'View all' },\n    { id: 'management', label: 'Management' },\n    { id: 'product', label: 'Product' },\n    { id: 'design', label: 'Design' },\n    { id: 'marketing', label: 'Marketing' },\n    { id: 'sales', label: 'Sales' },\n    { id: 'customer', label: 'Customer Success' },\n    { id: 'operations', label: 'Operations' },\n  ],\n}: ElegantTeamProps) {\n  const [activeDepartment, setActiveDepartment] = useState<Department>('all');\n\n  // Filter team members by department\n  const filteredTeamMembers =\n    activeDepartment === 'all'\n      ? teamMembers\n      : teamMembers.filter(\n          (member) =>\n            member.department?.toLowerCase() === activeDepartment ||\n            member.role?.toLowerCase().includes(activeDepartment),\n        );\n\n  // Split the title to apply italic styling to \"magic\"\n  const titleParts = title.split(/(magic)/);\n\n  return (\n    <section\n      className={cn('w-full py-16', className)}\n      style={{ backgroundColor, color: textColor }}\n    >\n      <div className=\"container mx-auto max-w-6xl px-4\">\n        <div className=\"mb-12 text-center\">\n          <h2 className=\"mb-4 font-serif text-4xl leading-tight md:text-5xl\">\n            {titleParts.map((part, index) =>\n              part.toLowerCase() === 'magic' ? (\n                <span key={index} className=\"italic\">\n                  {part}\n                </span>\n              ) : (\n                <span key={index}>{part}</span>\n              ),\n            )}\n          </h2>\n          <p\n            className=\"mx-auto max-w-3xl text-base\"\n            style={{ color: secondaryColor }}\n          >\n            {subtitle}\n          </p>\n        </div>\n\n        <div className=\"mb-12 flex flex-wrap justify-center gap-2\">\n          {departments.map((dept) => (\n            <button\n              key={dept.id}\n              onClick={() => setActiveDepartment(dept.id)}\n              className={cn(\n                'rounded-md px-4 py-2 text-sm font-medium transition-colors',\n                activeDepartment === dept.id\n                  ? 'bg-gray-900 text-white'\n                  : 'border border-gray-200 bg-white text-gray-800 hover:bg-gray-100',\n              )}\n            >\n              {dept.label}\n            </button>\n          ))}\n        </div>\n\n        <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4\">\n          {filteredTeamMembers.map((member) => (\n            <div\n              key={member.id}\n              className=\"relative overflow-hidden rounded-lg transition-all\"\n            >\n              <div className=\"relative aspect-square overflow-hidden\">\n                <img\n                  src={member.image}\n                  alt={member.name}\n                  className=\"object-cover\"\n                />\n              </div>\n              <div className=\"relative z-10 mx-auto -mt-[2.5rem] max-w-[90%] rounded-lg border border-gray-100 bg-white px-2 py-3 text-center\">\n                <h3 className=\"text-lg font-semibold\">{member.name}</h3>\n                <p className=\"text-sm\" style={{ color: secondaryColor }}>\n                  {member.role}\n                </p>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/team/team-4.tsx", "target": "components/mvpblocks/team-4.tsx"}]}