{"name": "meshy-cards", "type": "registry:block", "dependencies": [], "registryDependencies": [], "files": [{"type": "registry:block", "content": "export default function MeshyCards() {\n  return (\n    <div className=\"mx-auto my-8 grid w-full max-w-7xl grid-cols-2 gap-6 p-4 lg:grid-cols-4\">\n      <div\n        className=\"scale-in group visible cursor-pointer\"\n        style={{ transform: 'translateY(0px) scale(1)' }}\n      >\n        <div\n          className=\"relative transform overflow-hidden rounded-2xl p-6 shadow-lg transition-all duration-300 group-hover:scale-105 hover:shadow-xl\"\n          style={{\n            background:\n              'url(https://images.unsplash.com/photo-1635776062360-af423602aff3?w=800&amp;q=80)',\n            backgroundSize: 'cover',\n          }}\n        >\n          <div className=\"relative\">\n            <div className=\"mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-white/20\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                width=\"24\"\n                height=\"24\"\n                viewBox=\"0 0 24 24\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                data-lucide=\"code\"\n                className=\"lucide lucide-code h-6 w-6 text-white\"\n              >\n                <path d=\"m16 18 6-6-6-6\"></path>\n                <path d=\"m8 6-6 6 6 6\"></path>\n              </svg>\n            </div>\n            <h3 className=\"mb-2 font-sans text-lg font-medium text-white\">\n              Code Assistant\n            </h3>\n            <p className=\"mb-4 font-sans text-sm text-white/80\">\n              Generate, optimize, and debug code across 50+ languages\n            </p>\n            <div className=\"flex items-center text-white/60\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                width=\"24\"\n                height=\"24\"\n                viewBox=\"0 0 24 24\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                data-lucide=\"trending-up\"\n                className=\"lucide lucide-trending-up mr-1 h-4 w-4\"\n              >\n                <path d=\"M16 7h6v6\"></path>\n                <path d=\"m22 7-8.5 8.5-5-5L2 17\"></path>\n              </svg>\n              <span className=\"font-sans text-xs\">95% accuracy rate</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div\n        className=\"scale-in group visible cursor-pointer\"\n        style={{ transform: 'transform: translateY(0px) scale(1)' }}\n      >\n        <div\n          className=\"relative transform overflow-hidden rounded-2xl p-6 shadow-lg transition-all duration-300 group-hover:scale-105 hover:shadow-xl\"\n          style={{\n            background:\n              'url(https://images.unsplash.com/photo-1579548122080-c35fd6820ecb?w=800&amp;q=80)',\n            backgroundSize: 'cover',\n          }}\n        >\n          <div className=\"relative\">\n            <div className=\"mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-white/20\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                width=\"24\"\n                height=\"24\"\n                viewBox=\"0 0 24 24\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                data-lucide=\"pen-tool\"\n                className=\"lucide lucide-pen-tool h-6 w-6 text-white\"\n              >\n                <path d=\"M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z\"></path>\n                <path d=\"m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18\"></path>\n                <path d=\"m2.3 2.3 7.286 7.286\"></path>\n                <circle cx=\"11\" cy=\"11\" r=\"2\"></circle>\n              </svg>\n            </div>\n            <h3 className=\"mb-2 font-sans text-lg font-medium text-white\">\n              Content Creator\n            </h3>\n            <p className=\"mb-4 font-sans text-sm text-white/80\">\n              Write, edit, and optimize content with AI precision\n            </p>\n            <div className=\"flex items-center text-white/60\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                width=\"24\"\n                height=\"24\"\n                viewBox=\"0 0 24 24\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                data-lucide=\"sparkles\"\n                className=\"lucide lucide-sparkles mr-1 h-4 w-4\"\n              >\n                <path d=\"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z\"></path>\n                <path d=\"M20 3v4\"></path>\n                <path d=\"M22 5h-4\"></path>\n                <path d=\"M4 17v2\"></path>\n                <path d=\"M5 18H3\"></path>\n              </svg>\n              <span className=\"font-sans text-xs\">Human-like quality</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div\n        className=\"scale-in group visible cursor-pointer\"\n        style={{ transform: 'transform: translateY(0px) scale(1)' }}\n      >\n        <div\n          className=\"relative transform overflow-hidden rounded-2xl bg-gradient-to-br from-green-600 to-green-800 p-6 shadow-lg transition-all duration-300 group-hover:scale-105 hover:shadow-xl\"\n          style={{\n            background:\n              'url(https://images.unsplash.com/photo-1635776062127-d379bfcba9f8?w=800&amp;q=80)',\n            backgroundSize: 'cover',\n          }}\n        >\n          <div className=\"relative\">\n            <div className=\"mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-white/20\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                width=\"24\"\n                height=\"24\"\n                viewBox=\"0 0 24 24\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                data-lucide=\"bar-chart\"\n                className=\"lucide lucide-bar-chart h-6 w-6 text-white\"\n              >\n                <line x1=\"12\" x2=\"12\" y1=\"20\" y2=\"10\"></line>\n                <line x1=\"18\" x2=\"18\" y1=\"20\" y2=\"4\"></line>\n                <line x1=\"6\" x2=\"6\" y1=\"20\" y2=\"16\"></line>\n              </svg>\n            </div>\n            <h3 className=\"mb-2 font-sans text-lg font-medium text-white\">\n              Data Analyst\n            </h3>\n            <p className=\"mb-4 font-sans text-sm text-white/80\">\n              Extract insights from complex datasets instantly\n            </p>\n            <div className=\"flex items-center text-white/60\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                width=\"24\"\n                height=\"24\"\n                viewBox=\"0 0 24 24\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                data-lucide=\"eye\"\n                className=\"lucide lucide-eye mr-1 h-4 w-4\"\n              >\n                <path d=\"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0\"></path>\n                <circle cx=\"12\" cy=\"12\" r=\"3\"></circle>\n              </svg>\n              <span className=\"font-sans text-xs\">Real-time insights</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div\n        className=\"scale-in group visible cursor-pointer\"\n        style={{ transform: 'transform: translateY(0px) scale(1)' }}\n      >\n        <div\n          className=\"relative transform overflow-hidden rounded-2xl bg-gradient-to-br from-cyan-600 to-cyan-800 p-6 shadow-lg transition-all duration-300 group-hover:scale-105 hover:shadow-xl\"\n          style={{\n            background:\n              'url(https://images.unsplash.com/photo-1635776063328-153b13e3c245?w=800&amp;q=80)',\n            backgroundSize: 'cover',\n          }}\n        >\n          <div className=\"relative\">\n            <div className=\"mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-white/20\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                width=\"24\"\n                height=\"24\"\n                viewBox=\"0 0 24 24\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                data-lucide=\"workflow\"\n                className=\"lucide lucide-workflow h-6 w-6 text-white\"\n              >\n                <rect width=\"8\" height=\"8\" x=\"3\" y=\"3\" rx=\"2\"></rect>\n                <path d=\"M7 11v4a2 2 0 0 0 2 2h4\"></path>\n                <rect width=\"8\" height=\"8\" x=\"13\" y=\"13\" rx=\"2\"></rect>\n              </svg>\n            </div>\n            <h3 className=\"mb-2 font-sans text-lg font-medium text-white\">\n              Workflow Automation\n            </h3>\n            <p className=\"mb-4 font-sans text-sm text-white/80\">\n              Automate repetitive tasks and processes\n            </p>\n            <div className=\"flex items-center text-white/60\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                width=\"24\"\n                height=\"24\"\n                viewBox=\"0 0 24 24\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                data-lucide=\"clock\"\n                className=\"lucide lucide-clock mr-1 h-4 w-4\"\n              >\n                <path d=\"M12 6v6l4 2\"></path>\n                <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n              </svg>\n              <span className=\"font-sans text-xs\">Save 40+ hours/week</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/cards/basic/meshy-cards.tsx", "target": "components/mvpblocks/meshy-cards.tsx"}]}