{"name": "header-2", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence, easeInOut } from 'framer-motion';\nimport { Menu, X, ArrowRight, Zap, Search } from 'lucide-react';\nimport Link from 'next/link';\n\ninterface NavItem {\n  name: string;\n  href: string;\n}\n\nconst navItems: NavItem[] = [\n  { name: 'Home', href: '/' },\n  { name: 'Features', href: '/features' },\n  { name: 'Solutions', href: '/solutions' },\n  { name: 'Pricing', href: '/pricing' },\n  { name: 'Resources', href: '/resources' },\n  { name: 'Contact', href: '/contact' },\n];\n\nexport default function Header2() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [hoveredItem, setHoveredItem] = useState<string | null>(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const containerVariants = {\n    hidden: { opacity: 0, y: -20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: -10 },\n    visible: { opacity: 1, y: 0 },\n  };\n\n  const mobileMenuVariants = {\n    closed: {\n      opacity: 0,\n      x: '100%',\n      transition: {\n        duration: 0.3,\n        ease: easeInOut,\n      },\n    },\n    open: {\n      opacity: 1,\n      x: 0,\n      transition: {\n        duration: 0.3,\n        ease: easeInOut,\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const mobileItemVariants = {\n    closed: { opacity: 0, x: 20 },\n    open: { opacity: 1, x: 0 },\n  };\n\n  return (\n    <>\n      <motion.header\n        className={`fixed top-0 right-0 left-0 z-50 transition-all duration-500 ${\n          isScrolled\n            ? 'border-border/50 bg-background/80 border-b shadow-sm backdrop-blur-md'\n            : 'bg-transparent'\n        }`}\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        <div className=\"mx-auto max-w-6xl px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex h-16 items-center justify-between\">\n            <motion.div\n              className=\"flex items-center space-x-3\"\n              variants={itemVariants}\n              whileHover={{ scale: 1.02 }}\n              transition={{ type: 'spring', stiffness: 400, damping: 25 }}\n            >\n              <Link href=\"/\" className=\"flex items-center space-x-3\">\n                <div className=\"relative\">\n                  <div className=\"flex h-9 w-9 items-center justify-center rounded-xl bg-gradient-to-br from-rose-500 via-rose-600 to-rose-700 shadow-lg\">\n                    <Zap className=\"h-5 w-5 text-white\" />\n                  </div>\n                  <div className=\"absolute -top-1 -right-1 h-3 w-3 animate-pulse rounded-full bg-green-400\"></div>\n                </div>\n                <div className=\"flex flex-col\">\n                  <span className=\"text-foreground text-lg font-bold\">\n                    Acme Inc.\n                  </span>\n                  <span className=\"text-muted-foreground -mt-1 text-xs\">\n                    Build faster\n                  </span>\n                </div>\n              </Link>\n            </motion.div>\n\n            <nav className=\"hidden items-center space-x-1 lg:flex\">\n              {navItems.map((item, index) => (\n                <motion.div\n                  key={item.name}\n                  variants={itemVariants}\n                  className=\"relative\"\n                  onMouseEnter={() => setHoveredItem(item.name)}\n                  onMouseLeave={() => setHoveredItem(null)}\n                >\n                  <Link\n                    href={item.href}\n                    className=\"text-foreground/80 hover:text-foreground relative rounded-lg px-4 py-2 text-sm font-medium transition-colors duration-200\"\n                  >\n                    {hoveredItem === item.name && (\n                      <motion.div\n                        className=\"bg-muted absolute inset-0 rounded-lg\"\n                        layoutId=\"navbar-hover\"\n                        initial={{ opacity: 0 }}\n                        animate={{ opacity: 1 }}\n                        exit={{ opacity: 0 }}\n                        transition={{\n                          type: 'spring',\n                          stiffness: 400,\n                          damping: 30,\n                        }}\n                      />\n                    )}\n                    <span className=\"relative z-10\">{item.name}</span>\n                  </Link>\n                </motion.div>\n              ))}\n            </nav>\n\n            <motion.div\n              className=\"hidden items-center space-x-3 lg:flex\"\n              variants={itemVariants}\n            >\n              <motion.button\n                className=\"text-muted-foreground hover:bg-muted hover:text-foreground rounded-lg p-2 transition-colors duration-200\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <Search className=\"h-5 w-5\" />\n              </motion.button>\n\n              <Link\n                href=\"/login\"\n                className=\"text-foreground/80 hover:text-foreground px-4 py-2 text-sm font-medium transition-colors duration-200\"\n              >\n                Sign In\n              </Link>\n\n              <motion.div\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <Link\n                  href=\"/signup\"\n                  className=\"bg-foreground text-background hover:bg-foreground/90 inline-flex items-center space-x-2 rounded-lg px-5 py-2.5 text-sm font-medium shadow-sm transition-all duration-200\"\n                >\n                  <span>Get Started</span>\n                  <ArrowRight className=\"h-4 w-4\" />\n                </Link>\n              </motion.div>\n            </motion.div>\n\n            <motion.button\n              className=\"text-foreground hover:bg-muted rounded-lg p-2 transition-colors duration-200 lg:hidden\"\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              variants={itemVariants}\n              whileTap={{ scale: 0.95 }}\n            >\n              {isMobileMenuOpen ? (\n                <X className=\"h-6 w-6\" />\n              ) : (\n                <Menu className=\"h-6 w-6\" />\n              )}\n            </motion.button>\n          </div>\n        </div>\n      </motion.header>\n\n      <AnimatePresence>\n        {isMobileMenuOpen && (\n          <>\n            <motion.div\n              className=\"fixed inset-0 z-40 bg-black/20 backdrop-blur-sm lg:hidden\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              onClick={() => setIsMobileMenuOpen(false)}\n            />\n            <motion.div\n              className=\"border-border bg-background fixed top-16 right-4 z-50 w-80 overflow-hidden rounded-2xl border shadow-2xl lg:hidden\"\n              variants={mobileMenuVariants}\n              initial=\"closed\"\n              animate=\"open\"\n              exit=\"closed\"\n            >\n              <div className=\"space-y-6 p-6\">\n                <div className=\"space-y-1\">\n                  {navItems.map((item) => (\n                    <motion.div key={item.name} variants={mobileItemVariants}>\n                      <Link\n                        href={item.href}\n                        className=\"text-foreground hover:bg-muted block rounded-lg px-4 py-3 font-medium transition-colors duration-200\"\n                        onClick={() => setIsMobileMenuOpen(false)}\n                      >\n                        {item.name}\n                      </Link>\n                    </motion.div>\n                  ))}\n                </div>\n\n                <motion.div\n                  className=\"border-border space-y-3 border-t pt-6\"\n                  variants={mobileItemVariants}\n                >\n                  <Link\n                    href=\"/login\"\n                    className=\"text-foreground hover:bg-muted block w-full rounded-lg py-3 text-center font-medium transition-colors duration-200\"\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    Sign In\n                  </Link>\n                  <Link\n                    href=\"/signup\"\n                    className=\"bg-foreground text-background hover:bg-foreground/90 block w-full rounded-lg py-3 text-center font-medium transition-all duration-200\"\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    Get Started\n                  </Link>\n                </motion.div>\n              </div>\n            </motion.div>\n          </>\n        )}\n      </AnimatePresence>\n    </>\n  );\n}\n", "path": "/components/mvpblocks/required/headers/header-2.tsx", "target": "components/mvpblocks/header-2.tsx"}]}