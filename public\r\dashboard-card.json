{"name": "dashboard-card", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { memo } from 'react';\nimport { motion } from 'framer-motion';\nimport { TrendingUp } from 'lucide-react';\n\ninterface DashboardCardProps {\n  stat: {\n    title: string;\n    value: string;\n    change: string;\n    changeType: 'positive' | 'negative';\n    icon: any;\n    color: string;\n    bgColor: string;\n  };\n  index: number;\n}\n\nexport const DashboardCard = memo(({ stat, index }: DashboardCardProps) => {\n  const Icon = stat.icon;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ delay: index * 0.1 }}\n      whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}\n      className=\"group relative cursor-pointer\"\n    >\n      <div className=\"border-border bg-card/40 rounded-xl border p-6 transition-all duration-300 hover:shadow-lg\">\n        <div className=\"to-primary/5 absolute inset-0 rounded-xl bg-gradient-to-br from-transparent via-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100\" />\n\n        <div className=\"relative\">\n          <div className=\"mb-4 flex items-center justify-between\">\n            <div className={`rounded-lg p-3 ${stat.bgColor}`}>\n              <Icon className={`h-6 w-6 ${stat.color}`} />\n            </div>\n\n            <div\n              className={`flex items-center gap-1 text-sm font-medium ${\n                stat.changeType === 'positive'\n                  ? 'text-green-500'\n                  : 'text-red-500'\n              }`}\n            >\n              <TrendingUp\n                className={`h-4 w-4 ${\n                  stat.changeType === 'negative' ? 'rotate-180' : ''\n                }`}\n              />\n              <span>{stat.change}</span>\n            </div>\n          </div>\n\n          <div className=\"mb-3\">\n            <h3 className=\"text-foreground mb-1 text-3xl font-bold\">\n              {stat.value}\n            </h3>\n            <p className=\"text-muted-foreground text-sm font-medium\">\n              {stat.title}\n            </p>\n          </div>\n\n          <div className=\"bg-muted h-2 overflow-hidden rounded-full\">\n            <motion.div\n              initial={{ width: 0 }}\n              animate={{ width: `${65 + index * 8}%` }}\n              transition={{ duration: 1, delay: index * 0.1 }}\n              className={`h-full rounded-full ${stat.color.replace('text-', 'bg-')}`}\n            />\n          </div>\n        </div>\n      </div>\n    </motion.div>\n  );\n});\n\nDashboardCard.displayName = 'DashboardCard';\n", "path": "/components/mvpblocks/dashboards/admin-dashboard-1/ui/dashboard-card.tsx", "target": "components/mvpblocks/ui/dashboard-card.tsx"}]}