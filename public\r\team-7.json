{"name": "team-7", "author": "mosespace", "type": "registry:block", "dependencies": ["lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport Link from 'next/link';\nimport { cn } from '@/lib/utils';\nimport { Cabin_Condensed } from 'next/font/google';\nimport { Facebook, Instagram, Twitter, Globe } from 'lucide-react';\n\nconst mont = Cabin_Condensed({\n  subsets: ['latin'],\n  weight: ['400', '500', '600', '700'],\n});\n\ntype SocialMediaLinks = {\n  facebook?: string;\n  twitter?: string;\n  instagram?: string;\n  linkedin?: string;\n  github?: string;\n  website?: string;\n  email?: string;\n  dribbble?: string;\n};\n\ntype TeamMember = {\n  id: number;\n  name: string;\n  role: string;\n  email?: string;\n  bio?: string;\n  image: string;\n  backgroundColor?: string; // For colored backgrounds\n  socialMedia?: SocialMediaLinks;\n  expertise?: string[];\n};\n\ninterface TeamSectionProps {\n  title?: string;\n  subtitle?: string;\n  description?: string;\n  teamMembers: TeamMember[];\n  backgroundColor?: string;\n  textColor?: string;\n  accentColor?: string;\n  secondaryColor?: string;\n  className?: string;\n}\n\nconst teamMembers3D: TeamMember[] = [\n  {\n    id: 1,\n    name: '<PERSON><PERSON>',\n    role: 'Chief Executive Officer',\n    email: '<EMAIL>',\n    bio: '<PERSON>rem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy.',\n    image:\n      'https://img.freepik.com/free-psd/3d-rendering-avatar_23-2150833554.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n    backgroundColor: '#7f1d1d', // bg-red-900\n    socialMedia: {\n      facebook: '#',\n      twitter: '#',\n      instagram: '#',\n      website: '#',\n    },\n  },\n  {\n    id: 2,\n    name: 'Isabella Thompson',\n    role: 'Chief Technology Officer',\n    email: '<EMAIL>',\n    bio: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy.',\n    image:\n      'https://img.freepik.com/premium-photo/png-headset-headphones-portrait-cartoon_53876-762197.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n    backgroundColor: '#b45309', // bg-amber-700\n    socialMedia: {\n      facebook: '#',\n      twitter: '#',\n      instagram: '#',\n      website: '#',\n    },\n  },\n  {\n    id: 3,\n    name: 'Zainab Rahman',\n    role: 'Chief Operations Officer',\n    email: '<EMAIL>',\n    bio: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy.',\n    image:\n      'https://img.freepik.com/premium-photo/png-cartoon-portrait-glasses-white-background_53876-905385.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n    backgroundColor: '#1e3a8a', // bg-blue-900\n    socialMedia: {\n      facebook: '#',\n      twitter: '#',\n      instagram: '#',\n      website: '#',\n    },\n  },\n  {\n    id: 4,\n    name: 'Aiden Davis',\n    role: 'Chief Marketing Officer',\n    email: '<EMAIL>',\n    bio: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy.',\n    image:\n      'https://img.freepik.com/premium-psd/3d-avatar-character_975163-690.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n    backgroundColor: '#7f1d1d', // bg-red-900\n    socialMedia: {\n      facebook: '#',\n      twitter: '#',\n      instagram: '#',\n      website: '#',\n    },\n  },\n  {\n    id: 5,\n    name: 'Aysha Hussain',\n    role: 'UX Designer',\n    email: '<EMAIL>',\n    bio: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy.',\n    image:\n      'https://img.freepik.com/free-photo/fun-3d-illustration-american-referee_183364-81231.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n    backgroundColor: '#1e3a8a', // bg-blue-900\n    socialMedia: {\n      facebook: '#',\n      twitter: '#',\n      instagram: '#',\n      website: '#',\n    },\n  },\n  {\n    id: 6,\n    name: 'Samira Shah',\n    role: 'Product Manager',\n    email: '<EMAIL>',\n    bio: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy.',\n    image:\n      'https://img.freepik.com/premium-psd/lego-character-with-blue-button-his-chest_1217673-223400.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n    backgroundColor: '#b45309', // bg-amber-700\n    socialMedia: {\n      facebook: '#',\n      twitter: '#',\n      instagram: '#',\n      website: '#',\n    },\n  },\n  {\n    id: 7,\n    name: 'Ethan Williams',\n    role: 'Backend Developer',\n    email: '<EMAIL>',\n    bio: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy.',\n    image:\n      'https://img.freepik.com/premium-photo/there-is-black-girl-with-headphones-yellow-jacket_1034474-106535.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n    backgroundColor: '#065f46', // bg-emerald-800\n    socialMedia: {\n      facebook: '#',\n      twitter: '#',\n      instagram: '#',\n      website: '#',\n    },\n  },\n  {\n    id: 8,\n    name: 'Amina Khan',\n    role: 'Frontend Developer',\n    email: '<EMAIL>',\n    bio: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy.',\n    image:\n      'https://img.freepik.com/free-photo/portrait-young-student-with-book-education-day_23-2150980030.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\n    backgroundColor: '#b45309', // bg-amber-700\n    socialMedia: {\n      facebook: '#',\n      twitter: '#',\n      instagram: '#',\n      website: '#',\n    },\n  },\n];\n\nexport default function Team7({\n  title = 'We are Born For Technology',\n  subtitle = '• Our awesome team',\n  description = 'We make life easier for our customers and community through reliable, affordable, and useful tech innovations',\n  teamMembers = teamMembers3D,\n  backgroundColor = '#111111',\n  textColor = '#ffffff',\n  accentColor = '#10b981',\n  secondaryColor = '#6b7280',\n  className,\n}: TeamSectionProps) {\n  // Default social media icons if not specified\n  const getSocialIcon = (type: string, url: string) => {\n    switch (type) {\n      case 'facebook':\n        return (\n          <Link\n            href={url}\n            className=\"text-gray-400 transition-colors hover:text-white\"\n          >\n            <Facebook size={18} />\n          </Link>\n        );\n      case 'instagram':\n        return (\n          <Link\n            href={url}\n            className=\"text-gray-400 transition-colors hover:text-white\"\n          >\n            <Instagram size={18} />\n          </Link>\n        );\n      case 'twitter':\n        return (\n          <Link\n            href={url}\n            className=\"text-gray-400 transition-colors hover:text-white\"\n          >\n            <Twitter size={18} />\n          </Link>\n        );\n      case 'website':\n        return (\n          <Link\n            href={url}\n            className=\"text-gray-400 transition-colors hover:text-white\"\n          >\n            <Globe size={18} />\n          </Link>\n        );\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <section\n      className={cn('w-full py-16 text-white', className)}\n      style={{ backgroundColor, color: textColor }}\n    >\n      <div className=\"container mx-auto max-w-5xl px-4\">\n        <div className=\"mb-12\">\n          <p\n            className={cn(\n              'mb-2 text-sm font-medium tracking-wider',\n              mont.className,\n            )}\n            style={{ color: accentColor }}\n          >\n            {subtitle}\n          </p>\n          <h2\n            className={cn(\n              'mb-6 text-4xl leading-tight font-bold md:text-5xl',\n              mont.className,\n            )}\n          >\n            {title}\n          </h2>\n          <p className=\"max-w-2xl text-lg\" style={{ color: secondaryColor }}>\n            {description}\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\">\n          {teamMembers.map((member) => (\n            <div key={member.id} className=\"group\">\n              <div\n                className=\"mb-4 aspect-square overflow-hidden rounded-lg\"\n                style={{ backgroundColor: member.backgroundColor || '#374151' }}\n              >\n                <img\n                  src={member.image || '/placeholder.svg'}\n                  alt={member.name}\n                  className=\"h-full w-full object-cover\"\n                />\n              </div>\n              <h3 className=\"text-xl font-bold\">{member.name}</h3>\n              {member.email && (\n                <p className=\"mb-2 text-sm\" style={{ color: accentColor }}>\n                  {member.email}\n                </p>\n              )}\n              {member.bio && (\n                <p className=\"mb-4 text-sm\" style={{ color: secondaryColor }}>\n                  {member.bio}\n                </p>\n              )}\n              <div className=\"mt-2 flex space-x-4\">\n                {member.socialMedia &&\n                  Object.entries(member.socialMedia).map(\n                    ([key, value]) =>\n                      value && (\n                        <span key={key}>{getSocialIcon(key, value)}</span>\n                      ),\n                  )}\n              </div>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"mt-12 text-right\">\n          <Link\n            href=\"#\"\n            className=\"inline-flex items-center text-lg hover:underline\"\n            style={{ color: accentColor }}\n          >\n            Learn more <span className=\"ml-2\">→</span>\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/team/team-7.tsx", "target": "components/mvpblocks/team-7.tsx"}]}