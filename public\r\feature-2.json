{"name": "feature-2", "type": "registry:block", "dependencies": ["framer-motion", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { cn } from '@/lib/utils';\nimport { Rocket, Code, Paintbrush } from 'lucide-react';\n\nconst features = [\n  {\n    step: 'Step 1',\n    title: 'Build Faster',\n    content:\n      'Create your MVP in record time with our pre-built blocks and components.',\n    icon: <Rocket className=\"text-primary h-6 w-6\" />,\n    image:\n      'https://images.unsplash.com/photo-1581291518633-83b4ebd1d83e?q=80&w=2070&auto=format&fit=crop',\n  },\n  {\n    step: 'Step 2',\n    title: 'Customize Easily',\n    content:\n      'Tailor every component to your needs with our intuitive design system and flexible architecture.',\n    icon: <Paintbrush className=\"text-primary h-6 w-6\" />,\n    image:\n      'https://images.unsplash.com/photo-1618761714954-0b8cd0026356?q=80&w=2070&auto=format&fit=crop',\n  },\n  {\n    step: 'Step 3',\n    title: 'Deploy Confidently',\n    content:\n      'Launch your product with confidence using our optimized, responsive, and accessible components.',\n    icon: <Code className=\"text-primary h-6 w-6\" />,\n    image:\n      'https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2070&auto=format&fit=crop',\n  },\n  {\n    step: 'Step 4',\n    title: 'Add Yours!',\n    content:\n      'Contribute your own blocks and become part of the MVPBlocks community.',\n    icon: <Code className=\"text-primary h-6 w-6\" />,\n    image:\n      'https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2070&auto=format&fit=crop',\n  },\n];\n\nexport default function FeatureSteps() {\n  const [currentFeature, setCurrentFeature] = useState(0);\n  const [progress, setProgress] = useState(0);\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      if (progress < 100) {\n        setProgress((prev) => prev + 100 / (4000 / 100));\n      } else {\n        setCurrentFeature((prev) => (prev + 1) % features.length);\n        setProgress(0);\n      }\n    }, 100);\n\n    return () => clearInterval(timer);\n  }, [progress]);\n\n  return (\n    <div className={'p-8 md:p-12'}>\n      <div className=\"mx-auto w-full max-w-7xl\">\n        <div className=\"relative mx-auto mb-12 max-w-2xl sm:text-center\">\n          <div className=\"relative z-10\">\n            <h2 className=\"font-geist text-3xl font-bold tracking-tighter md:text-4xl lg:text-5xl\">\n              Build Your MVP in Three Steps\n            </h2>\n            <p className=\"font-geist text-foreground/60 mt-3\">\n              MVPBlocks helps you create, customize, and deploy your product\n              faster than ever before.\n            </p>\n          </div>\n          <div\n            className=\"absolute inset-0 mx-auto h-44 max-w-xs blur-[118px]\"\n            style={{\n              background:\n                'linear-gradient(152.92deg, rgba(192, 15, 102, 0.2) 4.54%, rgba(192, 11, 109, 0.26) 34.2%, rgba(192, 15, 102, 0.1) 77.55%)',\n            }}\n          ></div>\n        </div>\n        <hr className=\"bg-foreground/30 mx-auto mb-10 h-px w-1/2\" />\n\n        <div className=\"flex flex-col gap-6 md:grid md:grid-cols-2 md:gap-10\">\n          <div className=\"order-2 space-y-8 md:order-1\">\n            {features.map((feature, index) => (\n              <motion.div\n                key={index}\n                className=\"flex items-center gap-6 md:gap-8\"\n                initial={{ opacity: 0.3, x: -20 }}\n                animate={{\n                  opacity: index === currentFeature ? 1 : 0.3,\n                  x: 0,\n                  scale: index === currentFeature ? 1.05 : 1,\n                }}\n                transition={{ duration: 0.5 }}\n              >\n                <motion.div\n                  className={cn(\n                    'flex h-12 w-12 items-center justify-center rounded-full border-2 md:h-14 md:w-14',\n                    index === currentFeature\n                      ? 'border-primary bg-primary/10 text-primary scale-110 [box-shadow:0_0_15px_rgba(192,15,102,0.3)]'\n                      : 'border-muted-foreground bg-muted',\n                  )}\n                >\n                  {feature.icon}\n                </motion.div>\n\n                <div className=\"flex-1\">\n                  <h3 className=\"text-xl font-semibold md:text-2xl\">\n                    {feature.title}\n                  </h3>\n                  <p className=\"text-muted-foreground text-sm md:text-base\">\n                    {feature.content}\n                  </p>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          <div\n            className={cn(\n              'border-primary/20 relative order-1 h-[200px] overflow-hidden rounded-xl border [box-shadow:0_5px_30px_-15px_rgba(192,15,102,0.3)] md:order-2 md:h-[300px] lg:h-[400px]',\n            )}\n          >\n            <AnimatePresence mode=\"wait\">\n              {features.map(\n                (feature, index) =>\n                  index === currentFeature && (\n                    <motion.div\n                      key={index}\n                      className=\"absolute inset-0 overflow-hidden rounded-lg\"\n                      initial={{ y: 100, opacity: 0, rotateX: -20 }}\n                      animate={{ y: 0, opacity: 1, rotateX: 0 }}\n                      exit={{ y: -100, opacity: 0, rotateX: 20 }}\n                      transition={{ duration: 0.5, ease: 'easeInOut' }}\n                    >\n                      <img\n                        src={feature.image}\n                        alt={feature.title}\n                        className=\"h-full w-full transform object-cover transition-transform hover:scale-105\"\n                        width={1000}\n                        height={500}\n                      />\n                      <div className=\"from-background via-background/50 absolute right-0 bottom-0 left-0 h-2/3 bg-gradient-to-t to-transparent\" />\n\n                      <div className=\"bg-background/80 absolute bottom-4 left-4 rounded-lg p-2 backdrop-blur-sm\">\n                        <span className=\"text-primary text-xs font-medium\">\n                          {feature.step}\n                        </span>\n                      </div>\n                    </motion.div>\n                  ),\n              )}\n            </AnimatePresence>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/features/feature-2.tsx", "target": "components/mvpblocks/feature-2.tsx"}]}