{"name": "faq-3", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/badge.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ChevronDown, Mail } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { Badge } from '@/components/ui/badge';\n\ninterface FAQItemProps {\n  question: string;\n  answer: string;\n  index: number;\n}\n\nfunction FAQItem({ question, answer, index }: FAQItemProps) {\n  const [isOpen, setIsOpen] = useState(false);\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 10 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{\n        duration: 0.3,\n        delay: index * 0.15,\n        ease: 'easeOut',\n      }}\n      className={cn(\n        'group border-border/60 rounded-lg border',\n        'transition-all duration-200 ease-in-out',\n        isOpen ? 'bg-card/30 shadow-sm' : 'hover:bg-card/50',\n      )}\n    >\n      <button\n        type=\"button\"\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex w-full items-center justify-between gap-4 px-6 py-4\"\n      >\n        <h3\n          className={cn(\n            'text-left text-base font-medium transition-colors duration-200',\n            'text-foreground/80',\n            isOpen && 'text-foreground',\n          )}\n        >\n          {question}\n        </h3>\n        <motion.div\n          animate={{\n            rotate: isOpen ? 180 : 0,\n            scale: isOpen ? 1.1 : 1,\n          }}\n          transition={{\n            duration: 0.3,\n            ease: 'easeInOut',\n          }}\n          className={cn(\n            'shrink-0 rounded-full p-0.5',\n            'transition-colors duration-200',\n            isOpen ? 'text-primary' : 'text-muted-foreground',\n          )}\n        >\n          <ChevronDown className=\"h-4 w-4\" />\n        </motion.div>\n      </button>\n      <AnimatePresence initial={false}>\n        {isOpen && (\n          <motion.div\n            initial={{ height: 0, opacity: 0 }}\n            animate={{\n              height: 'auto',\n              opacity: 1,\n              transition: {\n                height: {\n                  duration: 0.4,\n                  ease: [0.04, 0.62, 0.23, 0.98],\n                },\n                opacity: {\n                  duration: 0.25,\n                  delay: 0.1,\n                },\n              },\n            }}\n            exit={{\n              height: 0,\n              opacity: 0,\n              transition: {\n                height: {\n                  duration: 0.3,\n                  ease: 'easeInOut',\n                },\n                opacity: {\n                  duration: 0.25,\n                },\n              },\n            }}\n          >\n            <div className=\"border-border/40 border-t px-6 pt-2 pb-4\">\n              <motion.p\n                initial={{ y: -8, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                exit={{ y: -8, opacity: 0 }}\n                transition={{\n                  duration: 0.3,\n                  ease: 'easeOut',\n                }}\n                className=\"text-muted-foreground text-sm leading-relaxed\"\n              >\n                {answer}\n              </motion.p>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.div>\n  );\n}\n\nexport default function Faq3() {\n  const faqs: Omit<FAQItemProps, 'index'>[] = [\n    {\n      question: 'What makes MVPBlocks unique?',\n      answer:\n        \"MVPBlocks stands out through its intuitive design, powerful component library, and seamless integration options. We've focused on creating a user experience that combines simplicity with advanced features, all while maintaining excellent performance and accessibility.\",\n    },\n    {\n      question: 'How can I customize the components?',\n      answer:\n        'All components are built with Tailwind CSS, making them highly customizable. You can modify colors, spacing, typography, and more by simply adjusting the class names or using our theme variables to match your brand identity.',\n    },\n    {\n      question: 'Do the components work with dark mode?',\n      answer:\n        \"Yes, all MVPBlocks components are designed to work seamlessly with both light and dark modes. They automatically adapt to your site's theme settings, providing a consistent user experience regardless of the user's preference.\",\n    },\n    {\n      question: 'How can I get started with MVPBlocks?',\n      answer:\n        'You can get started by browsing our component library and copying the code for the components you need. Our documentation provides clear instructions for installation and usage, and you can always reach out to our support team if you need assistance.',\n    },\n    {\n      question: 'Can I use MVPBlocks for commercial projects?',\n      answer:\n        'Absolutely! MVPBlocks is free to use for both personal and commercial projects. There are no licensing fees or attribution requirements—just build and launch your MVP faster than ever before.',\n    },\n  ];\n\n  return (\n    <section className=\"bg-background relative w-full overflow-hidden py-16\">\n      {/* Decorative elements */}\n      <div className=\"bg-primary/5 absolute top-20 -left-20 h-64 w-64 rounded-full blur-3xl\" />\n      <div className=\"bg-primary/5 absolute -right-20 bottom-20 h-64 w-64 rounded-full blur-3xl\" />\n\n      <div className=\"relative container mx-auto max-w-6xl px-4\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          className=\"mx-auto mb-12 max-w-2xl text-center\"\n        >\n          <Badge\n            variant=\"outline\"\n            className=\"border-primary mb-4 px-3 py-1 text-xs font-medium tracking-wider uppercase\"\n          >\n            FAQs\n          </Badge>\n\n          <h2 className=\"from-primary mb-3 bg-gradient-to-r to-rose-400 bg-clip-text text-3xl font-bold text-transparent\">\n            Frequently Asked Questions\n          </h2>\n          <p className=\"text-muted-foreground text-sm\">\n            Everything you need to know about MVPBlocks\n          </p>\n        </motion.div>\n\n        <div className=\"mx-auto max-w-2xl space-y-2\">\n          {faqs.map((faq, index) => (\n            <FAQItem key={index} {...faq} index={index} />\n          ))}\n        </div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.3 }}\n          className={cn('mx-auto mt-12 max-w-md rounded-lg p-6 text-center')}\n        >\n          <div className=\"bg-primary/10 text-primary mb-4 inline-flex items-center justify-center rounded-full p-2\">\n            <Mail className=\"h-4 w-4\" />\n          </div>\n          <p className=\"text-foreground mb-1 text-sm font-medium\">\n            Still have questions?\n          </p>\n          <p className=\"text-muted-foreground mb-4 text-xs\">\n            We&apos;re here to help you\n          </p>\n          <button\n            type=\"button\"\n            className={cn(\n              'rounded-md px-4 py-2 text-sm',\n              'bg-primary text-primary-foreground',\n              'hover:bg-primary/90',\n              'transition-colors duration-200',\n              'font-medium',\n            )}\n          >\n            Contact Support\n          </button>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/faqs/faq-3.tsx", "target": "components/mvpblocks/faq-3.tsx"}]}