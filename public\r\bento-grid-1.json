{"name": "bento-grid-1", "type": "registry:block", "author": "Xeven777", "dependencies": ["lucide-react", "framer-motion"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\nimport { cn } from '@/lib/utils';\nimport { motion } from 'framer-motion';\nimport { ArrowRight, Code, FileText, Layers, Palette, Zap } from 'lucide-react';\n\ninterface BentoGridItemProps {\n  title: string;\n  description: string;\n  icon: React.ReactNode;\n  className?: string;\n  size?: 'small' | 'medium' | 'large';\n}\n\nconst BentoGridItem = ({\n  title,\n  description,\n  icon,\n  className,\n  size = 'small',\n}: BentoGridItemProps) => {\n  const variants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { type: 'spring' as const, damping: 25 },\n    },\n  };\n\n  return (\n    <motion.div\n      variants={variants}\n      className={cn(\n        'group border-primary/10 bg-background hover:border-primary/30 relative flex h-full cursor-pointer flex-col justify-between overflow-hidden rounded-xl border px-6 pt-6 pb-10 shadow-md transition-all duration-500',\n        className,\n      )}\n    >\n      <div className=\"absolute top-0 -right-1/2 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#3d16165e_1px,transparent_1px),linear-gradient(to_bottom,#3d16165e_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]\"></div>\n\n      <div className=\"text-primary/5 group-hover:text-primary/10 absolute right-1 bottom-3 scale-[6] transition-all duration-700 group-hover:scale-[6.2]\">\n        {icon}\n      </div>\n\n      <div className=\"relative z-10 flex h-full flex-col justify-between\">\n        <div>\n          <div className=\"bg-primary/10 text-primary shadow-primary/10 group-hover:bg-primary/20 group-hover:shadow-primary/20 mb-4 flex h-12 w-12 items-center justify-center rounded-full shadow transition-all duration-500\">\n            {icon}\n          </div>\n          <h3 className=\"mb-2 text-xl font-semibold tracking-tight\">{title}</h3>\n          <p className=\"text-muted-foreground text-sm\">{description}</p>\n        </div>\n        <div className=\"text-primary mt-4 flex items-center text-sm\">\n          <span className=\"mr-1\">Learn more</span>\n          <ArrowRight className=\"size-4 transition-all duration-500 group-hover:translate-x-2\" />\n        </div>\n      </div>\n      <div className=\"from-primary to-primary/30 absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-500 group-hover:blur-lg\" />\n    </motion.div>\n  );\n};\n\nconst items = [\n  {\n    title: 'Developer Experience',\n    description:\n      'Built with developers in mind, making implementation a breeze.',\n    icon: <Code className=\"size-6\" />,\n    size: 'large' as const,\n  },\n  {\n    title: 'Accessibility',\n    description:\n      'Built with a11y best practices to ensure your app is usable by everyone.',\n    icon: <Layers className=\"size-6\" />,\n    size: 'small' as const,\n  },\n  {\n    title: 'Responsive Design',\n    description: 'Create layouts that adapt to any screen size with ease.',\n    icon: <Layers className=\"size-6\" />,\n    size: 'medium' as const,\n  },\n  {\n    title: 'Customizable',\n    description: \"Tailor components to match your brand's unique style.\",\n    icon: <Palette className=\"size-6\" />,\n    size: 'medium' as const,\n  },\n  {\n    title: 'Performance',\n    description: 'Optimized for speed and efficiency across all devices.',\n    icon: <Zap className=\"size-6\" />,\n    size: 'small' as const,\n  },\n  {\n    title: 'Documentation',\n    description:\n      'Comprehensive guides and examples to help you get started quickly.',\n    icon: <FileText className=\"size-6\" />,\n    size: 'large' as const,\n  },\n];\n\nexport default function BentoGrid1() {\n  const containerVariants = {\n    hidden: {},\n    visible: {\n      transition: {\n        staggerChildren: 0.12,\n        delayChildren: 0.1,\n      },\n    },\n  };\n\n  return (\n    <div className=\"mx-auto max-w-6xl px-4 py-12\">\n      <motion.div\n        className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-6\"\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        {items.map((item, i) => (\n          <BentoGridItem\n            key={i}\n            title={item.title}\n            description={item.description}\n            icon={item.icon}\n            size={item.size}\n            className={cn(\n              item.size === 'large'\n                ? 'col-span-4'\n                : item.size === 'medium'\n                  ? 'col-span-3'\n                  : 'col-span-2',\n              'h-full',\n            )}\n          />\n        ))}\n      </motion.div>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/grids/bento-grid-1.tsx", "target": "components/mvpblocks/bento-grid-1.tsx"}]}