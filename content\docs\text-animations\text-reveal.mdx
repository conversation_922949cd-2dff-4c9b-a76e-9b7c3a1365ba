---
title: Text Reveal
description: Text reveal animations create a dynamic and engaging effect as text appears on the screen. This technique can be used to draw attention to specific content or enhance the overall user experience.
root: text-animations
new: true
---

import { ComponentPreview } from "@/components/preview/component-preview";
import { extractSourceCode } from "@/lib/code";
import { ComponentSource } from "@/components/preview/component-source";
import { Tab, Tabs } from "fumadocs-ui/components/tabs";
import { Step, Steps } from "fumadocs-ui/components/steps";
import { TypeTable } from "fumadocs-ui/components/type-table";

## Title Reveal

<ComponentPreview
  name="text-reveal-1"
  classNameComponentContainer="h-[300px]"
  code={(await extractSourceCode("text-reveal-1")).code}
  lang="tsx"
  hasReTrigger={true}
/>

## With custom props

<ComponentPreview
  name="text-reveal-2"
  classNameComponentContainer="h-[300px]"
  code={(await extractSourceCode("text-reveal-2")).code}
  lang="tsx"
  hasReTrigger={true}
/>

<Steps>
  <Step>
    <Tabs items={["npm", "pnpm", "yarn", "bun"]}>
      <Tab>
        ```bash
        npm install motion/react react
        ```
      </Tab>
      <Tab>
      ```bash 
      pnpm install motion/react react
      ```
      </Tab>
      <Tab>
      ```bash 
      yarn add motion/react react
      ```
      </Tab>
      <Tab>
      ```bash 
      bun add motion/react react
      ```
      </Tab>
    </Tabs>
  </Step>
  <Step>
    Gradient Bars
    <ComponentSource
      code={(await extractSourceCode("text-reveal")).code}
      className="bg-fd-secondary/50"
    />
  </Step>
</Steps>

## Props

<TypeTable
  type={{
    children: {
      description: "The text to be revealed.",
      type: "string",
      default: "undefined",
    },
    className: {
      description: "Optional CSS class for customizing the style and appearance of the text.",
      type: "string",
      default: "undefined",
    },
    blur: {
      description: "Initial blur amount in pixels before the animation starts.",
      type: "number",
      default: "10",
    },
    delay: {
      description: "Delay in seconds between revealing each word or letter.",
      type: "number",
      default: "0.1",
    },
    duration: {
      description: "Animation duration in seconds for each word or letter.",
      type: "number",
      default: "1",
    },
    from: {
      description: "Animation direction from which each word or letter enters.",
      type: "'top' | 'bottom'",
      default: "'bottom'",
    },
    split: {
      description: "Whether to animate the text by words or individual letters.",
      type: "'word' | 'letter'",
      default: "'word'",
    },
  }}
/>