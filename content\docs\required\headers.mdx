---
title: Headers
description: Headers are used to define the structure of your document and provide context for the content that follows. They are essential for creating a well-organized and readable document.
root: required
new: true
---

import {ComponentPreview} from "@/components/preview/component-preview";
import {extractSourceCode} from "@/lib/code";

## Header 1

<ComponentPreview
  name="header-1"
  classNameComponentContainer="h-[600px] relative"
  code={(await extractSourceCode('header-1')).code}
  lang="tsx"
  fromDocs={true}
/>

## Header 2

<ComponentPreview
  name="header-2"
  classNameComponentContainer="h-[600px] relative"
  code={(await extractSourceCode('header-2')).code}
  lang="tsx"
  fromDocs={true}
/>