{"name": "typewriter", "type": "registry:ui", "dependencies": ["framer-motion", "react"], "registryDependencies": [], "files": [{"type": "registry:ui", "content": "'use client';\nimport { useEffect } from 'react';\nimport { motion, useMotionValue, useTransform, animate } from 'framer-motion';\n\nexport default function TextGenerateEffect({\n  words,\n  className = '',\n}: {\n  words: string;\n  className?: string;\n}) {\n  const count = useMotionValue(0);\n  const rounded = useTransform(count, (latest) => Math.round(latest));\n  const displayText = useTransform(rounded, (latest) => words.slice(0, latest));\n\n  useEffect(() => {\n    const controls = animate(count, words.length, {\n      type: 'tween',\n      duration: 2.5, // Increased from 1 to 2.5 seconds\n      ease: 'easeInOut',\n    });\n    return controls.stop;\n  }, [words]);\n\n  return <motion.span className={className}>{displayText}</motion.span>;\n}\n", "path": "/components/ui/typewriter.tsx", "target": "components/ui/typewriter.tsx"}]}