{"name": "team-1", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "'use client';\n// changes on line 109\nimport { cn } from '@/lib/utils';\nimport { GithubIcon, LinkedinIcon, TwitterIcon } from 'lucide-react';\nimport Link from 'next/link';\n\ninterface TeamMember {\n  name: string;\n  role: string;\n  bio: string;\n  imageUrl: string;\n  location?: string;\n  socialLinks?: { platform: 'github' | 'twitter' | 'linkedin'; url: string }[];\n}\n\ninterface TeamProps {\n  title?: string;\n  subtitle?: string;\n  members: TeamMember[];\n  className?: string;\n}\n\n// Default data\nconst defaultMembers: TeamMember[] = [\n  {\n    name: '<PERSON>',\n    role: 'Founder & CEO',\n    bio: '10+ years of experience in product development and team leadership. Passionate about building products that make a difference.',\n    imageUrl:\n      'https://images.unsplash.com/photo-1568602471122-7832951cc4c5?q=80&w=300&h=300&auto=format&fit=crop',\n    location: 'San Francisco, CA',\n    socialLinks: [\n      { platform: 'twitter', url: 'https://twitter.com' },\n      { platform: 'github', url: 'https://github.com' },\n      { platform: 'linkedin', url: 'https://linkedin.com' },\n    ],\n  },\n  {\n    name: '<PERSON>',\n    role: 'Lead Designer',\n    bio: 'Award-winning designer with a passion for creating beautiful, functional interfaces that delight users.',\n    imageUrl:\n      'https://images.unsplash.com/photo-1580489944761-15a19d654956?q=80&w=300&h=300&auto=format&fit=crop',\n    location: 'New York, NY',\n    socialLinks: [\n      { platform: 'twitter', url: 'https://twitter.com' },\n      { platform: 'linkedin', url: 'https://linkedin.com' },\n    ],\n  },\n  {\n    name: 'Marcus Johnson',\n    role: 'Senior Developer',\n    bio: 'Full-stack developer with expertise in React, Node.js, and cloud architecture. Building scalable solutions for complex problems.',\n    imageUrl:\n      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=300&h=300&auto=format&fit=crop',\n    location: 'Berlin, Germany',\n    socialLinks: [\n      { platform: 'github', url: 'https://github.com' },\n      { platform: 'linkedin', url: 'https://linkedin.com' },\n    ],\n  },\n  {\n    name: 'Priya Patel',\n    role: 'Product Manager',\n    bio: 'Strategic thinker with a track record of launching successful products that users love and businesses value.',\n    imageUrl:\n      'https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=300&h=300&auto=format&fit=crop',\n    location: 'London, UK',\n    socialLinks: [\n      { platform: 'twitter', url: 'https://twitter.com' },\n      { platform: 'linkedin', url: 'https://linkedin.com' },\n    ],\n  },\n];\n\nexport default function Team1({\n  title = 'Meet Our Team',\n  subtitle = \"We're a diverse group of passionate individuals working together to build amazing products.\",\n  members = defaultMembers,\n  className,\n}: TeamProps) {\n  return (\n    <section className={cn('mx-auto max-w-7xl py-16 md:py-24', className)}>\n      <div className=\"bg-primary/15 absolute top-0 left-0 h-96 w-96 rounded-full blur-3xl\" />\n      <div className=\"bg-primary/15 absolute top-0 right-0 h-96 w-96 rounded-full blur-3xl\" />\n      <div className=\"container px-4 md:px-6\">\n        <div className=\"mx-auto mb-16 max-w-3xl text-center\">\n          <h2 className=\"mb-4 text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl\">\n            {title}\n          </h2>\n          <p className=\"text-muted-foreground mx-auto max-w-2xl md:text-lg\">\n            {subtitle}\n          </p>\n        </div>\n\n        <div className=\"flex flex-wrap items-center justify-center gap-8\">\n          {members.map((member) => (\n            <TeamMemberCard key={member.name} member={member} />\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// Team member card component\nfunction TeamMemberCard({ member }: { member: TeamMember }) {\n  return (\n    <div className=\"group bg-card h-[420px] w-96 overflow-hidden rounded-xl opacity-100 shadow-sm transition-opacity hover:opacity-75\">\n      <div className=\"relative h-[200px] w-full overflow-hidden\">\n        <img\n          src={member.imageUrl}\n          alt={member.name}\n          sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw\"\n          className=\"object-cover object-center transition-transform duration-500 group-hover:scale-105\"\n        />\n      </div>\n\n      <div className=\"flex h-[220px] flex-col p-5\">\n        {member.location && (\n          <div className=\"text-muted-foreground mb-1 flex items-center text-xs\">\n            <div className=\"bg-primary mr-1.5 h-1.5 w-1.5 rounded-full\" />\n            {member.location}\n          </div>\n        )}\n\n        <h3 className=\"mb-1 text-xl font-bold\">{member.name}</h3>\n        <p className=\"text-primary mb-2 text-sm font-medium\">{member.role}</p>\n        <div className=\"mb-4\">\n          <p className=\"text-muted-foreground text-sm\">{member.bio}</p>\n        </div>\n        <div className=\"mt-auto\">\n          {member.socialLinks && (\n            <div className=\"flex space-x-3\">\n              {member.socialLinks.map((link) => (\n                <Link\n                  key={link.platform}\n                  href={link.url}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"bg-muted text-muted-foreground hover:bg-primary hover:text-primary-foreground flex h-8 w-8 items-center justify-center rounded-full transition-all\"\n                >\n                  {link.platform === 'github' && (\n                    <GithubIcon className=\"h-4 w-4\" />\n                  )}\n                  {link.platform === 'twitter' && (\n                    <TwitterIcon className=\"h-4 w-4\" />\n                  )}\n                  {link.platform === 'linkedin' && (\n                    <LinkedinIcon className=\"h-4 w-4\" />\n                  )}\n                </Link>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/team/team-1.tsx", "target": "components/mvpblocks/team-1.tsx"}]}