{"name": "pricing-card", "type": "registry:ui", "dependencies": ["lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/payment-modal.json", "https://blocks.mvp-subha.me/r/dialog.json", "https://blocks.mvp-subha.me/r/label.json", "https://blocks.mvp-subha.me/r/radio-group.json", "https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:ui", "content": "'use client';\n\nimport { useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Check } from 'lucide-react';\nimport { PaymentModal } from './payment-modal';\n\ninterface PricingCardProps {\n  name: string;\n  price: number;\n  period: string;\n  features: string[];\n  featured?: boolean;\n}\n\nexport function PricingCard({\n  name,\n  price,\n  period,\n  features,\n  featured,\n}: PricingCardProps) {\n  const [showPaymentModal, setShowPaymentModal] = useState(false);\n\n  return (\n    <>\n      <div className=\"relative rounded-lg border border-zinc-800 p-6\">\n        {featured && (\n          <div className=\"absolute -top-2 right-4 rounded-full bg-black px-3 py-1 text-sm font-medium text-white dark:bg-white dark:text-black\">\n            Featured\n          </div>\n        )}\n        <div className=\"space-y-6\">\n          <div>\n            <h3 className=\"text-lg font-medium\">{name}</h3>\n            <div className=\"mt-2 flex items-baseline\">\n              <span className=\"text-5xl font-bold tracking-tight\">\n                €{price}\n              </span>\n              <span className=\"ml-1 text-sm font-medium text-zinc-400\">\n                /{period}\n              </span>\n            </div>\n          </div>\n          <Button className=\"w-full\" onClick={() => setShowPaymentModal(true)}>\n            Get {name}\n          </Button>\n          <ul className=\"space-y-3\">\n            {features.map((feature, index) => (\n              <li key={index} className=\"flex items-center gap-2\">\n                <Check className=\"h-4 w-4 text-green-500\" />\n                <span className=\"text-sm text-zinc-500\">{feature}</span>\n              </li>\n            ))}\n          </ul>\n        </div>\n      </div>\n\n      <PaymentModal\n        isOpen={showPaymentModal}\n        onClose={() => setShowPaymentModal(false)}\n        plan={{ name, price, period }}\n      />\n    </>\n  );\n}\n", "path": "/components/ui/pricing-card.tsx", "target": "components/ui/pricing-card.tsx"}]}