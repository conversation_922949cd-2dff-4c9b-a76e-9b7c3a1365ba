{"name": "globe", "type": "registry:ui", "dependencies": ["lucide-react", "cobe"], "files": [{"type": "registry:ui", "content": "'use client';\n\nimport React, { useEffect, useRef, useState } from 'react';\nimport createGlobe from 'cobe';\nimport { cn } from '@/lib/utils';\n\ninterface EarthProps {\n  className?: string;\n  theta?: number;\n  dark?: number;\n  scale?: number;\n  diffuse?: number;\n  mapSamples?: number;\n  mapBrightness?: number;\n  baseColor?: [number, number, number];\n  markerColor?: [number, number, number];\n  glowColor?: [number, number, number];\n}\nconst Earth: React.FC<EarthProps> = ({\n  className,\n  theta = 0.25,\n  dark = 1,\n  scale = 1.1,\n  diffuse = 1.2,\n  mapSamples = 40000,\n  mapBrightness = 6,\n  baseColor = [0.4, 0.6509, 1],\n  markerColor = [1, 0, 0],\n  glowColor = [0.2745, 0.5765, 0.898],\n}) => {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n\n  useEffect(() => {\n    let width = 0;\n    const onResize = () =>\n      canvasRef.current && (width = canvasRef.current.offsetWidth);\n    window.addEventListener('resize', onResize);\n    onResize();\n    let phi = 0;\n\n    onResize();\n    const globe = createGlobe(canvasRef.current!, {\n      devicePixelRatio: 2,\n      width: width * 2,\n      height: width * 2,\n      phi: 0,\n      theta: theta,\n      dark: dark,\n      scale: scale,\n      diffuse: diffuse,\n      mapSamples: mapSamples,\n      mapBrightness: mapBrightness,\n      baseColor: baseColor,\n      markerColor: markerColor,\n      glowColor: glowColor,\n      opacity: 1,\n      offset: [0, 0],\n      markers: [\n        // longitude latitude\n      ],\n      onRender: (state: Record<string, any>) => {\n        // Called on every animation frame.\n        // `state` will be an empty object, return updated params.\\\n        state.phi = phi;\n        phi += 0.003;\n      },\n    });\n\n    return () => {\n      globe.destroy();\n    };\n  }, [dark]);\n\n  return (\n    <div\n      className={cn(\n        'z-[10] mx-auto flex w-full max-w-[350px] items-center justify-center',\n        className,\n      )}\n    >\n      <canvas\n        ref={canvasRef}\n        style={{\n          width: '100%',\n          height: '100%',\n          maxWidth: '100%',\n          aspectRatio: '1',\n        }}\n      />\n    </div>\n  );\n};\n\nexport default Earth;\n", "path": "/components/ui/globe.tsx", "target": "components/ui/globe.tsx"}]}