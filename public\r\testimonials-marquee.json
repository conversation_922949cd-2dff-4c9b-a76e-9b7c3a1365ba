{"name": "testimonials-marquee", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/marquee.json"], "files": [{"type": "registry:block", "content": "'use client';\n\nimport { cn } from '@/lib/utils';\nimport { motion } from 'framer-motion';\nimport { Star } from 'lucide-react';\nimport { Marquee } from '@/components/ui/marquee';\n\nexport function Highlight({\n  children,\n  className,\n}: {\n  children: React.ReactNode;\n  className?: string;\n}) {\n  return (\n    <span\n      className={cn(\n        'bg-blue-500/10 p-1 py-0.5 font-bold text-blue-500',\n        className,\n      )}\n    >\n      {children}\n    </span>\n  );\n}\n\nexport interface TestimonialCardProps {\n  name: string;\n  role: string;\n  img?: string;\n  description: React.ReactNode;\n  className?: string;\n  [key: string]: any;\n}\n\nexport function TestimonialCard({\n  description,\n  name,\n  img,\n  role,\n  className,\n  ...props // Capture the rest of the props\n}: TestimonialCardProps) {\n  return (\n    <div\n      className={cn(\n        'mb-4 flex w-full cursor-pointer break-inside-avoid flex-col items-center justify-between gap-6 rounded-xl p-4',\n        // theme styles\n        'border-border bg-card/50 border shadow-sm',\n        // hover effect\n        'transition-all duration-300 hover:-translate-y-0.5 hover:shadow-md',\n        className,\n      )}\n      {...props}\n    >\n      <div className=\"text-muted-foreground text-sm font-normal select-none\">\n        {description}\n        <div className=\"flex flex-row py-1\">\n          <Star className=\"size-4 fill-blue-500 text-blue-500\" />\n          <Star className=\"size-4 fill-blue-500 text-blue-500\" />\n          <Star className=\"size-4 fill-blue-500 text-blue-500\" />\n          <Star className=\"size-4 fill-blue-500 text-blue-500\" />\n          <Star className=\"size-4 fill-blue-500 text-blue-500\" />\n        </div>\n      </div>\n\n      <div className=\"flex w-full items-center justify-start gap-5 select-none\">\n        <img\n          width={40}\n          height={40}\n          src={img || ''}\n          alt={name}\n          className=\"size-10 rounded-full ring-1 ring-blue-500/20 ring-offset-2\"\n        />\n\n        <div>\n          <p className=\"text-foreground font-medium\">{name}</p>\n          <p className=\"text-muted-foreground text-xs font-normal\">{role}</p>\n        </div>\n      </div>\n    </div>\n  );\n}\nconst testimonials = [\n  {\n    name: 'Jordan Hayes',\n    role: 'CTO at Quantum Innovations',\n    img: 'https://randomuser.me/api/portraits/men/22.jpg',\n    description: (\n      <p>\n        NexaUI has completely transformed our development workflow.\n        <Highlight>\n          The component system saved us weeks of custom coding and design work.\n        </Highlight>{' '}\n        Our team can now focus on business logic instead of UI details.\n      </p>\n    ),\n  },\n  {\n    name: 'Maya Rodriguez',\n    role: 'Lead Developer at Skyline Digital',\n    img: 'https://randomuser.me/api/portraits/women/33.jpg',\n    description: (\n      <p>\n        I was skeptical at first, but NexaUI proved me wrong.\n        <Highlight>\n          The accessibility features and responsive design are top-notch.\n        </Highlight>{' '}\n        It&apos;s rare to find a framework that prioritizes both aesthetics and\n        functionality.\n      </p>\n    ),\n  },\n  {\n    name: 'Ethan Park',\n    role: 'Startup Founder at Elevate Labs',\n    img: 'https://randomuser.me/api/portraits/men/32.jpg',\n    description: (\n      <p>\n        As a non-technical founder, NexaUI has been a game-changer for our MVP.\n        <Highlight>We launched three months ahead of schedule.</Highlight> The\n        modular components allowed us to iterate quickly based on user feedback.\n      </p>\n    ),\n  },\n  {\n    name: 'Zoe Bennett',\n    role: 'UX Architect at Fusion Systems',\n    img: 'https://randomuser.me/api/portraits/women/44.jpg',\n    description: (\n      <p>\n        NexaUI&apos;s attention to detail is impressive.\n        <Highlight>\n          The micro-interactions and animations create a polished experience.\n        </Highlight>{' '}\n        It&apos;s become our go-to solution for client projects with tight\n        deadlines.\n      </p>\n    ),\n  },\n  {\n    name: 'Victor Nguyen',\n    role: 'Product Lead at FinEdge',\n    img: 'https://randomuser.me/api/portraits/men/55.jpg',\n    description: (\n      <p>\n        Our financial dashboard needed a complete overhaul, and NexaUI\n        delivered.\n        <Highlight>\n          The data visualization components are both beautiful and functional.\n        </Highlight>{' '}\n        User engagement has increased by 47% since the redesign.\n      </p>\n    ),\n  },\n  {\n    name: 'Amara Johnson',\n    role: 'Frontend Specialist at Nimbus Tech',\n    img: 'https://randomuser.me/api/portraits/women/67.jpg',\n    description: (\n      <p>\n        The documentation for NexaUI is exceptional.\n        <Highlight>\n          I was able to implement complex UI patterns in just a few hours.\n        </Highlight>{' '}\n        The TypeScript support is also a major productivity booster.\n      </p>\n    ),\n  },\n  {\n    name: 'Leo Tanaka',\n    role: 'Creative Technologist at Prism Agency',\n    img: 'https://randomuser.me/api/portraits/men/78.jpg',\n    description: (\n      <p>\n        NexaUI has the perfect balance of flexibility and structure.\n        <Highlight>\n          We can maintain brand consistency while still creating unique\n          experiences.\n        </Highlight>{' '}\n        Our clients are consistently impressed with the results.\n      </p>\n    ),\n  },\n  {\n    name: 'Sophia Martinez',\n    role: 'E-commerce Director at StyleHub',\n    img: 'https://randomuser.me/api/portraits/women/89.jpg',\n    description: (\n      <p>\n        Our conversion rates have increased by 28% since implementing NexaUI.\n        <Highlight>\n          The checkout flow components are optimized for both desktop and\n          mobile.\n        </Highlight>{' '}\n        The dark mode support was also a huge hit with our customers.\n      </p>\n    ),\n  },\n  {\n    name: 'Aiden Wilson',\n    role: 'Healthcare Solutions Architect',\n    img: 'https://randomuser.me/api/portraits/men/92.jpg',\n    description: (\n      <p>\n        NexaUI&apos;s accessibility features were crucial for our healthcare\n        platform.\n        <Highlight>\n          We passed compliance requirements with minimal additional work.\n        </Highlight>{' '}\n        The form components are especially well-designed for complex data entry.\n      </p>\n    ),\n  },\n  {\n    name: 'Olivia Chen',\n    role: 'EdTech Product Manager at LearnSphere',\n    img: 'https://randomuser.me/api/portraits/women/29.jpg',\n    description: (\n      <p>\n        Our educational platform needed to work for students of all ages and\n        abilities.\n        <Highlight>\n          NexaUI&apos;s inclusive design principles made this possible without\n          compromise.\n        </Highlight>{' '}\n        The interactive components have significantly improved student\n        engagement.\n      </p>\n    ),\n  },\n];\n\nexport default function Testimonials() {\n  return (\n    <section className=\"relative container py-10\">\n      {/* Decorative elements */}\n      <div className=\"absolute top-20 -left-20 z-10 h-64 w-64 rounded-full bg-blue-500/5 blur-3xl\" />\n      <div className=\"absolute -right-20 bottom-20 z-10 h-64 w-64 rounded-full bg-blue-500/5 blur-3xl\" />\n\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5 }}\n      >\n        <h2 className=\"text-foreground mb-4 text-center text-4xl leading-[1.2] font-bold tracking-tighter md:text-5xl\">\n          What Our Users Are Saying\n        </h2>\n        <h3 className=\"text-muted-foreground mx-auto mb-8 max-w-lg text-center text-lg font-medium tracking-tight text-balance\">\n          Don&apos;t just take our word for it. Here&apos;s what{' '}\n          <span className=\"bg-gradient-to-r from-blue-500 to-sky-500 bg-clip-text text-transparent\">\n            real developers\n          </span>{' '}\n          are saying about{' '}\n          <span className=\"font-semibold text-blue-500\">NexaUI</span>\n        </h3>\n      </motion.div>\n\n      <div className=\"relative mt-6 max-h-screen overflow-hidden\">\n        <div className=\"gap-4 md:columns-2 xl:columns-3 2xl:columns-4\">\n          {Array(Math.ceil(testimonials.length / 3))\n            .fill(0)\n            .map((_, i) => (\n              <Marquee\n                vertical\n                key={i}\n                className={cn({\n                  '[--duration:60s]': i === 1,\n                  '[--duration:30s]': i === 2,\n                  '[--duration:70s]': i === 3,\n                })}\n              >\n                {testimonials.slice(i * 3, (i + 1) * 3).map((card, idx) => (\n                  <motion.div\n                    key={idx}\n                    initial={{ opacity: 0 }}\n                    whileInView={{ opacity: 1 }}\n                    viewport={{ once: true }}\n                    transition={{\n                      delay: Math.random() * 0.8,\n                      duration: 1.2,\n                    }}\n                  >\n                    <TestimonialCard {...card} />\n                  </motion.div>\n                ))}\n              </Marquee>\n            ))}\n        </div>\n        <div className=\"from-background pointer-events-none absolute inset-x-0 bottom-0 h-1/4 w-full bg-gradient-to-t from-20%\"></div>\n        <div className=\"from-background pointer-events-none absolute inset-x-0 top-0 h-1/4 w-full bg-gradient-to-b from-20%\"></div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/testimonials/testimonials-marquee.tsx", "target": "components/mvpblocks/testimonials-marquee.tsx"}]}