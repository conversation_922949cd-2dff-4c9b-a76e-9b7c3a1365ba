{"name": "signin-modal", "type": "registry:block", "dependencies": ["react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/checkbox.json", "https://blocks.mvp-subha.me/r/dialog.json", "https://blocks.mvp-subha.me/r/input.json", "https://blocks.mvp-subha.me/r/label.json"], "files": [{"type": "registry:block", "content": "import { useId } from 'react';\n\nimport { Button } from '@/components/ui/button';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n  DialogTrigger,\n} from '@/components/ui/dialog';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\n\nexport default function SigninModal() {\n  const id = useId();\n  return (\n    <Dialog>\n      <DialogTrigger asChild>\n        <Button variant=\"secondary\">Sign in</Button>\n      </DialogTrigger>\n      <DialogContent>\n        <div className=\"flex flex-col items-center gap-2\">\n          <div\n            className=\"flex size-11 shrink-0 items-center justify-center rounded-full border\"\n            aria-hidden=\"true\"\n          >\n            <img src=\"/logo.webp\" alt=\"logo\" className=\"h-8 w-8 rounded-full\" />\n          </div>\n          <DialogHeader>\n            <DialogTitle className=\"sm:text-center\">Welcome back</DialogTitle>\n            <DialogDescription className=\"sm:text-center\">\n              Enter your credentials to login to your account.\n            </DialogDescription>\n          </DialogHeader>\n        </div>\n\n        <form className=\"space-y-5\">\n          <div className=\"space-y-4\">\n            <div className=\"*:not-first:mt-2\">\n              <Label htmlFor={`${id}-email`}>Email</Label>\n              <Input\n                id={`${id}-email`}\n                placeholder=\"<EMAIL>\"\n                type=\"email\"\n                required\n              />\n            </div>\n            <div className=\"*:not-first:mt-2\">\n              <Label htmlFor={`${id}-password`}>Password</Label>\n              <Input\n                id={`${id}-password`}\n                placeholder=\"Enter your password\"\n                type=\"password\"\n                required\n              />\n            </div>\n          </div>\n          <div className=\"flex justify-between gap-2\">\n            <div className=\"flex items-center gap-2\">\n              <Checkbox id={`${id}-remember`} />\n              <Label\n                htmlFor={`${id}-remember`}\n                className=\"text-muted-foreground font-normal\"\n              >\n                Remember me\n              </Label>\n            </div>\n            <a className=\"text-sm underline hover:no-underline\" href=\"#\">\n              Forgot password?\n            </a>\n          </div>\n          <Button type=\"button\" className=\"w-full\">\n            Sign in\n          </Button>\n        </form>\n\n        <div className=\"before:bg-border after:bg-border flex items-center gap-3 before:h-px before:flex-1 after:h-px after:flex-1\">\n          <span className=\"text-muted-foreground text-xs\">Or</span>\n        </div>\n\n        <Button variant=\"outline\">Login with Google</Button>\n      </DialogContent>\n    </Dialog>\n  );\n}\n", "path": "/components/mvpblocks/basics/modals/signin-modal.tsx", "target": "components/mvpblocks/signin-modal.tsx"}]}