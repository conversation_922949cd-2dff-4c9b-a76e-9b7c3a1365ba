{"name": "mvpblocks", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "fumadocs-mdx", "build:registry": "bun run ./scripts/build-registry.ts", "format": "prettier \"**/*.{js,jsx,ts,tsx}\" --write", "sync": "git pull && bun i && bun run build:registry && bun run dev", "count": "bun run generate-loc.ts", "register": "bun run ./scripts/auto-register.ts", "add-component": "powershell -File ./scripts/register-component.ps1", "update-dependencies": "bun run ./scripts/update-dependencies.ts", "optimise-image": "bun run ./scripts/convert-to-webp.js"}, "dependencies": {"@ai-sdk/groq": "^1.2.9", "@hookform/resolvers": "^5.1.1", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@number-flow/react": "^0.5.10", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "@types/canvas-confetti": "^1.9.0", "@types/hsl-to-hex": "^1.0.2", "@types/mdx": "^2.0.13", "@vercel/speed-insights": "^1.2.0", "ai": "^4.3.19", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cobe": "^0.6.4", "critters": "^0.0.25", "date-fns": "^4.1.0", "dotted-map": "^2.2.3", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.6", "fumadocs-core": "^15.6.4", "fumadocs-docgen": "^2.1.0", "fumadocs-mdx": "^11.6.11", "fumadocs-twoslash": "^3.1.4", "fumadocs-typescript": "^4.0.6", "fumadocs-ui": "^15.6.4", "hsl-to-hex": "^1.0.0", "input-otp": "^1.4.2", "lenis": "^1.3.7", "lucide-react": "^0.534.0", "marked": "^16.1.0", "mini-svg-data-uri": "^1.4.4", "mongoose": "^8.16.4", "motion": "^12.23.6", "next": "15.4.1", "next-themes": "^0.4.6", "razorpay": "^2.9.6", "react": "^19.1.0", "react-day-picker": "9.8.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.3", "recharts": "2.15.4", "resend": "^4.7.0", "sonner": "^2.0.6", "swiper": "^11.2.10", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tsparticles-confetti": "^2.12.0", "vaul": "^1.1.2", "verifymailjs": "^1.1.5", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@types/node": "^24.0.14", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-config-next": "15.4.1", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "sharp": "^0.34.3", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}}