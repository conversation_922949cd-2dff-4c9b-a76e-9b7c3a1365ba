---
title: Masonry Grids
description: Masonry grids are a popular layout style that allows items to be arranged in a way that optimizes space and creates a visually appealing design. They are commonly used in image galleries, portfolios, and other content-heavy applications.
root: grids
new: true
---

import { ComponentPreview } from "@/components/preview/component-preview";
import { extractSourceCode } from "@/lib/code";

<ComponentPreview
  name="masonry-grid-1"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode("masonry-grid-1")).code}
  lang="tsx"
/>