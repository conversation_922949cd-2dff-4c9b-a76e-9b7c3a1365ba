{"name": "pricing-with-modals", "type": "registry:block", "dependencies": [], "registryDependencies": ["https://blocks.mvp-subha.me/r/pricing-card.json", "https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/payment-modal.json", "https://blocks.mvp-subha.me/r/dialog.json", "https://blocks.mvp-subha.me/r/radio-group.json", "https://blocks.mvp-subha.me/r/label.json"], "files": [{"type": "registry:block", "content": "import { PricingCard } from '@/components/ui/pricing-card';\n\nconst plans = [\n  {\n    name: 'Starter',\n    price: 15,\n    period: 'month',\n    features: [\n      'Up to 10,000 data points per month',\n      'Email support',\n      'Community forum access',\n      'Cancel anytime',\n    ],\n  },\n  {\n    name: 'Pro',\n    price: 40,\n    period: 'quarter',\n    featured: true,\n    features: [\n      'Advanced analytics dashboard',\n      'Customizable reports and charts',\n      'Real-time data tracking',\n      'Integration with third-party tools',\n      'Everything in Hobby Plan',\n    ],\n  },\n  {\n    name: 'Premium',\n    price: 120,\n    period: 'year',\n    features: [\n      'Unlimited data storage',\n      'Customizable dashboards',\n      'Advanced data segmentation',\n      'Real-time data processing',\n      'AI-powered insights and recommendations',\n      'Everything in Hobby Plan',\n      'Everything in Pro Plan',\n    ],\n  },\n];\n\nexport default function PricingPage() {\n  return (\n    <div className=\"relative min-h-screen w-full px-4 py-20\">\n      <div className=\"absolute inset-0 -z-10 overflow-hidden\">\n        <div className=\"bg-primary/5 absolute -top-[10%] -right-[10%] h-[40%] w-[40%] rounded-full blur-3xl\" />\n        <div className=\"bg-primary/5 absolute -top-[10%] -left-[10%] h-[40%] w-[40%] rounded-full blur-3xl\" />\n      </div>\n      <div className=\"mx-auto max-w-6xl space-y-12\">\n        <div className=\"space-y-4 text-center\">\n          <h1 className=\"text-4xl font-bold\">\n            Simple pricing for advanced people\n          </h1>\n          <p className=\"mx-auto max-w-2xl text-gray-400\">\n            Our pricing is designed for advanced people who need more features\n            and more flexibility.\n          </p>\n        </div>\n\n        <div className=\"grid gap-8 md:grid-cols-3\">\n          {plans.map((plan) => (\n            <PricingCard key={plan.name} {...plan} />\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/pricing/pricing-with-modals.tsx", "target": "components/mvpblocks/pricing-with-modals.tsx"}]}