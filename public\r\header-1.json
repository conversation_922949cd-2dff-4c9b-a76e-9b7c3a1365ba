{"name": "header-1", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "next-themes", "react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Menu, X, ChevronDown, ArrowRight, Sparkles } from 'lucide-react';\nimport Link from 'next/link';\nimport { useTheme } from 'next-themes';\n\ninterface NavItem {\n  name: string;\n  href: string;\n  hasDropdown?: boolean;\n  dropdownItems?: { name: string; href: string; description?: string }[];\n}\n\nconst navItems: NavItem[] = [\n  { name: 'Home', href: '/' },\n  { name: 'Features', href: '/features' },\n  {\n    name: 'Products',\n    href: '/products',\n    hasDropdown: true,\n    dropdownItems: [\n      {\n        name: 'Analytics',\n        href: '/analytics',\n        description: 'Track your metrics',\n      },\n      {\n        name: 'Dashboard',\n        href: '/dashboard',\n        description: 'Manage your data',\n      },\n      { name: 'Reports', href: '/reports', description: 'Generate insights' },\n    ],\n  },\n  { name: 'Pricing', href: '/pricing' },\n  { name: 'About', href: '/about' },\n];\n\nexport default function Header1() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);\n  const { theme } = useTheme();\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 20);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const headerVariants = {\n    initial: { y: -100, opacity: 0 },\n    animate: { y: 0, opacity: 1 },\n    scrolled: {\n      backdropFilter: 'blur(20px)',\n      backgroundColor:\n        theme === 'dark' ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.8)',\n      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',\n    },\n  };\n\n  const mobileMenuVariants = {\n    closed: { opacity: 0, height: 0 },\n    open: { opacity: 1, height: 'auto' },\n  };\n\n  const dropdownVariants = {\n    hidden: { opacity: 0, y: -10, scale: 0.95 },\n    visible: { opacity: 1, y: 0, scale: 1 },\n  };\n\n  return (\n    <motion.header\n      className=\"fixed top-0 right-0 left-0 z-50 transition-all duration-300\"\n      variants={headerVariants}\n      initial=\"initial\"\n      animate={isScrolled ? 'scrolled' : 'animate'}\n      transition={{ duration: 0.3, ease: 'easeInOut' }}\n      style={{\n        backdropFilter: isScrolled ? 'blur(20px)' : 'none',\n        backgroundColor: isScrolled\n          ? theme === 'dark'\n            ? 'rgba(0, 0, 0, 0.8)'\n            : 'rgba(255, 255, 255, 0.8)'\n          : 'transparent',\n        boxShadow: isScrolled ? '0 8px 32px rgba(0, 0, 0, 0.1)' : 'none',\n      }}\n    >\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between lg:h-20\">\n          <motion.div\n            className=\"flex items-center space-x-2\"\n            whileHover={{ scale: 1.05 }}\n            transition={{ type: 'spring', stiffness: 400, damping: 10 }}\n          >\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-rose-500 to-rose-700\">\n                <Sparkles className=\"h-5 w-5 text-white\" />\n              </div>\n              <span className=\"bg-gradient-to-r from-rose-500 to-rose-700 bg-clip-text text-xl font-bold text-transparent\">\n                Acme Inc.\n              </span>\n            </Link>\n          </motion.div>\n\n          <nav className=\"hidden items-center space-x-8 lg:flex\">\n            {navItems.map((item) => (\n              <div\n                key={item.name}\n                className=\"relative\"\n                onMouseEnter={() =>\n                  item.hasDropdown && setActiveDropdown(item.name)\n                }\n                onMouseLeave={() => setActiveDropdown(null)}\n              >\n                <Link\n                  href={item.href}\n                  className=\"text-foreground flex items-center space-x-1 font-medium transition-colors duration-200 hover:text-rose-500\"\n                >\n                  <span>{item.name}</span>\n                  {item.hasDropdown && (\n                    <ChevronDown className=\"h-4 w-4 transition-transform duration-200\" />\n                  )}\n                </Link>\n\n                {item.hasDropdown && (\n                  <AnimatePresence>\n                    {activeDropdown === item.name && (\n                      <motion.div\n                        className=\"border-border bg-background/95 absolute top-full left-0 mt-2 w-64 overflow-hidden rounded-xl border shadow-xl backdrop-blur-lg\"\n                        variants={dropdownVariants}\n                        initial=\"hidden\"\n                        animate=\"visible\"\n                        exit=\"hidden\"\n                        transition={{ duration: 0.2 }}\n                      >\n                        {item.dropdownItems?.map((dropdownItem) => (\n                          <Link\n                            key={dropdownItem.name}\n                            href={dropdownItem.href}\n                            className=\"hover:bg-muted block px-4 py-3 transition-colors duration-200\"\n                          >\n                            <div className=\"text-foreground font-medium\">\n                              {dropdownItem.name}\n                            </div>\n                            {dropdownItem.description && (\n                              <div className=\"text-muted-foreground text-sm\">\n                                {dropdownItem.description}\n                              </div>\n                            )}\n                          </Link>\n                        ))}\n                      </motion.div>\n                    )}\n                  </AnimatePresence>\n                )}\n              </div>\n            ))}\n          </nav>\n\n          <div className=\"hidden items-center space-x-4 lg:flex\">\n            <Link\n              href=\"/login\"\n              className=\"text-foreground font-medium transition-colors duration-200 hover:text-rose-500\"\n            >\n              Sign In\n            </Link>\n            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n              <Link\n                href=\"/signup\"\n                className=\"inline-flex items-center space-x-2 rounded-full bg-gradient-to-r from-rose-500 to-rose-700 px-6 py-2.5 font-medium text-white transition-all duration-200 hover:shadow-lg\"\n              >\n                <span>Get Started</span>\n                <ArrowRight className=\"h-4 w-4\" />\n              </Link>\n            </motion.div>\n          </div>\n\n          <motion.button\n            className=\"hover:bg-muted rounded-lg p-2 transition-colors duration-200 lg:hidden\"\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            whileTap={{ scale: 0.95 }}\n          >\n            {isMobileMenuOpen ? (\n              <X className=\"h-6 w-6\" />\n            ) : (\n              <Menu className=\"h-6 w-6\" />\n            )}\n          </motion.button>\n        </div>\n\n        <AnimatePresence>\n          {isMobileMenuOpen && (\n            <motion.div\n              className=\"overflow-hidden lg:hidden\"\n              variants={mobileMenuVariants}\n              initial=\"closed\"\n              animate=\"open\"\n              exit=\"closed\"\n              transition={{ duration: 0.3, ease: 'easeInOut' }}\n            >\n              <div className=\"border-border bg-background/95 mt-4 space-y-2 rounded-xl border py-4 shadow-xl backdrop-blur-lg\">\n                {navItems.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"text-foreground hover:bg-muted block px-4 py-3 font-medium transition-colors duration-200\"\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    {item.name}\n                  </Link>\n                ))}\n                <div className=\"space-y-2 px-4 py-2\">\n                  <Link\n                    href=\"/login\"\n                    className=\"text-foreground hover:bg-muted block w-full rounded-lg py-2.5 text-center font-medium transition-colors duration-200\"\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    Sign In\n                  </Link>\n                  <Link\n                    href=\"/signup\"\n                    className=\"block w-full rounded-lg bg-gradient-to-r from-rose-500 to-rose-700 py-2.5 text-center font-medium text-white transition-all duration-200 hover:shadow-lg\"\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    Get Started\n                  </Link>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.header>\n  );\n}\n", "path": "/components/mvpblocks/required/headers/header-1.tsx", "target": "components/mvpblocks/header-1.tsx"}]}